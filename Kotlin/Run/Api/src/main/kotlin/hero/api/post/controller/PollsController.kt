package hero.api.post.controller

import hero.api.post.controller.dto.CastVotesRequest
import hero.api.post.controller.dto.exampleCastVotesRequest
import hero.api.post.controller.dto.examplePollResponse
import hero.api.post.controller.dto.exampleUpdatePollRequest
import hero.api.post.controller.dto.toResponse
import hero.api.post.service.CastVotesCommand
import hero.api.post.service.EndPoll
import hero.api.post.service.GetPoll
import hero.api.post.service.PollCommandService
import hero.api.post.service.PollQueryService
import hero.api.post.service.Vote
import hero.http4k.auth.getJwtUser
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.http4k.extensions.put
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path
import org.http4k.lens.string

class PollsController(
    private val pollQueryService: PollQueryService,
    private val pollCommandService: PollCommandService,
) {
    @Suppress("unused")
    val routeGetPoll: ContractRoute =
        ("/v1/polls" / Path.string().of("pollId")).get(
            summary = "Get poll by Id.",
            tag = "Polls",
            parameters = object {},
            responses = listOf(Status.OK example examplePollResponse),
            handler = { request, _, pollId ->
                val userId = request.getJwtUser().id

                val result = pollQueryService.execute(GetPoll(userId, pollId))

                Response(Status.OK).body(result.toResponse())
            },
        )

    @Suppress("unused")
    val routePostVotes: ContractRoute =
        ("/v1/polls" / Path.string().of("pollId") / "votes").post(
            summary = "Cast votes",
            tag = "Polls",
            parameters = object {},
            responses = listOf(Status.OK example examplePollResponse),
            receiving = exampleCastVotesRequest,
            handler = { request, _, pollId, _ ->
                val userId = request.getJwtUser().id
                val body = lens<CastVotesRequest>(request)

                pollCommandService.execute(CastVotesCommand(userId, pollId, body.votes.map { Vote(it.optionId) }))

                Response(Status.OK)
            },
        )

    @Suppress("unused")
    val routeUpdatePoll: ContractRoute =
        ("/v1/polls" / Path.string().of("pollId")).put(
            summary = "Update poll",
            tag = "Polls",
            parameters = object {},
            responses = listOf(Status.NO_CONTENT to Unit),
            receiving = exampleUpdatePollRequest,
            handler = { request, _, pollId ->
                val userId = request.getJwtUser().id

                val result = pollCommandService.execute(EndPoll(userId, pollId))

                Response(Status.NO_CONTENT)
            },
        )
}
