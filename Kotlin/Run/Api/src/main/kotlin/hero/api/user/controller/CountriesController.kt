package hero.api.user.controller

import com.github.kittinunf.fuel.httpGet
import hero.baseutils.FuelException
import hero.baseutils.fetch
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.model.CountryDto
import hero.model.CountryDtoAttributes
import hero.model.CountryNameDto
import hero.model.SearchCountryDtoV2Response
import org.http4k.contract.ContractRoute
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Query
import org.http4k.lens.string
import java.util.Locale

class CountriesController {
    @Suppress("unused")
    val routeSearchCountries: ContractRoute =
        "/v1/countries".get(
            summary = "Search countries",
            parameters = object {},
            responses = listOf(
                Status.OK example SearchCountryDtoV2Response(
                    listOf(
                        CountryDto(
                            "CZ",
                            CountryDtoAttributes(
                                mapOf(
                                    "cs" to CountryNameDto("<PERSON><PERSON><PERSON>"),
                                    "en" to CountryNameDto("Czechia"),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
            tag = "Countries",
            handler = ::searchCountriesHandler,
        )
}

private fun searchCountriesHandler(req: Request): Response {
    val searchQuery = countrySearchQuery(req)
    val fieldsQuery = "name,cca2,translations"
    val url = if (searchQuery != null) {
        "$REST_COUNTRIES_BASE_URL/v3.1/translation/$searchQuery"
    } else {
        "$REST_COUNTRIES_BASE_URL/v3.1/all"
    }

    val response: List<RestCountriesResponse>
    try {
        response = url
            .httpGet(listOf("fields" to fieldsQuery))
            .fetch<List<RestCountriesResponse>>()
    } catch (fe: FuelException) {
        return Response(Status.OK).body(SearchCountryDtoV2Response(listOf()))
    }

    val results = response.map { country ->
        val translations: Map<String, CountryNameDto> = country.translations.entries
            .associate { entry ->
                (localesByIsoLanguage[entry.key]?.language ?: "undefined") to CountryNameDto(entry.value.common)
            }
            .filterKeys { it != "undefined" }
            .let {
                it + mapOf("en" to CountryNameDto(country.name.common))
            }
        CountryDto(
            country.cca2,
            CountryDtoAttributes(
                translations,
            ),
        )
    }

    return Response(Status.OK).body(SearchCountryDtoV2Response(results))
}

// https://restcountries.com is not stable
private const val REST_COUNTRIES_BASE_URL = "https://restcountries-y7fnkx4lwq-ew.a.run.app"

private val countrySearchQuery = Query.string().optional("query")

private val localesByIsoLanguage: Map<String, Locale> = Locale.getISOLanguages()
    .map { Locale(it) }
    .associateBy { it.isO3Language }

private data class RestCountriesResponse(
    val cca2: String,
    val name: RestCountriesTranslationsResponse,
    val translations: Map<String, RestCountriesTranslationsResponse>,
)

private data class RestCountriesTranslationsResponse(
    val official: String,
    val common: String,
)
