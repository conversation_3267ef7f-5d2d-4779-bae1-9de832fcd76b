package hero.api.scripts.post

import hero.baseutils.SystemEnv
import hero.gcloud.CellHorizontalAlignment
import hero.gcloud.Format
import hero.gcloud.appendFormatted
import hero.gcloud.batched
import hero.gcloud.createSpreadsheet
import hero.gcloud.date
import hero.gcloud.firestore
import hero.gcloud.hyperlink
import hero.gcloud.resizeColumns
import hero.gcloud.string
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.gcloud.writeHeader
import hero.model.Category
import hero.model.topics.PostState
import hero.repository.post.PostRepository
import hero.repository.post.PostType
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import java.time.Duration
import java.util.UUID

fun main() {
    val creatorId = "zfgfgwfigygu"
    val context = JooqSQL.context(ConnectorConnectionPool.dataSource)
    val firestore = firestore(SystemEnv.cloudProject, SystemEnv.isProduction)
    val categoriesCollection = firestore.typedCollectionOf(Category)

    val categories = categoriesCollection.where(Category::userId).isEqualTo(creatorId).fetchAll()
        .associateBy { it.id }
    exportPostDetails(
        creatorId,
        categories,
        PostRepository(context),
    )
}

private fun exportPostDetails(
    creatorId: String,
    categories: Map<String, Category>,
    postRepository: PostRepository,
) {
    val postsData = postRepository
        .find {
            this
                .where(Tables.POST.USER_ID.eq(creatorId))
                .and(Tables.POST.STATE.eq(PostState.PUBLISHED.name))
                .and(Tables.POST.TYPE.eq(PostType.CONTENT_POST.name))
                .orderBy(Tables.POST.PUBLISHED_AT.desc())
        }
        .map {
            val episodeLength = it.assets
                .firstNotNullOfOrNull { asset -> asset.gjirafa }
                ?.duration

            listOf(
                hyperlink("https://herohero.co/mmf/post/${it.id}", it.id),
                date(it.published),
                string(formatSecondsToTime(episodeLength), Format(horizontalAlignment = CellHorizontalAlignment.RIGHT)),
                string(it.text, Format(wrapStrategy = Format.Wrapping.WRAP)),
                string(
                    it.categories.joinToString("\n") { cat -> categories.getValue(cat).name },
                    Format(wrapStrategy = Format.Wrapping.OVERFLOW_CELL),
                ),
            )
        }

    val spreadsheet = createSpreadsheet("$creatorId-${UUID.randomUUID()}", "0ANKHV3uKPZm2Uk9PVA")

    spreadsheet
        .batched()
        .writeHeader(
            listOf(
                string("Link"),
                string("Published at"),
                string("Length"),
                string("Description"),
                string("Categories"),
            ),
        )
        .appendFormatted(postsData)
        .resizeColumns(1, 1, 250)
        .resizeColumns(2, 1, 150)
        .resizeColumns(3, 1, 100)
        .resizeColumns(4, 1, 300)
        .resizeColumns(5, 1, 130)
        .execute()
}

fun formatSecondsToTime(seconds: Double?): String {
    if (seconds == null) return ""
    val duration = Duration.ofSeconds(seconds.toLong())
    return String.format(
        null,
        "%02d:%02d:%02d",
        duration.toHours(),
        duration.toMinutesPart(),
        duration.toSecondsPart(),
    )
}
