package hero.api.payment.controller

import com.stripe.exception.SignatureVerificationException
import com.stripe.net.Webhook
import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.PubSub
import hero.http4k.extensions.enum
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.jackson.toJson
import hero.model.Currency
import hero.model.topics.StripeChargeReceived
import hero.stripe.model.StripeEventDataObject
import hero.stripe.model.StripeEventRequest
import hero.stripe.model.StripeWebhookSecrets
import org.http4k.contract.ContractRoute
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Query

/**
 * Use stripe-cli for testing:
 * - https://github.com/stripe/stripe-node/blob/8f3c3ec49634e509f90cb4fd55aedf5dc0a4dfe3/examples/webhook-signing/README.md#setup
 * - https://docs.stripe.com/stripe-cli/triggers
 * TODO move webhooks to separate service
 */
class StripeWebhookController(
    private val pubSub: PubSub,
    private val webhookSecrets: Map<Currency, StripeWebhookSecrets>,
) {
    private val currencyQuery = Query.enum<Currency>()
        .required("currency", "Currency to decide which Stripe account to handle.")

    @Suppress("unused")
    val routeStripeWebhookCharges: ContractRoute =
        "/v1/stripe/webhooks/charges".post(
            summary = "Handle Stripe charge events.",
            tag = "Stripe webhooks",
            parameters = object {},
            receiving = null,
            responses = listOf(Status.NO_CONTENT example Unit),
            hideFromOpenApi = true,
            handler = { request, _ ->
                val currency = currencyQuery(request)
                validatePayload(request, webhookSecrets[currency]!!.charges)
                val body = lens<StripeEventRequest<StripeEventDataObject>>(request)
                log.info("Stripe webhooks us for charges: ${body.toJson()}")
                val payload = body.eventData?.payload
                    ?: throw BadRequestException("Stripe event payload was null.")
                if (payload.objectType != "charge") {
                    throw BadRequestException("Stripe event was not for `charge`: ${payload.objectType}")
                }
                val chargeId = payload.objectId
                    ?: throw BadRequestException("Field objectId was not given.")
                pubSub.publish(StripeChargeReceived.WithId(chargeId, currency))
                Response(Status.NO_CONTENT)
            },
        )
}

internal fun validatePayload(
    request: Request,
    webhookSecret: String,
    tolerance: Long = 300,
) {
    try {
        Webhook.constructEvent(
            request.bodyString(),
            request.headerValues("Stripe-Signature").joinToString(","),
            webhookSecret,
            tolerance,
        )
    } catch (e: SignatureVerificationException) {
        log.fatal("Failed to verify Stripe signature", cause = e)
        throw ForbiddenException()
    }
}
