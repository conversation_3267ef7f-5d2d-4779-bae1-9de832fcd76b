package hero.http4k.extensions

import hero.baseutils.nullIfEmpty
import hero.jwt.ACCESS_TOKEN
import org.http4k.core.Request
import org.http4k.core.cookie.Cookie
import org.http4k.lens.BiDiLens
import org.http4k.lens.Cookies
import org.http4k.lens.Header

fun Header.authorization(): BiDiL<PERSON><Request, Cookie?> = Cookies.optional(ACCESS_TOKEN, "Token in JWT format")

val Request.countryCode: String?
    get() = header("Cf-Ipcountry")

val Request.region: String?
    get() = header("Cf-Region")

val Request.city: String?
    get() = header("Cf-Ipcity")

val Request.location: String?
    get() {
        return listOfNotNull(city, region, countryCode)
            .joinToString(", ")
            .nullIfEmpty()
    }

val Request.remoteAddress: String?
    get() = header("Cf-Connecting-Ip") ?: header("X-Forwarded-For")?.replace(",.*".toRegex(), "")

val Request.userAgent: String?
    get() = headerValues("User-Agent").joinToString().nullIfEmpty()

val Request.deviceId: String?
    get() = header("X-Device-Id")
