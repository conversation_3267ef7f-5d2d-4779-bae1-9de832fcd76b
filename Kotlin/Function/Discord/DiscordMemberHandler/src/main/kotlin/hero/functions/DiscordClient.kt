package hero.functions

import com.fasterxml.jackson.annotation.JsonProperty
import com.github.kittinunf.fuel.core.Request
import com.github.kittinunf.fuel.core.ResponseResultOf
import com.github.kittinunf.fuel.httpDelete
import com.github.kittinunf.fuel.httpGet
import com.github.kittinunf.fuel.httpPost
import com.github.kittinunf.fuel.httpPut
import hero.baseutils.FuelException
import hero.baseutils.fetch
import hero.baseutils.fuelUserAgent
import hero.exceptions.http.NotFoundException
import hero.jackson.toJson
import java.net.URLEncoder

class DiscordClient(
    private val clientId: String,
    private val clientSecret: String,
    private val botToken: String,
) {
    private val path = "https://discord.com/api/v10"

    // https://discord.com/developers/docs/resources/guild#add-guild-member
    fun addUserToGuild(
        guildId: String,
        discordUserId: String,
        accessToken: String,
    ): ResponseResultOf<ByteArray> =
        "$path/guilds/$guildId/members/$discordUserId"
            .httpPut()
            .authorize(accessToken)
            .response()

    // https://discord.com/developers/docs/resources/guild#add-guild-member-role
    fun addUserGuildRole(
        guildId: String,
        discordUserId: String,
        roleId: String,
    ): ResponseResultOf<ByteArray> =
        "$path/guilds/$guildId/members/$discordUserId/roles/$roleId"
            .httpPut()
            .authorize(null)
            .response()

    // https://discord.com/developers/docs/resources/guild#remove-guild-member-role
    fun removeUserGuildRole(
        guildId: String,
        discordUserId: String,
        roleId: String,
        accessToken: String,
    ): ResponseResultOf<ByteArray> =
        "$path/guilds/$guildId/members/$discordUserId/roles/$roleId"
            .httpDelete()
            .authorize(accessToken)
            .response()

    // https://discord.com/developers/docs/resources/guild#create-guild-role
    fun createGuildRole(
        name: String,
        color: Int,
        mentionable: Boolean,
        hoist: Boolean,
        guildId: String,
    ): String =
        "$path/guilds/$guildId/roles"
            .httpPost()
            .authorize(botToken)
            .body(
                mapOf(
                    "name" to name,
                    "permissions" to 0,
                    "color" to color,
                    "hoist" to hoist,
                    "icon" to null,
                    "unicode_emoji" to null,
                    "mentionable" to mentionable,
                ).toJson(),
            )
            .fetch<DiscordRole>()
            .id

    // https://discord.com/developers/docs/resources/guild#get-guild-roles
    // NOTE there is no method to check for a single role
    fun getGuildRoles(guildId: String): List<String> =
        try {
            "$path/guilds/$guildId/roles"
                .httpGet()
                .authorize(botToken)
                .fetch<List<DiscordRole>>()
                .map { it.id }
        } catch (e: FuelException) {
            if (e.status == 404) {
                throw NotFoundException(e.message)
            }
            throw e
        }

    private fun Request.authorize(accessToken: String?) =
        this
            .let {
                if (accessToken != null)
                    it.body(mapOf("access_token" to accessToken).toJson())
                else
                    it
            }
            .header("User-Agent", fuelUserAgent)
            .header("Content-Type", "application/json")
            .header("Authorization", "Bot $botToken")

    fun refreshToken(refreshToken: String): AccessTokenResponse {
        // TODO implement via OAuthController
        return "$path/oauth2/token"
            .httpPost()
            .header("Content-Type", "application/x-www-form-urlencoded")
            .body(
                listOf(
                    "client_id" to clientId,
                    "client_secret" to clientSecret,
                    "grant_type" to "refresh_token",
                    "refresh_token" to refreshToken,
                )
                    .map { it.first + "=" + URLEncoder.encode(it.second, "UTF-8") }
                    .joinToString("&"),
            )
            // .authorize(accessToken)
            .fetch<AccessTokenResponse>()
    }
}

data class DiscordRole(
    val id: String,
)

data class AccessTokenResponse(
    @JsonProperty("access_token")
    val accessToken: String,
    @JsonProperty("token_type")
    val tokenType: String?,
    @JsonProperty("expires_in")
    val expiresIn: Long?,
    @JsonProperty("idToken")
    val idToken: String?,
    val scope: String?,
    @JsonProperty("refresh_token")
    val refreshToken: String?,
)
