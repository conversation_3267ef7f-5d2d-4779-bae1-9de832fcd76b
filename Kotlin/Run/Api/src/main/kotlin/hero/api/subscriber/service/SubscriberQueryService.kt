package hero.api.subscriber.service

import com.fasterxml.jackson.annotation.JsonTypeInfo
import hero.api.post.service.fetchActiveSubscription
import hero.api.subscriber.repository.SubscribersSort
import hero.api.subscriber.repository.SubscribersSort.GIFTED_FIRST
import hero.api.subscriber.repository.SubscribersSort.HIGHEST_PRICE
import hero.api.subscriber.repository.SubscribersSort.LOWEST_PRICE
import hero.api.subscriber.repository.SubscribersSort.NEWEST
import hero.api.subscriber.repository.SubscribersSort.OLDEST
import hero.baseutils.fromBase64
import hero.baseutils.toBase64
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.core.data.Sort
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.CollectionQuery
import hero.gcloud.MAX_ARRAY_SIZE_IN_ARRAY_OPERATOR
import hero.gcloud.TypedCollectionReference
import hero.gcloud.isNull
import hero.gcloud.util.paginate
import hero.gcloud.where
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.Tier
import hero.model.User
import java.time.Instant

class SubscriberQueryService(
    private val subscribersCollection: TypedCollectionReference<Subscriber>,
    private val usersCollection: TypedCollectionReference<User>,
) {
    fun execute(query: GetSubscribers): Page<Subscription> {
        validateSubscription(query.requesterId, query.creatorId)
        return execute(query.pageable, query.expired) {
            where(Subscriber::creatorId).isEqualTo(query.creatorId)
        }
    }

    fun execute(query: GetSubscriptions): Page<Subscription> {
        validateSubscription(query.requesterId, query.userId)
        return execute(query.pageable, query.expired) {
            where(Subscriber::userId).isEqualTo(query.userId)
        }
    }

    fun execute(query: GetSubscription): Subscription {
        val subscriber = subscribersCollection
            .where(Subscriber::userId).isEqualTo(query.userId)
            .and(Subscriber::creatorId).isEqualTo(query.creatorId)
            .fetchSingle()
            ?.takeIf { it.status in SubscriberStatus.activeStatuses }
            ?: throw NotFoundException("Subscription of user ${query.userId} for creator ${query.creatorId} not found")

        val user = usersCollection[query.userId].get()
        val creator = usersCollection[query.creatorId].get()

        return Subscription(
            subscriber,
            user,
            creator,
            Tier.ofId(subscriber.tierId),
        )
    }

    private fun execute(
        pageable: Pageable,
        expired: Boolean,
        whereCondition: TypedCollectionReference<Subscriber>.() -> CollectionQuery<Subscriber>,
    ): Page<Subscription> {
        val cursor = pageable.afterCursor?.fromBase64()?.fromJson<SubscriberCursor>()
        val sort = pageable.sort.by?.let { SubscribersSort.valueOf(it) } ?: NEWEST

        val (subscribers, hasNext) = subscribersCollection
            .whereCondition()
            .and(Subscriber::status).isIn(
                if (expired)
                    SubscriberStatus.inactiveStatuses
                else
                    SubscriberStatus.activeStatuses,
            )
            .editQueryByCursor(cursor, sort)
            .paginate(pageable.pageSize)

        val userById = subscribers
            .asSequence()
            .flatMap { listOf(it.userId, it.creatorId) }
            .distinct()
            .chunked(MAX_ARRAY_SIZE_IN_ARRAY_OPERATOR)
            .map { usersCollection.where(User::id).isIn(it).fetchAll() }
            .flatten()
            .associateBy { it.id }

        val mapped = subscribers.map {
            Subscription(
                it,
                user = userById[it.userId] ?: error("User ${it.userId} not found for subscriber ${it.id}"),
                creator = userById[it.creatorId] ?: error("Creator ${it.creatorId} not found for subscriber ${it.id}"),
                // use the constructed tier directly from Tier.ofId instead of fetching it from TierRepository,
                // as attributes like fee, default, and hidden are irrelevant in this context.
                Tier.ofId(it.tierId),
            )
        }

        return Page(mapped, nextPageable(subscribers, sort, pageable), hasNext)
    }

    private fun validateSubscription(
        requesterId: String,
        targetId: String,
    ) {
        if (requesterId != targetId) {
            subscribersCollection
                .fetchActiveSubscription(requesterId, targetId)
                ?: throw ForbiddenException("$requesterId does not subscribe $targetId")
        }
    }

    private fun nextPageable(
        subscribers: List<Subscriber>,
        sort: SubscribersSort,
        pageable: Pageable,
    ): Pageable {
        val afterCursor = subscribers
            .lastOrNull()
            ?.let {
                when (sort) {
                    OLDEST, NEWEST -> SubscriberSubscribedAtCursor(it.subscribed)
                    LOWEST_PRICE, HIGHEST_PRICE -> SubscriberTierIdCursor(it.tierId, it.subscribed)
                    GIFTED_FIRST -> {
                        val couponAppliedForMonths = it.couponAppliedForMonths
                        if (couponAppliedForMonths != null) {
                            SubscriberGiftedFirstCursor(couponAppliedForMonths.toInt(), it.subscribed)
                        } else {
                            SubscriberSubscribedAtCursor(it.subscribed)
                        }
                    }
                }
            }
            ?.toJson()?.toBase64()

        return PageRequest(-1, pageable.pageSize, afterCursor = afterCursor, sort = pageable.sort)
    }

    private fun CollectionQuery<Subscriber>.editQueryByCursor(
        cursor: SubscriberCursor?,
        sort: SubscribersSort,
    ): CollectionQuery<Subscriber> =
        when (sort) {
            OLDEST -> orderBySubscribedAt(cursor as? SubscriberSubscribedAtCursor, Sort.Direction.ASC)
            NEWEST -> orderBySubscribedAt(cursor as? SubscriberSubscribedAtCursor, Sort.Direction.DESC)

            LOWEST_PRICE -> orderByTierId(cursor as? SubscriberTierIdCursor, Sort.Direction.ASC)
            HIGHEST_PRICE -> orderByTierId(cursor as? SubscriberTierIdCursor, Sort.Direction.DESC)

            GIFTED_FIRST -> orderByCouponFirst(cursor)
        }

    private fun CollectionQuery<Subscriber>.orderBySubscribedAt(
        cursor: SubscriberSubscribedAtCursor?,
        direction: Sort.Direction,
    ): CollectionQuery<Subscriber> =
        orderBy(Subscriber::subscribed, direction).let {
            if (cursor == null) {
                it
            } else {
                it.startAfter(cursor.subscribedAt)
            }
        }

    private fun CollectionQuery<Subscriber>.orderByTierId(
        cursor: SubscriberTierIdCursor?,
        direction: Sort.Direction,
    ): CollectionQuery<Subscriber> =
        this
            .orderBy(Subscriber::tierId, direction)
            .orderBy(Subscriber::subscribed, Sort.Direction.DESC)
            .let {
                if (cursor == null) {
                    it
                } else {
                    it.startAfter(cursor.tierId, cursor.subscribedAt)
                }
            }

    private fun CollectionQuery<Subscriber>.orderByCouponFirst(
        cursor: SubscriberCursor?,
    ): CollectionQuery<Subscriber> =
        when (cursor) {
            null -> {
                orderBy(Subscriber::couponAppliedForMonths, Sort.Direction.DESC)
                    .orderBy(Subscriber::subscribed, Sort.Direction.DESC)
            }

            is SubscriberSubscribedAtCursor -> {
                and(Subscriber::couponAppliedForMonths).isNull()
                    .orderBy(Subscriber::subscribed, Sort.Direction.DESC)
                    .startAfter(cursor.subscribedAt)
            }

            is SubscriberGiftedFirstCursor -> {
                orderBy(Subscriber::couponAppliedForMonths, Sort.Direction.DESC)
                    .orderBy(Subscriber::subscribed, Sort.Direction.DESC)
                    .startAfter(cursor.couponAppliedForMonths.toLong(), cursor.subscribedAt)
            }

            else -> {
                throw IllegalArgumentException("Invalid cursor passed when sorting by GIFTED_FIRST")
            }
        }
}

/**
 * Sort is expected of type [SubscribersSort]
 */
data class GetSubscribers(
    val creatorId: String,
    val requesterId: String,
    val expired: Boolean,
    val pageable: Pageable,
)

/**
 * Sort is expected of type [SubscribersSort]
 */
data class GetSubscriptions(
    val userId: String,
    val requesterId: String,
    val expired: Boolean,
    val pageable: Pageable,
)

data class GetSubscription(
    val userId: String,
    val creatorId: String,
)

data class Subscription(val subscriber: Subscriber, val user: User, val creator: User, val tier: Tier)

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
private sealed class SubscriberCursor

private data class SubscriberSubscribedAtCursor(val subscribedAt: Instant) : SubscriberCursor()

private data class SubscriberTierIdCursor(val tierId: String?, val subscribedAt: Instant) : SubscriberCursor()

private data class SubscriberGiftedFirstCursor(
    val couponAppliedForMonths: Int,
    val subscribedAt: Instant,
) : SubscriberCursor()
