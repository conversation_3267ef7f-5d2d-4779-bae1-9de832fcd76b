package hero.functions

import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.topics.UploadEvent
import hero.sql.jooq.Tables
import hero.sql.jooq.tables.records.UploadTrackingRecord
import hero.test.IntegrationTest
import hero.test.TestEnvironmentVariables
import hero.test.logging.TestLogger
import org.assertj.core.api.Assertions.assertThat
import org.jooq.JSONB
import org.junit.jupiter.api.Test
import java.time.Instant

class UploadEventHandlerIT : IntegrationTest() {
    @Test
    fun `should track entire successful lifecycle of a multipart upload from upload to encoding`() {
        val underTest = UploadEventHandler(lazyTestContext, TestEnvironmentVariables, TestLogger)
        val creator = testHelper.createUser("cestmir")

        underTest.consume(
            UploadEvent.MultipartUploadInitiated(
                mimeType = "video/mp4",
                partsNumber = 7,
                contentLength = 317730560,
                uploadId = """
                    msUUYMTDe5_X9rEX8-6W6qOXrfMTqUMoCVNXKag8UUlwcw1sUPbo2JNDIBjhNZ53Dm9nUz1NHGwbxWRLCaEvmCPyN0Mcne7RPbpo3SDqTY-srRQMAq-chIZH65deu0l9
                """.trimIndent(),
                userId = creator.id,
                requestKey = "cd81c7b035ba465e9d70e0257e7f02a2",
                preSignedUrl = """
                    https://agmipoda.s3.eu-central-1.wasabisys.com/uploads/cd81c7b035ba465e9d70e0257e7f02a2/file.mp4?uploadId=msUUYMTDe5_X9rEX8-6W6qOXrfMTqUMoCVNXKag8UUlwcw1sUPbo2JNDIBjhNZ53Dm9nUz1NHGwbxWRLCaEvmCPyN0Mcne7RPbpo3SDqTY-srRQMAq-chIZH65deu0l9&partNumber=1&X-Amz-Expires=43200&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=5A5F01LBC655BWZOX82B%2F20250423%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250423T185809Z&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Signature=91e6a4198807d6048c406744fafa308a3fe984a4a74c5c01b90edf8687778d66
                """.trimIndent(),
                timestamp = Instant.ofEpochSecond(1745437225),
            ),
        )

        val initiatedUploadRecord = testContext
            .selectFrom(Tables.UPLOAD_TRACKING)
            .fetchSingle()

        val expectedInitiatedUploadRecord = UploadTrackingRecord().apply {
            this.id = 1
            this.mimeType = "video/mp4"
            this.partsNumber = 7
            this.contentLength = 317730560
            this.uploadId = """
                    msUUYMTDe5_X9rEX8-6W6qOXrfMTqUMoCVNXKag8UUlwcw1sUPbo2JNDIBjhNZ53Dm9nUz1NHGwbxWRLCaEvmCPyN0Mcne7RPbpo3SDqTY-srRQMAq-chIZH65deu0l9
            """.trimIndent()
            this.userId = creator.id
            this.requestKey = "cd81c7b035ba465e9d70e0257e7f02a2"
            this.preSignedUrl = """
                    https://agmipoda.s3.eu-central-1.wasabisys.com/uploads/cd81c7b035ba465e9d70e0257e7f02a2/file.mp4?uploadId=msUUYMTDe5_X9rEX8-6W6qOXrfMTqUMoCVNXKag8UUlwcw1sUPbo2JNDIBjhNZ53Dm9nUz1NHGwbxWRLCaEvmCPyN0Mcne7RPbpo3SDqTY-srRQMAq-chIZH65deu0l9&partNumber=1&X-Amz-Expires=43200&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=5A5F01LBC655BWZOX82B%2F20250423%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250423T185809Z&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Signature=91e6a4198807d6048c406744fafa308a3fe984a4a74c5c01b90edf8687778d66
            """.trimIndent()
            this.createdAt = Instant.ofEpochSecond(1745437225)
            this.updatedAt = Instant.ofEpochSecond(1745437225)
            this.events = JSONB.valueOf("[]")
        }

        assertThat(initiatedUploadRecord).isEqualTo(expectedInitiatedUploadRecord)

        // ========================= MultipartUploadCompleted  ========================= //
        val uploadCompletedEvent = UploadEvent.MultipartUploadCompleted(
            userId = creator.id,
            requestKey = "cd81c7b035ba465e9d70e0257e7f02a2",
            result = true,
            exception = null,
            timestamp = Instant.ofEpochSecond(1745437230),
        )
        underTest.consume(uploadCompletedEvent)

        val completedUploadRecord = testContext
            .selectFrom(Tables.UPLOAD_TRACKING)
            .fetchSingle()

        val expectedCompletedUploadRecord = expectedInitiatedUploadRecord.copy().apply {
            this.id = completedUploadRecord.id
            this.completedAt = Instant.ofEpochSecond(1745437230)
            this.updatedAt = Instant.ofEpochSecond(1745437230)
            // we will compare manually since comparing JSONB is a pain in the ass
            this.events = JSONB.valueOf("[${uploadCompletedEvent.toJson()}]")
        }
        assertThat(completedUploadRecord[Tables.UPLOAD_TRACKING.EVENTS].data().fromJson<List<UploadEvent>>())
            .containsExactly(uploadCompletedEvent)

        assertThat(completedUploadRecord).isEqualTo(expectedCompletedUploadRecord)

        // ========================= AssetEncodingInitiated  ========================= //
        val encodeInitiatedEvent = UploadEvent.AssetEncodingInitiated(
            userId = creator.id,
            url = """
                https://agmipoda.s3.eu-central-1.wasabisys.com/uploads/cd81c7b035ba465e9d70e0257e7f02a2/file.mp4?uploadId=msUUYMTDe5_X9rEX8-6W6qOXrfMTqUMoCVNXKag8UUlwcw1sUPbo2JNDIBjhNZ53Dm9nUz1NHGwbxWRLCaEvmCPyN0Mcne7RPbpo3SDqTY-srRQMAq-chIZH65deu0l9&partNumber=4&X-Amz-Expires=43200&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=5A5F01LBC655BWZOX82B%2F20250423%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250423T185809Z&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Signature=f7997aa5d5b8ec2718cf4873634715e3a6a85bfbf1c7b64a65c15ad03dc70167
            """.trimIndent(),
            exception = null,
            assetId = "vjsnxccq",
            timestamp = Instant.ofEpochSecond(1745437235),
        )

        underTest.consume(encodeInitiatedEvent)

        val encodeInitiatedRecord = testContext
            .selectFrom(Tables.UPLOAD_TRACKING)
            .fetchSingle()

        val expectedEncodeInitiatedRecord = expectedCompletedUploadRecord.copy().apply {
            this.id = encodeInitiatedRecord.id
            this.updatedAt = Instant.ofEpochSecond(1745437235)
            this.encodingStartedAt = Instant.ofEpochSecond(1745437235)
            this.assetId = "vjsnxccq"
            // we will compare manually since comparing JSONB is a pain in the ass
            this.events = encodeInitiatedRecord.events
        }
        assertThat(encodeInitiatedRecord[Tables.UPLOAD_TRACKING.EVENTS].data().fromJson<List<UploadEvent>>())
            .containsExactly(
                uploadCompletedEvent,
                encodeInitiatedEvent,
            )

        assertThat(encodeInitiatedRecord).isEqualTo(expectedEncodeInitiatedRecord)

        // ========================= AssetEncodingCompleted  ========================= //
        val encodeCompletedEvent = UploadEvent.AssetEncodingCompleted(
            assetId = "vjsnxccq",
            timestamp = Instant.ofEpochSecond(1745437240),
        )

        underTest.consume(encodeCompletedEvent)

        val encodeCompletedRecord = testContext
            .selectFrom(Tables.UPLOAD_TRACKING)
            .fetchSingle()

        val expectedEncodeCompletedRecord = expectedEncodeInitiatedRecord.copy().apply {
            this.id = encodeCompletedRecord.id
            this.updatedAt = Instant.ofEpochSecond(1745437240)
            this.encodingCompletedAt = Instant.ofEpochSecond(1745437240)
            // we will compare manually since comparing JSONB is a pain in the ass
            this.events = encodeCompletedRecord.events
        }
        assertThat(encodeCompletedRecord[Tables.UPLOAD_TRACKING.EVENTS].data().fromJson<List<UploadEvent>>())
            .containsExactly(
                uploadCompletedEvent,
                encodeInitiatedEvent,
                encodeCompletedEvent,
            )

        assertThat(encodeCompletedRecord).isEqualTo(expectedEncodeCompletedRecord)
    }

    @Test
    fun `should mark an upload as aborted`() {
        val underTest = UploadEventHandler(lazyTestContext, TestEnvironmentVariables, TestLogger)
        val creator = testHelper.createUser("cestmir")

        val initiatedUploadRecord = UploadTrackingRecord().apply {
            this.mimeType = "video/mp4"
            this.partsNumber = 7
            this.contentLength = 317730560
            this.uploadId = """
                    msUUYMTDe5_X9rEX8-6W6qOXrfMTqUMoCVNXKag8UUlwcw1sUPbo2JNDIBjhNZ53Dm9nUz1NHGwbxWRLCaEvmCPyN0Mcne7RPbpo3SDqTY-srRQMAq-chIZH65deu0l9
            """.trimIndent()
            this.userId = creator.id
            this.requestKey = "cd81c7b035ba465e9d70e0257e7f02a2"
            this.preSignedUrl = """
                    https://agmipoda.s3.eu-central-1.wasabisys.com/uploads/cd81c7b035ba465e9d70e0257e7f02a2/file.mp4?uploadId=msUUYMTDe5_X9rEX8-6W6qOXrfMTqUMoCVNXKag8UUlwcw1sUPbo2JNDIBjhNZ53Dm9nUz1NHGwbxWRLCaEvmCPyN0Mcne7RPbpo3SDqTY-srRQMAq-chIZH65deu0l9&partNumber=1&X-Amz-Expires=43200&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=5A5F01LBC655BWZOX82B%2F20250423%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250423T185809Z&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Signature=91e6a4198807d6048c406744fafa308a3fe984a4a74c5c01b90edf8687778d66
            """.trimIndent()
            this.createdAt = Instant.ofEpochSecond(1745437225)
            this.updatedAt = Instant.ofEpochSecond(1745437225)
            this.events = JSONB.valueOf("[]")
        }

        testContext.insertInto(Tables.UPLOAD_TRACKING).set(initiatedUploadRecord).execute()

        val multipartUploadAbortedEvent = UploadEvent.MultipartUploadAborted(
            userId = creator.id,
            requestKey = "cd81c7b035ba465e9d70e0257e7f02a2",
            result = true,
            exception = null,
            timestamp = Instant.ofEpochSecond(1745437230),
        )
        underTest.consume(multipartUploadAbortedEvent)

        val abortedUploadRecord = testContext
            .selectFrom(Tables.UPLOAD_TRACKING)
            .fetchSingle()

        val expectedAbortedUploadRecord = initiatedUploadRecord.copy().apply {
            this.id = abortedUploadRecord.id
            this.updatedAt = Instant.ofEpochSecond(1745437230)
            this.abortedAt = Instant.ofEpochSecond(1745437230)
            this.events = abortedUploadRecord.events
        }
        assertThat(abortedUploadRecord[Tables.UPLOAD_TRACKING.EVENTS].data().fromJson<List<UploadEvent>>())
            .containsExactly(multipartUploadAbortedEvent)

        assertThat(abortedUploadRecord).isEqualTo(expectedAbortedUploadRecord)
    }

    @Test
    fun `should insert tracking for single part upload`() {
        val underTest = UploadEventHandler(lazyTestContext, TestEnvironmentVariables, TestLogger)
        val creator = testHelper.createUser("cestmir")

        underTest.consume(
            UploadEvent.SinglePartUploadInitiated(
                mimeType = "video/mp4",
                userId = creator.id,
                requestKey = "cd81c7b035ba465e9d70e0257e7f02a2",
                preSignedUrl = """
                    https://agmipoda.s3.eu-central-1.wasabisys.com/uploads/cd81c7b035ba465e9d70e0257e7f02a2/file.mp4?uploadId=msUUYMTDe5_X9rEX8-6W6qOXrfMTqUMoCVNXKag8UUlwcw1sUPbo2JNDIBjhNZ53Dm9nUz1NHGwbxWRLCaEvmCPyN0Mcne7RPbpo3SDqTY-srRQMAq-chIZH65deu0l9&partNumber=1&X-Amz-Expires=43200&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=5A5F01LBC655BWZOX82B%2F20250423%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250423T185809Z&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Signature=91e6a4198807d6048c406744fafa308a3fe984a4a74c5c01b90edf8687778d66
                """.trimIndent(),
                timestamp = Instant.ofEpochSecond(1745437225),
            ),
        )

        val initiatedUploadRecord = testContext
            .selectFrom(Tables.UPLOAD_TRACKING)
            .fetchSingle()

        val expectedInitiatedUploadRecord = UploadTrackingRecord().apply {
            this.id = 1
            this.mimeType = "video/mp4"
            this.partsNumber = 0
            this.contentLength = 0
            this.uploadId = null
            this.userId = creator.id
            this.requestKey = "cd81c7b035ba465e9d70e0257e7f02a2"
            this.preSignedUrl = """
                    https://agmipoda.s3.eu-central-1.wasabisys.com/uploads/cd81c7b035ba465e9d70e0257e7f02a2/file.mp4?uploadId=msUUYMTDe5_X9rEX8-6W6qOXrfMTqUMoCVNXKag8UUlwcw1sUPbo2JNDIBjhNZ53Dm9nUz1NHGwbxWRLCaEvmCPyN0Mcne7RPbpo3SDqTY-srRQMAq-chIZH65deu0l9&partNumber=1&X-Amz-Expires=43200&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=5A5F01LBC655BWZOX82B%2F20250423%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20250423T185809Z&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Signature=91e6a4198807d6048c406744fafa308a3fe984a4a74c5c01b90edf8687778d66
            """.trimIndent()
            this.createdAt = Instant.ofEpochSecond(1745437225)
            this.updatedAt = Instant.ofEpochSecond(1745437225)
            this.events = JSONB.valueOf("[]")
        }

        assertThat(initiatedUploadRecord).isEqualTo(expectedInitiatedUploadRecord)
    }
}
