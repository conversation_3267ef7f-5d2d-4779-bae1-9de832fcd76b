package hero.http4k.config

import hero.baseutils.systemEnvRelaxed
import org.http4k.core.Filter
import org.http4k.core.Method
import org.http4k.filter.AnyOf
import org.http4k.filter.CorsPolicy
import org.http4k.filter.OriginPolicy
import org.http4k.filter.ServerFilters

private val corsPolicyDomains = systemEnvRelaxed("ORIGIN_POLICY")?.split(";")

private fun corsPolicy(domains: List<String>) =
    CorsPolicy(
        credentials = true,
        originPolicy = OriginPolicy.AnyOf(domains),
        headers = listOf(
            "Authorization",
            "Origin",
            "X-Requested-With",
            "Content-Type",
            "Accept",
            "Last-Commit",
            "X-Device-Id",
            "Cache-Control",
        ),
        methods = Method.entries,
        maxAge = 7200,
    )

val cors =
    if (corsPolicyDomains != null) {
        ServerFilters.Cors(corsPolicy(corsPolicyDomains))
    } else {
        Filter { next -> { next(it) } }
    }
