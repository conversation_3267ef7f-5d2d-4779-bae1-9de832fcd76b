package hero.api.device.controller

import hero.api.device.controller.dto.RegisterDeviceRequest
import hero.api.device.controller.dto.exampleRegisterDeviceRequest
import hero.api.device.service.DeviceCommandService
import hero.api.device.service.DisableDevice
import hero.api.device.service.RegisterDevice
import hero.http4k.auth.getJwtUser
import hero.http4k.extensions.authorization
import hero.http4k.extensions.delete
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.put
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path

class DevicesController(
    private val deviceCommandService: DeviceCommandService,
) {
    @Suppress("unused")
    val routeUpdateDevice: ContractRoute =
        ("/v1/devices" / Path.of("deviceId")).put(
            summary = "Register new device or update old one",
            tag = "Devices",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = exampleRegisterDeviceRequest,
            responses = listOf(Status.OK example Unit),
            handler = { request, _, deviceId ->
                val user = request.getJwtUser(allowImpersonation = false)
                val body = lens<RegisterDeviceRequest>(request)
                deviceCommandService.execute(
                    RegisterDevice(
                        userId = user.id,
                        deviceId = deviceId,
                        appVersion = body.appVersion,
                        registrationToken = body.registrationToken,
                        deviceType = body.deviceType,
                    ),
                )

                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("unused")
    val routeDeleteDevice: ContractRoute =
        ("/v1/devices" / Path.of("deviceId")).delete(
            summary = "Disable a device",
            tag = "Devices",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.OK example Unit),
            handler = { request, _, deviceId ->
                val user = request.getJwtUser(allowImpersonation = false)
                deviceCommandService.execute(
                    DisableDevice(
                        userId = user.id,
                        deviceId = deviceId,
                    ),
                )

                Response(Status.NO_CONTENT)
            },
        )
}
