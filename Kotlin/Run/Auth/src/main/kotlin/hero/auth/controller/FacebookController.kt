package hero.auth.controller

import hero.auth.service.FacebookService
import hero.baseutils.log
import hero.baseutils.minusMinutes
import hero.http4k.extensions.authorization
import hero.http4k.extensions.example
import hero.http4k.extensions.post
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import java.time.Instant

/** https://developers.facebook.com/docs/development/create-an-app/app-dashboard/data-deletion-callback/ */
class FacebookController(private val service: FacebookService) {
    @Suppress("unused")
    val routeDeAuth: ContractRoute =
        ("/v1/facebook/deauth").post(
            summary = "",
            tag = "Authentication",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            receiving = null,
            handler = { request, _ ->
                val requestRaw = String(request.body.payload.array())
                log.info("Received Facebook deletion request: $requestRaw")
                val requestParsed = service.parseDeletionRequest(requestRaw)
                if (requestParsed.issuedAt < Instant.now().minusMinutes(10)) {
                    log.error("Received old request, taking no action: $requestParsed")
                    return@post Response(Status.NO_CONTENT)
                }
                // TODO decide what to do with Facebook user of `requestParsed.userId`
                log.info("Received Facebook deletion request: $requestParsed")
                Response(Status.NO_CONTENT)
            },
        )
}
