package hero.api.invoice.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.instantOf
import hero.baseutils.log
import hero.baseutils.md5
import hero.baseutils.nullIfEmpty
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Currency
import hero.model.Invoice
import hero.model.InvoiceItemType
import hero.model.User
import hero.model.UserCompany
import hero.model.euCountries
import org.apache.commons.lang3.StringEscapeUtils.escapeXml11
import org.threeten.bp.LocalDate
import java.math.RoundingMode
import java.nio.charset.StandardCharsets
import java.nio.file.Files
import java.nio.file.Paths
import java.time.Instant
import java.time.temporal.ChronoUnit

fun main() {
    // Spec: https://linear.app/herohero/issue/HH-1349/dac7-reporting
    val year = 2024
    val currency = Currency.EUR

    val firestore = firestore(SystemEnv.cloudProject, true)
    val invoicesCollection = firestore.typedCollectionOf(Invoice)
    val usersCollection = firestore.typedCollectionOf(User)
    val creatorCompanies = mutableMapOf<String, UserCompany>()

    val invoicesToChange = invoicesCollection
        // .where(Invoice::userId).isEqualTo("userId")
        .where(Invoice::timestamp).isGreaterThan(instantOf("$year-01-01T00:00:00Z"))
        .and(Invoice::timestamp).isLessThan(instantOf("${year + 1}-01-01T00:00:00Z"))
        .fetchAll()
        .filter { (it.invoicedCompany.country ?: "CZ") in euCountries }
        .filter { it.currencyInvoice == currency }

    val invoicesToWrite = invoicesToChange.map {
        if (it.invoicedCompany.name.isNullOrBlank() || it.invoicedCompany.id.isNullOrBlank()) {
            val currentCompany = creatorCompanies.getOrPut(it.userId) {
                usersCollection[it.userId].fetch()?.companyOrPhysicalPerson ?: error("Couldn't find ${it.userId}")
            }
            it.copy(invoicedCompany = currentCompany)
        } else {
            it
        }
    }

    fun Collection<Invoice>.sum(): Sums {
        val parts = this.flatMap { it.items }
        // all values are served without decimals, see KDoc in Sums class
        return Sums(
            total = parts.filter { it.type == InvoiceItemType.TURNOVER }.sumOf { it.priceTotal.toLong() }.div(100),
            feeWithTaxes = parts.filter { it.type == InvoiceItemType.FEE }.sumOf { it.priceTotal.toLong() }.div(100),
            activities = parts.filter { it.type == InvoiceItemType.TURNOVER }.sumOf { it.count.toLong() },
            taxes = parts
                .filter { it.type == InvoiceItemType.FEE }
                .sumOf {
                    it.priceTotal.toBigDecimal().times(it.vatCents.toBigDecimal())
                        .divide(100.toBigDecimal(), RoundingMode.HALF_UP).toLong()
                }
                .div(100),
        )
    }

    val groups = invoicesToWrite
        .groupBy { it.invoicedCompany.name + it.invoicedCompany.id }
        .map { (_, invoices) -> invoices }
        .map { invoices ->
            try {
                val userId = invoices.first().userId
                val company = invoices.first().invoicedCompany

                val q1 = invoices
                    .filter { it.timestamp < instantOf("$year-04-01T00:00:00Z") }
                    .sum()
                val q2 = invoices
                    .filter {
                        it.timestamp >= instantOf("$year-04-01T00:00:00Z") &&
                            it.timestamp < instantOf("$year-07-01T00:00:00Z")
                    }
                    .sum()
                val q3 = invoices
                    .filter {
                        it.timestamp >= instantOf("$year-07-01T00:00:00Z") &&
                            it.timestamp < instantOf("$year-10-01T00:00:00Z")
                    }
                    .sum()
                val q4 = invoices
                    .filter {
                        it.timestamp >= instantOf("$year-10-01T00:00:00Z") &&
                            it.timestamp < instantOf("${year + 1}-01-01T00:00:00Z")
                    }
                    .sum()

                val vatId = company.vatId?.trim().nullIfEmpty()
                println("CZ$year-${(company.name + company.id).md5()} $userId ${company.name} ${company.id}")
                val seller = if (company.id.nullIfEmpty() == null && company.birthDate.nullIfEmpty() != null) {
                    """
                    <IndividualSeller>
                      <Standard>
                        <IndSellerID>
                          <ResCountryCode>${company.country ?: "CZ"}</ResCountryCode>
                          <TIN unknown="${vatId.isNullOrBlank()}"/>
                          <Name>
                            <FirstName>${escapeXml11(company.firstName)}</FirstName>
                            <LastName>${escapeXml11(company.lastName)}</LastName>
                          </Name>
                          <Address>
                            <CountryCode>${company.country ?: "CZ"}</CountryCode>
                            <AddressFree>${escapeXml11(company.address)}</AddressFree>
                          </Address>
                          <BirthInfo>
                            <BirthDate>${company.birthDate}</BirthDate>
                            <BirthPlace>
                              <City>${company.city}</City>
                              <CountryInfo>
                                <CountryCode>${company.country}</CountryCode>
                              </CountryInfo>
                            </BirthPlace>
                          </BirthInfo>
                        </IndSellerID>
                      </Standard>
                    </IndividualSeller>
                """
                } else {
                    """
                    <EntitySeller>
                      <Standard>
                        <EntSellerID>
                          <ResCountryCode>${company.country ?: "CZ"}</ResCountryCode>
                          <!-- TIN is vatId without prefix -->
                          <TIN issuedBy="${company.country ?: "CZ"}" unknown="${vatId == null}">${(company.vatId?.trim().nullIfEmpty() ?: "  ").substring(
                        2,
                    )}</TIN>
                          <!--
                            This attribute defines the type of identification number being sent among the following:
                            •	[EU Specific]: IIN for the reporting of an individual identification number;
                            •	LEI for the reporting of a legal entity identifier;
                            •	EIN for the reporting of an entity identification number; 
                            •	BRN for the reporting of a business registration number; or
                            •	Other.
                          -->
                          ${if (!company.id.isNullOrBlank()) "<IN issuedBy=\"${company.country ?: "CZ"}\" INType=\"LEI\">${company.id}</IN>" else ""}
                          ${if (vatId != null) "<VAT>${company.vatId?.trim().nullIfEmpty() ?: ""}</VAT>" else ""}
                          <Name>${escapeXml11(company.name.nullIfEmpty() ?: company.namePublic)}</Name>
                          <Address>
                            <CountryCode>${company.country ?: "CZ"}</CountryCode>
                            <AddressFix>
                                <Street>${escapeXml11(company.address.nullIfEmpty() ?: " ")}</Street>
                                <PostCode>${escapeXml11(company.postalCode.nullIfEmpty() ?: " ")}</PostCode>
                                <City>${escapeXml11(company.city.nullIfEmpty() ?: " ")}</City>
                            </AddressFix>
                          </Address>
                        </EntSellerID>
                      </Standard>
                    </EntitySeller>
                """
                }

                """
                <ReportableSeller>
                  <Identity>
                     $seller
                  </Identity>
                  <RelevantActivities>
                    <PersonalServices>
                      <Consideration>
                        <ConsQ1 currCode="$currency">${q1.consideration}</ConsQ1>
                        <ConsQ2 currCode="$currency">${q2.consideration}</ConsQ2>
                        <ConsQ3 currCode="$currency">${q3.consideration}</ConsQ3>
                        <ConsQ4 currCode="$currency">${q4.consideration}</ConsQ4>
                      </Consideration>
                      <NumberOfActivities>
                        <NumbQ1>${q1.activities}</NumbQ1>
                        <NumbQ2>${q2.activities}</NumbQ2>
                        <NumbQ3>${q3.activities}</NumbQ3>
                        <NumbQ4>${q4.activities}</NumbQ4>
                      </NumberOfActivities>
                      <Fees>
                        <FeesQ1 currCode="$currency">${q1.feeWithoutTaxes}</FeesQ1>
                        <FeesQ2 currCode="$currency">${q2.feeWithoutTaxes}</FeesQ2>
                        <FeesQ3 currCode="$currency">${q3.feeWithoutTaxes}</FeesQ3>
                        <FeesQ4 currCode="$currency">${q4.feeWithoutTaxes}</FeesQ4>
                      </Fees>
                      <Taxes>
                        <TaxQ1 currCode="$currency">${q1.taxes}</TaxQ1>
                        <TaxQ2 currCode="$currency">${q2.taxes}</TaxQ2>
                        <TaxQ3 currCode="$currency">${q3.taxes}</TaxQ3>
                        <TaxQ4 currCode="$currency">${q4.taxes}</TaxQ4>
                      </Taxes>
                    </PersonalServices>
                  </RelevantActivities>
                  <DocSpec>
                    <stf:DocTypeIndic>OECD1</stf:DocTypeIndic>
                    <stf:DocRefId>CZ$year-${(company.name + company.id).md5()}</stf:DocRefId>
                  </DocSpec>
                </ReportableSeller>
            """
            } catch (e: Exception) {
                log.error(invoices.map { it.id }.toString() + " " + e.message)
            }
        }

    val content = """
        <?xml version="1.0" encoding="UTF-8"?>
        <DPI_OECD
            xmlns="urn:oecd:ties:dpi:v1"
            xmlns:stf="urn:oecd:ties:dpistf:v1" version="1.0"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:schemaLocation="urn:oecd:ties:dpi:v1 DPIXML_v1.0.xsd">
          <MessageSpec>
            <TransmittingCountry>CZ</TransmittingCountry>
            <ReceivingCountry>CZ</ReceivingCountry>
            <MessageType>DPI</MessageType>
            <MessageRefId>CZ${year}CZ-herohero.co</MessageRefId>
            <!--
                401 -> The message contains new (including additional) information.
                402 -> The message contains corrections for previously sent information.
            -->
            <MessageTypeIndic>DPI401</MessageTypeIndic>
            <ReportingPeriod>$year-12-31</ReportingPeriod>
            <Timestamp>${Instant.now().truncatedTo(ChronoUnit.SECONDS)}</Timestamp>
          </MessageSpec>
          <DPIBody>
            <PlatformOperator>
              <ResCountryCode>CZ</ResCountryCode>
              <TIN issuedBy="CZ">09644156</TIN>
              <VAT>CZ09644156</VAT>
              <Name>Herohero s.r.o.</Name>
              <Address legalAddressType="OECD304">
                <CountryCode>CZ</CountryCode>
                <AddressFix>
                    <Street>Spálená 53</Street>
                    <PostCode>150 00</PostCode>
                    <City>Prague</City>
                </AddressFix>
              </Address>
              <DocSpec>
                <stf:DocTypeIndic>OECD1</stf:DocTypeIndic>
                <!-- value must be unique through all given reports even if the report is correctional -->
                <stf:DocRefId>CZ$year-Herohero-${LocalDate.now()}</stf:DocRefId>
              </DocSpec>
            </PlatformOperator>
            ${groups.joinToString("\n")}
          </DPIBody>
        </DPI_OECD>
    """.trimIndent()
        .replace("(?s)\\s*<!--.*?-->\\s*".toRegex(), "\n")

    val buildDir = Sums::class.java.protectionDomain.codeSource.location.path
    val resourcesPath = buildDir.replace("/out.*".toRegex(), "/src/main/resources")
    log.info("Building DAC7 export in: $resourcesPath")
    Files.write(Paths.get("$resourcesPath/dac7/export-$year.xml"), content.toByteArray(StandardCharsets.UTF_8))
}

data class Sums(
    val total: Long,
    /**
     * The Fees element is further split into four elements, representing the quarters in respect of which reporting takes place.
     * Each quarter element is further comprised of the MonAmnt_Type, used to communicate the fees withheld in respect of Sellers.
     * Such amounts shall be given in full units, i.e. without decimals. The code for the currency, in which the value is expressed
     * has to be taken from the ISO code list 4217 and added in attribute currCode.
     */
    val feeWithTaxes: Long,
    /**
     * The Number of Activities element specified the number of Relevant Activities that a Reportable Seller
     * has provided. It is further split into four elements. These elements represent the four quarters in respect
     * of which reporting of the number of Relevant Activities in respect of which Consideration was paid
     * or credited to the Reportable Seller is required. As such, that the numbers of activities are reported
     * on the basis of the date of payment or credit of the Consideration.
     */
    val activities: Long,
    /**
     * The Taxes element is further split into four elements, representing the quarters in respect of which reporting takes place.
     * Each quarter element is further comprised of the MonAmnt_Type, used to communicate taxes withheld in respect of Sellers.
     * Such amounts shall be given in full units, i.e. without decimals. The code for the currency, in which the value is expressed
     * has to be taken from the ISO code list 4217 and added in attribute currCode.
     *
     * In our case, this is a tax deducted from our fee.
     */
    val taxes: Long,
) {
    /**
     * Consideration is a compensation in any form, net of any fees, commissions or taxes withheld or charged by the platform
     * operator, that is paid or credited to a seller in connection with the relevant activity.
     *
     * The Consideration element contains information on the Consideration received by a Reportable Seller
     * in relation to the Relevant Activities provided. It is further split into four elements, representing
     * the quarters during which the Consideration was paid or credited to a Reportable Seller. In this respect,
     * Consideration is considered to be paid or credited to a Reportable Seller when it is paid or credited
     * to an account specified by the Reportable Seller.
     */
    val consideration: Long
        get() = total - feeWithTaxes

    val feeWithoutTaxes: Long
        get() = feeWithTaxes - taxes
}
