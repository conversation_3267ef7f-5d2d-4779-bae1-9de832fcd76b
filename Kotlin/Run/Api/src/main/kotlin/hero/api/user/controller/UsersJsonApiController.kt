package hero.api.user.controller

import com.fasterxml.jackson.annotation.JsonProperty
import hero.api.category.controller.categoryDtoExample
import hero.api.category.repository.CategoriesRepository
import hero.api.post.service.htmlSanitizer
import hero.api.user.repository.RssFeedsRepository
import hero.api.user.repository.TiersRepository
import hero.api.user.repository.UsersRepository
import hero.api.user.repository.pathUpdateableAfter
import hero.api.user.repository.toDto
import hero.api.user.service.DeleteUser
import hero.api.user.service.DeleteUserCommandService
import hero.baseutils.log
import hero.baseutils.nullIfEmpty
import hero.baseutils.removeNonAscii
import hero.core.data.Caching
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.exceptions.http.UnauthorizedException
import hero.gcloud.PubSub
import hero.http4k.auth.parseJwtUser
import hero.http4k.config.PAGE_SIZE_MAX
import hero.http4k.controller.HeaderUtils
import hero.http4k.controller.QueryUtils
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.ErrorResponse
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.caching
import hero.http4k.extensions.delete
import hero.http4k.extensions.enum
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.patch
import hero.http4k.extensions.post
import hero.jackson.map
import hero.model.Analytics
import hero.model.CategoryDto
import hero.model.CategoryDtoRelationship
import hero.model.CompanyType
import hero.model.Creator
import hero.model.Currency
import hero.model.DeletedReason
import hero.model.DiscordDtoAttributes
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.FeedUrlResponse
import hero.model.GjirafaLivestreamMeta
import hero.model.ImageAsset
import hero.model.ListResponseMeta
import hero.model.NotificationsEnabled
import hero.model.StripeRequirements
import hero.model.SupportCounts
import hero.model.Tier
import hero.model.TierDtoRelationship
import hero.model.UserCompany
import hero.model.UserDetails
import hero.model.UserDto
import hero.model.UserDtoAttributes
import hero.model.UserDtoIncluded
import hero.model.UserDtoListResponse
import hero.model.UserDtoRelationships
import hero.model.UserStatus
import hero.model.VatPayer
import hero.model.topics.DiscordConnectionChanged
import hero.model.topics.FeesUpdateRequested
import hero.model.topics.SubscriberStatusChange
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.boolean
import org.http4k.lens.nonEmptyString
import org.http4k.lens.regex
import org.http4k.lens.string
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.util.Base64

class UsersJsonApiController(
    private val categoriesRepository: CategoriesRepository,
    private val deleteUserCommandService: DeleteUserCommandService,
    private val repository: UsersRepository,
    private val pubSub: PubSub,
    private val tierRepository: TiersRepository,
    private val rssFeedsRepository: RssFeedsRepository,
) {
    companion object {
        internal val excludedPaths =
            sortedSetOf("assets", "create", "hero", "herohero", "login", "post", "public", "search", "services")

        private val phoneMatch: Regex = "[+][ 0-9]{8,}".toRegex()

        private val exampleTier = Tier(
            id = "EUR05",
            price = 500,
            default = false,
            currency = Currency.EUR,
            hidden = false,
        )

        val exampleCompany = UserCompany(
            namePublic = "podcast of my-company",
            name = "my-company s.r.o.",
            firstName = "Lukas",
            lastName = "Jandac",
            isIndividual = false,
            companyType = CompanyType.LEGAL_ENTITY,
            address = "my-address",
            city = "Brno",
            postalCode = "100 00",
            id = "01010101",
            country = "CZ",
            state = "Alabama",
            phone = "+*********** 060",
            vatId = "UK01010101",
            birthDate = "2000-01-01",
            registeredWith = "Subscribed in Prague.",
            vatType = VatPayer.VAT_PAYER,
            iban = "*****************",
            swift = "SWIFTYSWIFT",
            additionalInfo = "IČ DPH 10101001",
        )

        val exampleUserResponseV2 = UserDto(
            id = "mul-and-f29fca0",
            attributes = UserDtoAttributes(
                name = "Andreas Muller",
                bio = "Just a good guy.",
                createdAt = Instant.now(),
                image = ImageAsset(
                    id = "https://uploaded.image/url.jpg",
                    width = 600,
                    height = 400,
                ),
                path = "janedoe-painter",
                pathChangeableAt = Instant.now().pathUpdateableAfter(),
                subscribable = false,
                verified = false,
                stripeAccountId = "acct_1I0aQDudm32s9IUc",
                counts = SupportCounts(
                    supporters = 10,
                    supporting = 20,
                    supportersThreshold = 10,
                    incomes = 1800,
                    incomesClean = 1000,
                    payments = 10,
                    invoices = 0,
                ),
                hasRssFeed = true,
                hasDrm = false,
                language = "en",
                discord = DiscordDtoAttributes(
                    id = "321",
                    guildId = "123",
                ),
                hasLivestreams = true,
                hasSpotifyExport = true,
                hasSpotifyConnection = true,
                spotifyUri = "spotify:show:604vOUSIAcprLWqCcRWcAT",
                notificationsEnabled = NotificationsEnabled(
                    emailNewPost = true,
                ),
                creatorSuspended = false,
                analytics = Analytics(
                    facebookPixelId = "asfjnfjghnbauwjej",
                    ga4Stream = "gagaga4",
                    googleAdsConversionId = "google-ads-conversion-id",
                    googleAdsConversionLabel = "google-ads-conversion-label",
                    tiktokPixelId = "tiktok-pixel-id",
                ),
                company = exampleCompany,
                privacyPolicyEffectiveAt = Instant.now(),
                lastChargeFailedAt = Instant.now(),
                gjirafaLivestream = GjirafaLivestreamMeta(
                    publicId = "public-id",
                    streamUrl = "https://stream-url",
                    streamKey = "stream-key",
                    playbackUrl = "https://playback-url",
                ),
                isOfAge = true,
            ),
            relationships = UserDtoRelationships(
                tier = TierDtoRelationship(exampleTier.id),
                categories = listOf(CategoryDtoRelationship("category-id")),
            ),
        )
    }

    @Suppress("unused")
    val routeUserGet: ContractRoute =
        ("/v2/users" / Path.userId().of("userId")).get(
            summary = "Get user by given id.",
            tag = "Users",
            parameters = object {},
            responses = listOf(
                Status.OK example exampleUserResponseV2,
            ),
            meta = { it.markAsDeprecated() },
            handler = { request, _, userId ->
                val jwtUser = request.parseJwtUser()
                val user = repository.get(userId)
                Response(Status.OK)
                    .body(
                        repository.toDto(
                            user = user,
                            details = jwtUser?.id == user.id,
                            categories = categoriesRepository.list(user.id),
                        ),
                    )
            },
        )

    @Suppress("unused")
    @Deprecated("We should migrate these fields to regular UserDto and /v2/users endpoint.")
    val routeUserGetDetails: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "details").get(
            summary = "Get user by given id.",
            tag = "Users",
            parameters = object {
                val authorization = Header.authorization()
            },
            meta = { it.markAsDeprecated() },
            responses = listOf(
                Status.OK example UserDetails(
                    email = "<EMAIL>",
                    language = "en",
                    creator = Creator(
                        tierId = "EUR05",
                        stripeAccountId = "ac_123456789",
                        stripeAccountActive = true,
                        stripeAccountOnboarded = true,
                        currency = Currency.EUR,
                        stripeRequirements = StripeRequirements(
                            "a",
                            false,
                            "b",
                            listOf(),
                            listOf(),
                            listOf(),
                            listOf(),
                            listOf(),
                        ),
                    ),
                    notificationsEnabled = NotificationsEnabled(
                        emailNewPost = true,
                    ),
                    discord = DiscordDtoAttributes("discord-id", "guild-id"),
                ),
                Status.BAD_REQUEST to ErrorResponse("Missing or malformed parameter."),
            ),
            handler = { request, _, userId, _ ->
                val user = repository.get(request, userId)
                Response(Status.OK)
                    .body(
                        UserDetails(
                            email = user.email,
                            creator = user.creator,
                            notificationsEnabled = user.notificationsEnabled,
                            language = user.language,
                            discord = user.discord?.let {
                                if (it.active)
                                    DiscordDtoAttributes(
                                        id = it.id!!,
                                        guildId = it.guildId,
                                    )
                                else
                                    null
                            },
                        ),
                    )
            },
        )

    @Suppress("unused")
    val routeUserSearch: ContractRoute =
        "/v2/users".get(
            summary = "Search users by query.",
            tag = "Users",
            parameters = object {
                val query = Query.nonEmptyString().optional("query", "Query string to search for.")
                val subscriptionUserId = Query.regex("([a-z-]+)")
                    .optional("subscriptionUserId", "Fetch creators to which given user is subscribed.")
                val subscriptionCreatorId = Query.regex("([a-z-]+)")
                    .optional("subscriptionCreatorId", "Fetch users subscribed to given creator.")
                @Deprecated("Use `featuredBy` instead.")
                val featured = Query.boolean().optional("featured", "Do not use. Use `featuredBy` instead.")
                val featuredBy = Query.regex("([a-z]{2})")
                    .optional("featuredBy", "Fetch users featured by given language.")
                val spotifyUri = Query.regex("(spotify:show:[a-zA-Z0-9]+)")
                    .optional("spotifyUri", "Spotify Uri to search for.")
                // this should not be a regex to properly return 404 for any path instead of 400
                val path = Query.string().optional("path", "Find user by its URL path.")
                val userIds = Query.regex("([a-z-]{2,64})").multi
                    .optional("userIds[]", "Select multiple users by their userId.")
                val include = Query.string()
                    .optional("include", "Entities to include. Supported: [tiers,categories]")
                val listDeleted = Query.boolean()
                    .defaulted("listDeleted", false, "List deleted users as empty entities.")
                val pageIndex = QueryUtils.pageIndex()
                val pageSize = QueryUtils.pageSize()
            },
            responses = listOf(
                Status.OK example UserDtoListResponse(
                    meta = ListResponseMeta(1, true),
                    users = listOf(exampleUserResponseV2),
                    included = UserDtoIncluded(
                        tiers = listOf(exampleTier.toDto()),
                        categories = listOf(categoryDtoExample),
                    ),
                ),
                Status.BAD_REQUEST to ErrorResponse("Missing or malformed parameter."),
            ),
            meta = { it.markAsDeprecated() },
            handler = { request, parameters ->
                val jwtUser = request.parseJwtUser()
                val query = parameters.query(request)
                val path = parameters.path(request)
                val featured = parameters.featured(request) == true
                val featuredBy = parameters.featuredBy(request)
                val spotifyUri = parameters.spotifyUri(request)
                val subscriptionUserId = parameters.subscriptionUserId(request)
                val subscriptionCreatorId = parameters.subscriptionCreatorId(request)
                val userIds = parameters.userIds(request) ?: listOf()
                val listDeleted = parameters.listDeleted(request)
                val pageIndex = parameters.pageIndex(request)
                val pageSize = parameters.pageSize(request)
                if (pageSize > PAGE_SIZE_MAX) {
                    throw BadRequestException("Parameter pageSize is out of bounds.", mapOf())
                }

                val users = repository
                    .getUsers(
                        query = query,
                        path = path,
                        subscriptionUserId = subscriptionUserId,
                        subscriptionCreatorId = subscriptionCreatorId,
                        queryUserIds = userIds,
                        featured = featured,
                        featuredBy = featuredBy,
                        spotifyUri = spotifyUri,
                        onBehalfOfUser = jwtUser,
                        offset = pageIndex,
                        limit = pageSize + 1,
                    )

                val categories = mutableListOf<CategoryDto>()
                val isInternalCall = HeaderUtils.hasValidApiKey(request)
                val userDtos = users
                    .filter { listDeleted || it.status != UserStatus.DELETED }
                    .map { user ->
                        val userCategories = categoriesRepository.list(user.id)
                        categories += userCategories
                        val showDetails = isInternalCall || (jwtUser?.id == user.id)
                        repository.toDto(user = user, details = showDetails, categories = userCategories)
                    }

                Response(Status.OK)
                    .caching(if (featured) Caching.Enabled(Duration.ofMinutes(10)) else Caching.Disabled)
                    .body(
                        UserDtoListResponse(
                            meta = ListResponseMeta(pageIndex, users.size == pageSize + 1),
                            users = userDtos.take(pageSize),
                            included = UserDtoIncluded(
                                tiers = users
                                    .map { it.creator.tierId }
                                    .distinct()
                                    .sorted()
                                    .map { tierRepository.get(it).toDto() },
                                categories = categories,
                            ),
                        ),
                    )
            },
        )

    @Suppress("unused")
    val routeUserDelete: ContractRoute =
        ("/v1/users" / Path.userId().of("userId")).delete(
            summary = "Delete user. Admins are allowed to delete other users.",
            tag = "Users",
            parameters = object {
                val cancelSubscriptions = Query.boolean()
                    .required("cancelSubscriptions", "True to cancel all susbcriptions.")
                val refundSubscriptions = Query.boolean()
                    .required("refundSubscriptions", "True to refund all subscriptions.")
                val deletedReason = Query.enum<DeletedReason>()
                    .required("deletedReason", "Reason for profile deletion.")
                val deletedNote = Query.string()
                    .optional("deletedNote", "Reason for profile deletion.")
            },
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            meta = { it.markAsDeprecated() },
            handler = { request, parameters, userId ->
                val jwtUser = request.parseJwtUser(allowImpersonation = false) ?: throw UnauthorizedException()
                // todo mark session id as non nullable
                val sessionId = jwtUser.sessionId ?: error("Session id missing for user ${jwtUser.id}")

                deleteUserCommandService.execute(
                    DeleteUser(
                        userId = userId,
                        requesterId = jwtUser.id,
                        sessionId = sessionId,
                        cancelSubscriptions = parameters.cancelSubscriptions(request),
                        refundSubscriptions = parameters.refundSubscriptions(request),
                        deletedReason = parameters.deletedReason(request),
                        deletedNote = parameters.deletedNote(request),
                    ),
                )
                Response(Status.NO_CONTENT)
            },
        )

    enum class ValidationErrorType {
        @JsonProperty("min_length_two")
        MIN_LENGTH_TWO,

        @JsonProperty("lowercase_alphanumeric")
        LOWERCASE_ALPHANUMERIC,

        @JsonProperty("illegal_string")
        ILLEGAL_STRING,

        @JsonProperty("name_taken")
        NAME_TAKEN,

        @JsonProperty("path_change_too_often")
        PATH_CHANGE_TOO_OFTEN,

        @JsonProperty("max_length_exceeded")
        MAX_LENGTH_EXCEEDED,
    }

    class ValidationException(
        val userId: String,
        val property: String,
        val value: String,
        val error: ValidationErrorType,
        override val message: String = "Field '$property' of value '$value' is invalid: ${error.name.lowercase()}",
    ) : BadRequestException(
            message = message,
            labels = mapOf("userId" to userId),
            body = ValidationExceptionBody(listOf(ValidationExceptionBodyError(property, value, error))),
        )

    data class ValidationExceptionBody(
        val errors: List<ValidationExceptionBodyError>,
    )

    data class ValidationExceptionBodyError(
        val property: String,
        val value: String,
        val error: ValidationErrorType,
    )

    @Suppress("unused")
    val routePatchUser: ContractRoute =
        ("/v2/users" / Path.userId().of("userId")).patch(
            summary = "Updates current user's profile.",
            tag = "Users",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = exampleUserResponseV2,
            responses = listOf(
                Status.OK example exampleUserResponseV2,
                Status.CONFLICT example ValidationExceptionBody(
                    errors = listOf(
                        ValidationExceptionBodyError(
                            "path",
                            "JANE_DOE",
                            ValidationErrorType.LOWERCASE_ALPHANUMERIC,
                        ),
                        ValidationExceptionBodyError(
                            "name",
                            "herohero",
                            ValidationErrorType.ILLEGAL_STRING,
                        ),
                    ),
                ),
            ),
            meta = { it.markAsDeprecated() },
            handler = { request, _, userId ->
                val updates = lens<UserDto>(request)
                val user = repository.get(request, userId)
                var feesUpdate = false
                log.info("User updates their profile.", mapOf("userId" to user.id) + updates.map())
                // path update
                val updatePath = updates.attributes.path?.trim()
                if (updatePath != user.path && updatePath != null) {
                    if (updatePath.length < 3) {
                        throw ValidationException(
                            user.id,
                            "path",
                            updatePath,
                            ValidationErrorType.MIN_LENGTH_TWO,
                        )
                    }
                    if (updatePath.length > 64) {
                        throw ValidationException(
                            user.id,
                            "path",
                            updatePath,
                            ValidationErrorType.MAX_LENGTH_EXCEEDED,
                        )
                    }
                    if (!updatePath.matches("[a-z0-9]{2,}".toRegex())) {
                        throw ValidationException(
                            user.id,
                            "path",
                            updatePath,
                            ValidationErrorType.LOWERCASE_ALPHANUMERIC,
                        )
                    }
                    if (updatePath in excludedPaths) {
                        throw ValidationException(
                            user.id,
                            "path",
                            updatePath,
                            ValidationErrorType.ILLEGAL_STRING,
                        )
                    }
                    if ("herohero" in updatePath) {
                        throw ValidationException(
                            user.id,
                            "path",
                            updatePath,
                            ValidationErrorType.ILLEGAL_STRING,
                        )
                    }
                    val path = repository.path(updatePath)
                    if (path?.userId != null && path.userId != user.id) {
                        throw ValidationException(
                            user.id,
                            "path",
                            updatePath,
                            ValidationErrorType.NAME_TAKEN,
                            "User.path is already taken by other user: $updatePath",
                        )
                    }
                    if (user.pathChanged.pathUpdateableAfter() > Instant.now()) {
                        throw ValidationException(
                            user.id,
                            "path",
                            updatePath,
                            ValidationErrorType.PATH_CHANGE_TOO_OFTEN,
                        )
                    }
                    user.path = updatePath
                    user.pathChanged = Instant.now()
                }

                if (updates.attributes.notificationsEnabled != null) {
                    user.notificationsEnabled = updates.attributes.notificationsEnabled!!
                }
                if (updates.attributes.creatorSuspended != null) {
                    user.creator.suspended = updates.attributes.creatorSuspended!!
                }
                if (updates.attributes.isOfAge != null) {
                    user.isOfAge = updates.attributes.isOfAge!!
                }
                user.hasRssFeed = updates.attributes.hasRssFeed
                user.hasDrm = updates.attributes.hasDrm
                user.hasSpotifyExport = updates.attributes.hasSpotifyExport

                // tier update
                if (updates.relationships.tier != null && updates.relationships.tier?.id != user.creator.tierId) {
                    val tier = tierRepository[updates.relationships.tier!!.id]
                    if (user.creator.currency != null && tier.currency != user.creator.currency) {
                        throw ConflictException(
                            "Creator $userId has already fixed currency to ${user.creator.currency}" +
                                " and cannot use Tier ${tier.id}.",
                            mapOf("userId" to user.id),
                        )
                    }
                    user.creator.tierId = tier.id
                }

                val company = updates.attributes.company
                if (company != null) {
                    if (company.vatId.nullIfEmpty() != null &&
                        (
                            !company.vatId!!.matches(
                                "[A-Z]{2}[A-Z0-9]{6,}".toRegex(),
                            ) ||
                                company.vatId!!.startsWith("EU")
                        )
                    ) {
                        throw BadRequestException(
                            "VAT ID for $userId must be in format of EU012345678 " +
                                "(but not with EU, must be local country), given was: ${company.vatId}",
                            mapOf("userId" to userId),
                        )
                    }
                    if (company.vatId != user.company?.vatId) {
                        feesUpdate = true
                    }
                    if (company.phone?.matches(phoneMatch) == false) {
                        throw BadRequestException(
                            "Phone must be numeric and start with with prefix like +999 for $userId.",
                            mapOf("userId" to userId),
                        )
                    }
                    if (!company.birthDate.isNullOrEmpty()) {
                        val birthDate = try {
                            LocalDate.parse(company.birthDate)
                        } catch (e: Exception) {
                            throw BadRequestException(
                                "Birthdate ${company.birthDate} must be of format YYYY-MM-DD for user $userId.",
                                mapOf("userId" to userId),
                                e,
                            )
                        }
                        if (birthDate.year < 1930) {
                            throw BadRequestException(
                                "Year in $birthDate must be >= 1930.",
                                mapOf("userId" to userId),
                            )
                        }
                        if (birthDate.plusYears(13) > LocalDate.now()) {
                            throw BadRequestException(
                                "You have to be at least 13 years to create.",
                                mapOf("userId" to userId),
                            )
                        }
                    }
                    if (user.company?.country != null && company.country != user.company!!.country) {
                        throw BadRequestException(
                            "Country cannot be changed after it is set for $userId.",
                            mapOf("userId" to userId),
                        )
                    }
                    if (company.country != user.company?.country) {
                        feesUpdate = true
                    }
                    user.company = company.copy(
                        id = company.id.nullIfEmpty(),
                        vatId = company.vatId.nullIfEmpty(),
                    )
                }

                // image update
                if (user.image?.id != updates.attributes.image?.id) {
                    user.image = updates.attributes.image
                }

                if (updates.attributes.discord == null && user.discord?.active == true) {
                    // patching can only be used to disconnect discord - connection must be done via DiscordOAuthController
                    user.discord?.active = false
                    pubSub.publish(DiscordConnectionChanged(userId, SubscriberStatusChange.UNSUBSCRIBED))
                }

                // name and bio
                val name = updates.attributes.name?.trim() ?: ""

                // TODO inconsistency with UserCommandService, where `< 3` is required
                if (name.removeNonAscii().length < 2) {
                    throw ValidationException(
                        user.id,
                        "name",
                        name ?: "name was not given",
                        ValidationErrorType.MIN_LENGTH_TWO,
                    )
                }
                if ("herohero" in name.lowercase() && user.email != "<EMAIL>") {
                    throw ValidationException(
                        user.id,
                        "name",
                        name,
                        ValidationErrorType.ILLEGAL_STRING,
                    )
                }
                user.name = name
                if ((updates.attributes.bio?.length ?: 0) > 1500 ||
                    (updates.attributes.bioHtml?.length ?: 0) > 2000 ||
                    (updates.attributes.bioEn?.length ?: 0) > 1500 ||
                    (updates.attributes.bioHtmlEn?.length ?: 0) > 2000
                ) {
                    throw BadRequestException(
                        "Bio too long, maximum 1500, given ${updates.attributes.bio?.length} characters.",
                    )
                }
                user.bio = updates.attributes.bio?.trim() ?: ""
                user.bioHtml = htmlSanitizer.sanitize(updates.attributes.bioHtml?.trim() ?: user.bio)
                user.bioEn = updates.attributes.bioEn?.trim() ?: ""
                user.bioHtmlEn = htmlSanitizer.sanitize(updates.attributes.bioHtmlEn?.trim() ?: user.bioEn)
                if (updates.attributes.language != null) {
                    user.language = updates.attributes.language!!.lowercase()
                }
                user.hasRssFeed = updates.attributes.hasRssFeed

                repository.store(user)

                if (feesUpdate && user.company?.country != null && user.creator.tierId != FREE_SUBSCRIBER_TIER_ID) {
                    val tier = tierRepository[user.creator.tierId]
                    pubSub.publish(
                        FeesUpdateRequested(
                            creatorId = user.id,
                            vatId = user.company?.vatId,
                            tierId = user.creator.tierId,
                            country = user.company!!.country!!,
                            forced = false,
                        ),
                    )
                }
                Response(Status.OK).body(
                    repository.toDto(
                        user = user,
                        details = true,
                        categories = categoriesRepository.list(user.id),
                    ),
                )
            },
        )

    data class ChallengeBody(
        val sub: String,
    )

    @Suppress("unused")
    val routeUserExists: ContractRoute =
        "/v1/challenge".post(
            summary = "Checks whether given user exists.",
            tag = "Users",
            parameters = object {},
            hideFromOpenApi = true,
            responses = listOf(
                Status.NO_CONTENT to Unit,
                Status.NOT_FOUND to Unit,
            ),
            // we don't explicitely mention that this is a Base64-encoded email so that it is not publicly visible
            receiving = ChallengeBody(sub = "Zm9vQGJhci5jeg=="),
            handler = { request, parameters ->
                val subjectEncoded = lens<ChallengeBody>(request).sub
                val email = try {
                    Base64.getDecoder().decode(subjectEncoded).let { String(it) }.lowercase()
                } catch (e: Exception) {
                    log.error("Invalid user identification: $subjectEncoded")
                    throw NotFoundException()
                }
                if (repository.exists(email)) {
                    Response(Status.NO_CONTENT)
                } else {
                    // we must throw NotFound exception and not return status to keep empty messaage
                    throw NotFoundException()
                }
            },
        )

    @Suppress("unused")
    val routeRssFeedUrl: ContractRoute =
        ("/v1/users" / Path.userId().of("creatorId") / "rss-feed").post(
            summary = "Generates a RSS feed url.",
            tag = "Users",
            receiving = null,
            parameters = object {
                val authorization = Header.authorization()
                val isSpotify = Query.boolean().defaulted(
                    "isSpotify",
                    false,
                    "True when this feed is intended for Spotify consumption.",
                )
            },
            responses = listOf(
                Status.OK example FeedUrlResponse("https://feed-url"),
            ),
            handler = { request, parameters, creatorId, _ ->
                val user = repository.get(request)
                val creator = repository.get(creatorId)
                val isSpotify = parameters.isSpotify(request)
                if (isSpotify) {
                    if (user.id != creator.id) {
                        throw ForbiddenException(
                            "You can only generate Spotify RSS feed for yourself.",
                            mapOf("userId" to user.id, "creatorId" to creatorId),
                        )
                    }
                    if (user.bio.isBlank() || user.image == null || user.email == null) {
                        throw BadRequestException(
                            "Cannot generate Spotify RSS feed when bio, email or image are missing.",
                            mapOf("userId" to user.id),
                        )
                    }
                    if (!user.hasSpotifyExport) {
                        throw ForbiddenException(
                            "User ${user.id} does not have Spotify export enabled.",
                            mapOf("userId" to user.id),
                        )
                    }
                }

                if (!isSpotify && !creator.hasRssFeed) {
                    throw ForbiddenException(
                        "Creator $creatorId has disabled RSS feeds.",
                        mapOf("userId" to user.id, "creatorId" to creatorId),
                    )
                }
                val feedUrl = rssFeedsRepository.feedUrl(user, creatorId, isSpotify)
                Response(Status.OK).body(FeedUrlResponse(feedUrl))
            },
        )
}
