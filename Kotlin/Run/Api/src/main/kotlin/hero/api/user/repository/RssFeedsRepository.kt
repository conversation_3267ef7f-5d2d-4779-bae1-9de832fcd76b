package hero.api.user.repository

import hero.baseutils.instantOf
import hero.gcloud.TypedCollectionReference
import hero.jwt.toJwt
import hero.model.RssFeed
import hero.model.User
import java.time.Instant

class RssFeedsRepository(
    private val hostnameServices: String,
    private val collection: TypedCollectionReference<RssFeed>,
) {
    fun feedUrl(
        user: User,
        creatorId: String,
        isSpotify: Boolean,
    ): String {
        // for spotify, we need to have identical links to avoid duplicates for creators
        val iat = if (isSpotify)
            instantOf("2024-04-24T00:00:00Z").epochSecond
        else
            Instant.now().epochSecond
        val jwt = mapOf(
            "u" to user.id,
            "c" to creatorId,
            "t" to iat,
            "s" to isSpotify,
        ).toJwt()
        if (!isSpotify) {
            collection["$creatorId-${user.id}"].set(RssFeed(iat))
        }
        return "${hostnameServices.replace(".herohero.co", "-na.herohero.co")}/rss-feed/?token=$jwt"
    }
}
