package hero.api.invoice.controller

import com.google.cloud.firestore.Query.Direction.DESCENDING
import hero.api.invoice.controller.response.InvoicesDtoIncluded
import hero.api.invoice.controller.response.InvoicesDtoMeta
import hero.api.invoice.controller.response.InvoicesDtoResponse
import hero.api.invoice.controller.response.invoiceDtoExample
import hero.api.invoice.service.InvoiceService
import hero.api.invoice.util.invoiceReportLink
import hero.api.user.controller.UsersJsonApiController.Companion.exampleUserResponseV2
import hero.api.user.repository.UsersRepository
import hero.baseutils.log
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.where
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.enum
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.model.Invoice
import hero.model.InvoiceItemType
import hero.model.UserCompany
import hero.model.toDto
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.boolean
import org.http4k.lens.string

class InvoicesController(
    private val userRepository: UsersRepository,
    private val invoiceRepository: TypedCollectionReference<Invoice>,
    private val invoiceService: InvoiceService,
    private val hostnameServices: String,
) {
    private val parentUser = userRepository.toDto(userRepository.get("infoheroherokamniiih"), false, emptyList())

    @Suppress("unused")
    val routeUserInvoice: ContractRoute by lazy {
        ("/v2/users" / Path.userId().of("userId") / "invoices" / Path.string().of("invoiceId")).get(
            summary = "Get invoice by given id.",
            tag = "Invoices",
            parameters = object {
                val authToken = Query.string().required("authToken", "Authorization token.")
                val filterPartType = Query.enum<InvoiceItemType>().optional(
                    "filterPartType",
                    "Filter by part type. Possible values: ${InvoiceItemType.entries.map { it.name.lowercase() }}",
                )
            },
            responses = listOf(
                Status.OK example invoiceDtoExample,
            ),
            handler = { request, parameters, userId, _, invoiceId ->
                val invoice = invoiceRepository
                    .where(Invoice::userId).isEqualTo(userId)
                    .and(Invoice::invoiceId).isEqualTo(invoiceId)
                    .fetchSingle()
                    ?: throw NotFoundException("Invoice $invoiceId for user $userId was not found.")

                if (parameters.authToken(request) != invoice.authToken) {
                    throw ForbiddenException()
                }
                val filterPartType = parameters.filterPartType(request)

                val invoiceDto = invoice.toDto(
                    filterPartType = filterPartType,
                    parentUserId = parentUser.id,
                    sheetReportLink = invoiceReportLink(hostnameServices, userId, invoiceId, invoice.authToken),
                )
                Response(Status.OK).body(invoiceDto)
            },
        )
    }

    @Suppress("unused")
    val routeUserInvoiceReport: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "invoices" / Path.string().of("invoiceId") / "report").get(
            summary = "Get invoice report by given id.",
            tag = "Invoices",
            parameters = object {
                val authToken = Query.string().required("authToken", "Authorization token.")
                val refresh = Query.boolean().defaulted("refresh", false, "Set true to always generate new report")
            },
            responses = listOf(
                Status.MOVED_PERMANENTLY to Unit,
            ),
            handler = { request, parameters, userId, _, invoiceId, _ ->
                val forceRefresh = parameters.refresh(request)
                val authToken = parameters.authToken(request)
                val invoice = invoiceService.createReportSheet(invoiceId, userId, authToken, forceRefresh)

                val spreadsheetLink = invoice
                    .sheetReportLink
                    ?: error("Missing report link for invoice $invoiceId")

                Response(Status.FOUND)
                    .header("Location", spreadsheetLink)
            },
        )

    @Suppress("unused")
    val routeUserInvoices: ContractRoute by lazy {
        ("/v1/users" / Path.userId().of("userId") / "invoices").get(
            summary = "List users invoices.",
            tag = "Invoices",
            parameters = object {
                val authorization = Header.authorization()
                val id = Query.string().optional("id", "Requests a single invoice.")
                val filterPartType = Query.enum<InvoiceItemType>().optional(
                    "filterPartType",
                    "Filter by part type. Possible values: ${InvoiceItemType.entries.map { it.name.lowercase() }}",
                )
                // TODO paging
            },
            responses = listOf(
                Status.OK example InvoicesDtoResponse(
                    meta = InvoicesDtoMeta(
                        hasNext = false,
                        scrollAfter = "aaaaaaaaaaaa",
                    ),
                    invoices = listOf(invoiceDtoExample),
                    included = InvoicesDtoIncluded(listOf(exampleUserResponseV2)),
                ),
            ),
            handler = { request, parameters, userId, _ ->
                val id = parameters.id(request)
                val user = userRepository.get(request, userId)
                val filterPartType = parameters.filterPartType(request)
                if (user.company?.name == null) {
                    user.company = UserCompany(user.name)
                }
                val invoices = invoiceRepository
                    .where(Invoice::userId).isEqualTo(user.id)
                    .let {
                        if (id != null) {
                            it.and(Invoice::invoiceId).isEqualTo(id)
                        } else {
                            it
                        }
                    }
                    .orderBy(Invoice::timestamp, DESCENDING)
                    .fetchAll()
                    .mapNotNull {
                        try {
                            it.toDto(
                                filterPartType,
                                parentUser.id,
                                invoiceReportLink(hostnameServices, userId, it.invoiceId, it.authToken),
                            )
                        } catch (e: Exception) {
                            log.fatal("Cannot render invoice ${it.invoiceId}", cause = e)
                            null
                        }
                    }
                Response(Status.OK)
                    .body(
                        InvoicesDtoResponse(
                            meta = InvoicesDtoMeta(false, null),
                            invoices = invoices,
                            included = InvoicesDtoIncluded(
                                listOf(
                                    userRepository.toDto(user, true, listOf()),
                                    parentUser,
                                ),
                            ),
                        ),
                    )
            },
        )
    }
}
