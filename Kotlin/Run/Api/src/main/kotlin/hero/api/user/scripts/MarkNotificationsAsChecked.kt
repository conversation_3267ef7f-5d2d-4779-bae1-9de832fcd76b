package hero.api.user.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.firestore
import hero.gcloud.isNotNull
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Notification
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)

    val collection = firestore.typedCollectionOf(Notification)
    val notifications = collection
        .where(Notification::seenAt).isNotNull()
        .fetchAll()
        .filter { it.checkedAt == null && it.seenAt != null }

    val pool = Executors.newFixedThreadPool(30)
    for (notification in notifications) {
        pool.submit {
            val seen = notification.seenAt ?: notification.created
            log.info("Marking ${notification.id} as checked at $seen.")
            collection[notification.id].field(Notification::checkedAt).update(seen)
            if (notification.seenAt == null) {
                collection[notification.id].field(Notification::seenAt).update(seen)
            }
        }
    }
    pool.awaitTermination(Long.MAX_VALUE, TimeUnit.HOURS)
}
