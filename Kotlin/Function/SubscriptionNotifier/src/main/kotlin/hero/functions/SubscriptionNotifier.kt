package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.atStartOfDay
import hero.baseutils.log
import hero.core.logging.Logger
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.jackson.map
import hero.model.CancelledByRole
import hero.model.CouponMethod
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.Notification
import hero.model.NotificationType.CANCELLED_SUBSCRIPTION_BY_CREATOR
import hero.model.NotificationType.CANCELLED_SUBSCRIPTION_ENDED
import hero.model.NotificationType.CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS
import hero.model.NotificationType.CANCELLED_SUBSCRIPTION_OTHER
import hero.model.NotificationType.CANCELLED_SUBSCRIPTION_REFUNDED
import hero.model.NotificationType.CANCELLED_SUBSCRIPTION_REFUSED
import hero.model.NotificationType.NEW_SUBSCRIPTION
import hero.model.StorageEntityType
import hero.model.Subscriber
import hero.model.Tier
import hero.model.topics.EmailPublished
import hero.model.topics.SubscriberStatusChange
import hero.model.topics.SubscriberStatusChange.SUBSCRIBED
import hero.model.topics.SubscriberStatusChanged
import hero.repository.notification.NotificationRepository
import hero.repository.user.UserRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables.NOTIFICATION
import org.jooq.DSLContext
import org.jooq.impl.DSL
import java.time.Clock
import java.time.Instant
import java.time.ZonedDateTime
import hero.gcloud.TypedCollectionReference as TCR

class SubscriptionNotifier(
    systemEnvs: EnvironmentVariables = SystemEnv,
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val hostname: String = systemEnvs.hostname,
    private val production: Boolean = systemEnvs.isProduction,
    private val firestore: FirestoreRef = firestore(systemEnvs.cloudProject, production),
    private val notificationRepository: NotificationRepository = NotificationRepository(lazyContext),
    private val subscribersCollection: TCR<Subscriber> = firestore.typedCollectionOf(Subscriber),
    private val userRepository: UserRepository = UserRepository(lazyContext),
    private val pubSub: PubSub = PubSub(systemEnvs.environment, systemEnvs.cloudProject),
    private val logger: Logger = log,
    private val clock: Clock = Clock.systemUTC(),
) : PubSubSubscriber<SubscriberStatusChanged>(systemEnvs) {
    private val context by lazyContext

    override fun consume(payload: SubscriberStatusChanged) {
        val subscriber = findSubscription(payload.userId, payload.creatorId)
        if (subscriber == null) {
            logger.fatal("No subscription for ${payload.userId}-> ${payload.creatorId} was found. Skipping.")
            return
        }

        // confirm user of successful subscription
        if (payload.doNotNotify) {
            logger.info("Creator ${payload.creatorId} was deleted, not notifying anyone.")
            return
        }

        emailUser(
            userId = payload.userId,
            creatorId = payload.creatorId,
            payload = payload,
            cancelledByCreator = subscriber.cancelledByRole == CancelledByRole.CREATOR,
            tierId = subscriber.tierId,
            couponAppliedForMonths = subscriber.couponAppliedForMonths,
            couponAppliedForDays = subscriber.couponAppliedForDays,
            couponMethod = subscriber.couponMethod,
            couponPercentOff = subscriber.couponPercentOff,
        )

        // notify creator of new subscription
        notificationFactory(
            subscriber = subscriber,
            statusChange = payload.statusChange,
            // cancelledAt was a new field so we need to default to now
            timestamp = if (payload.statusChange == SUBSCRIBED)
                subscriber.subscribed
            else
                (subscriber.cancelAt ?: subscriber.cancelledAt ?: Instant.now(clock)),
        )
    }

    private fun emailUser(
        userId: String,
        creatorId: String,
        couponAppliedForMonths: Long?,
        couponAppliedForDays: Long?,
        couponMethod: CouponMethod?,
        couponPercentOff: Long?,
        payload: SubscriberStatusChanged,
        cancelledByCreator: Boolean,
        tierId: String,
    ) {
        logger.info(
            "Notifying user $userId of ${payload.statusChange} subscription of $creatorId.",
            mapOf("userId" to userId, "creatorId" to creatorId),
        )
        val user = userRepository.getById(userId)
        val creator = userRepository.getById(creatorId)

        if (user.email == null) {
            // not every user might have email
            return
        }

        val isInsufficientFunds = "insufficient funds" in (payload.cancelledReason ?: "")

        // we want to always send this link on devel, on production right hand side is always evaluated.
        val isFreeSubscription = tierId == FREE_SUBSCRIBER_TIER_ID
        val resubscribeLink = if ((!production || (!cancelledByCreator && !payload.refunded)) && !isFreeSubscription) {
            "$hostname/${creator.id}?resubscribeFor=$userId&tier=$tierId"
        } else {
            null
        }

        val tier = Tier.ofId(tierId)

        if (couponMethod == CouponMethod.TRIAL && couponPercentOff == null) {
            error("When couponMethod is TRIAL, couponPercentOff must not be null.")
        }

        val priceDiscountedMonthly = if (couponPercentOff != null)
            tier.priceCents
                .times(100 - couponPercentOff)
                .toBigDecimal()
                .movePointLeft(4)
                .setScale(2)
                .toString()
        else
            null

        pubSub.publish(
            EmailPublished(
                to = user.email!!,
                // https://gitlab.com/-/ide/project/heroheroco/mailgun-templates/tree/main/-/templates/
                template = when (payload.statusChange) {
                    SUBSCRIBED -> "subscribe-successful"
                    SubscriberStatusChange.UNSUBSCRIBED -> "subscribe-cancelled"
                },
                variables = listOf(
                    "user-name" to user.name,
                    "creator-name" to creator.name,
                    "creator-link" to "$hostname/${creator.id}",
                    "user-email" to user.email!!,
                    "apple-hide-my-email" to ("privaterelay.appleid.com" in user.email!!),
                    "refunded" to payload.refunded,
                    "refused" to payload.refused,
                    "insufficient-funds" to isInsufficientFunds,
                    "ended" to payload.ended,
                    "cancelled-by-creator" to cancelledByCreator,
                    "resubscribe-link" to resubscribeLink,
                    "price-monthly" to tier.priceCents.div(100),
                    "price-discounted-monthly" to priceDiscountedMonthly,
                    "currency" to tier.currency.symbol,
                    "coupon-applied-for-months" to couponAppliedForMonths,
                    "coupon-applied-for-days" to couponAppliedForDays,
                    "percent-off" to couponPercentOff,
                    "is-paid-by-coupon" to
                        (couponMethod == CouponMethod.VOUCHER),
                    "is-discount" to
                        (
                            couponMethod == CouponMethod.TRIAL &&
                                couponPercentOff!! < 100L &&
                                couponAppliedForMonths == null &&
                                couponAppliedForDays == null
                        ),
                    "is-free-invite" to
                        (
                            couponMethod == CouponMethod.TRIAL &&
                                couponPercentOff!! == 100L &&
                                couponAppliedForMonths == null &&
                                couponAppliedForDays == null
                        ),
                    "is-trial" to
                        (
                            couponMethod == CouponMethod.TRIAL &&
                                couponPercentOff!! < 100L &&
                                (couponAppliedForMonths != null || couponAppliedForDays != null)
                        ),
                    "is-free-trial" to
                        (
                            couponMethod == CouponMethod.TRIAL &&
                                couponPercentOff!! == 100L &&
                                (couponAppliedForMonths != null || couponAppliedForDays != null)
                        ),
                ),
                language = user.language,
            ),
        )
    }

    private fun notificationFactory(
        subscriber: Subscriber,
        statusChange: SubscriberStatusChange,
        timestamp: Instant,
    ) {
        // when subscribing, we notify creators, when unsubscribing, we notify users
        val notificationUserId = if (statusChange == SUBSCRIBED) subscriber.creatorId else subscriber.userId
        val notificationActorId = if (statusChange == SUBSCRIBED) subscriber.userId else subscriber.creatorId

        if (statusChange == SUBSCRIBED && tryToAddToExistingNotification(subscriber.creatorId, subscriber.userId)) {
            return
        }

        val notificationType = when {
            statusChange == SUBSCRIBED -> NEW_SUBSCRIPTION
            "insufficient" in (subscriber.cancelledReason ?: "") -> CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS
            subscriber.cancelAtPeriodEnd -> CANCELLED_SUBSCRIPTION_ENDED
            subscriber.refused -> CANCELLED_SUBSCRIPTION_REFUSED
            subscriber.refunded -> CANCELLED_SUBSCRIPTION_REFUNDED
            subscriber.cancelledByRole == CancelledByRole.CREATOR -> CANCELLED_SUBSCRIPTION_BY_CREATOR
            else -> CANCELLED_SUBSCRIPTION_OTHER
        }

        val notification = Notification(
            userId = notificationUserId,
            type = notificationType,
            actorIds = mutableListOf(notificationActorId),
            objectType = StorageEntityType.USER,
            objectId = notificationActorId,
            created = timestamp,
            timestamp = timestamp,
        )
        logger.info("Notifying with new subscription.", notification.map())
        notificationRepository.save(notification)
    }

    private fun findSubscription(
        userId: String,
        creatorId: String,
    ): Subscriber? =
        subscribersCollection
            .where(Subscriber::userId).isEqualTo(userId)
            .and(Subscriber::creatorId).isEqualTo(creatorId)
            .fetchAll()
            .minByOrNull { if (it.status.isActive) 0 else 1 }

    // returns true if added to an existing notification
    private fun tryToAddToExistingNotification(
        creatorId: String,
        userId: String,
    ): Boolean {
        val updatedRows = context
            .update(NOTIFICATION)
            .set(NOTIFICATION.CREATED_AT, Instant.now(clock))
            .setNull(NOTIFICATION.SEEN_AT)
            .set(NOTIFICATION.ACTOR_IDS, DSL.arrayAppend(NOTIFICATION.ACTOR_IDS, userId))
            .where(NOTIFICATION.USER_ID.eq(creatorId))
            .and(NOTIFICATION.TYPE.eq(NEW_SUBSCRIPTION.name))
            .and(NOTIFICATION.CREATED_AT.gt(ZonedDateTime.now(clock).atStartOfDay().toInstant()))
            .returningResult(NOTIFICATION.ID)
            .fetch()

        assert(updatedRows.size < 2) { "Updated more notifications for creator $creatorId and user $userId" }

        if (updatedRows.size == 1) {
            logger.info(
                "Appending to already existing notification ${updatedRows.first().value1()}",
                mapOf("userId" to userId),
            )
        }

        return updatedRows.size != 0
    }
}
