package hero.stripe.service

import com.stripe.exception.InvalidRequestException
import com.stripe.model.Account
import com.stripe.model.BalanceTransaction
import com.stripe.model.Charge
import com.stripe.model.Coupon
import com.stripe.model.Customer
import com.stripe.model.Invoice
import com.stripe.model.LoginLink
import com.stripe.model.PaymentIntent
import com.stripe.model.Payout
import com.stripe.model.Price
import com.stripe.model.Refund
import com.stripe.model.StripeCollection
import com.stripe.model.Subscription
import com.stripe.net.RequestOptions
import com.stripe.param.AccountListParams
import com.stripe.param.AccountLoginLinkCreateParams
import com.stripe.param.AccountRejectParams
import com.stripe.param.AccountUpdateParams
import com.stripe.param.AccountUpdateParams.Settings.Payouts
import com.stripe.param.BalanceTransactionListParams
import com.stripe.param.ChargeListParams
import com.stripe.param.ChargeRetrieveParams
import com.stripe.param.ChargeUpdateParams
import com.stripe.param.CouponCreateParams
import com.stripe.param.CouponCreateParams.Duration
import com.stripe.param.CustomerCreateParams
import com.stripe.param.CustomerUpdateParams
import com.stripe.param.InvoiceRetrieveParams
import com.stripe.param.PaymentIntentCreateParams
import com.stripe.param.PayoutListParams
import com.stripe.param.RefundRetrieveParams
import com.stripe.param.RefundUpdateParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.randomString
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.PubSub
import hero.jackson.map
import hero.jackson.parseEnum
import hero.model.Currency
import hero.model.StripeRequirements
import hero.model.Tier
import hero.model.User
import hero.model.eurCountries
import hero.model.topics.EmailPublished
import java.time.Instant
import hero.model.CouponMethod as CM
import hero.model.CouponTarget as CT

class StripeService(
    private val clients: StripeClients,
    private val pubSub: PubSub?,
) {
    fun createCustomer(
        user: User,
        currency: Currency,
    ): Customer {
        val params = CustomerCreateParams.builder()
            // abusing for user.id
            .setDescription(user.id)
            .setName(user.name + " / " + currency)
            .apply {
                user.email?.let { email -> setEmail(email) }
            }
            .putMetadata("currency", currency.name)
            .build()

        return retry {
            clients[currency].customers().create(params)
        }
    }

    fun subscription(
        subscriptionId: String,
        currency: Currency,
    ): Subscription =
        retry {
            clients[currency].subscriptions().retrieve(subscriptionId)
        }

    fun deleteCustomer(
        customerId: String,
        currency: Currency,
        reason: String,
    ) {
        retry {
            try {
                clients[currency]
                    .customers()
                    .retrieve(customerId)
                    .update(CustomerUpdateParams.builder().putMetadata("deletedReason", reason).build())
                    .delete()
            } catch (e: InvalidRequestException) {
                // avoid spamming logs if the customer is already deleted
                if ("No such customer" in (e.message ?: "")) {
                    return@retry
                }
                throw e
            }
        }
    }

    fun listAccounts(currency: Currency): StripeCollection<Account> =
        retry {
            clients[currency].accounts().list(AccountListParams.builder().setLimit(MAX_PERCENT_VALUE).build())
        }

    fun listPayouts(
        accountId: String,
        currency: Currency,
        apply: PayoutListParams.Builder.() -> PayoutListParams.Builder = { this },
    ): StripeCollection<Payout> =
        retry {
            clients[currency].payouts().list(
                PayoutListParams
                    .builder()
                    .let { it.apply() }
                    .build(),
                accountId.stripeAccountRequestOptions(),
            )
        }

    fun getPayout(
        accountId: String,
        payoutId: String,
        currency: Currency,
    ): Payout =
        retry {
            clients[currency].payouts().retrieve(payoutId, accountId.stripeAccountRequestOptions())
        }

    fun getCharge(
        chargeId: String,
        currency: Currency,
    ): Charge =
        retry {
            clients[currency].charges().retrieve(chargeId)
        }

    /** see: https://linear.app/herohero/issue/HH-3217/attach-payout-id-to-transactions-when-paid-out */
    fun markTransactionAsPaidOut(
        transaction: BalanceTransaction,
        accountId: String,
        currency: Currency,
        payoutId: String,
    ) {
        retry {
            val src = transaction.sourceObject!!
            when (src) {
                is Refund -> clients[currency].refunds().update(
                    transaction.source,
                    RefundUpdateParams.builder().putMetadata("payoutId", payoutId).build(),
                    RequestOptions.builder().setStripeAccount(accountId).build(),
                )
                is Charge -> clients[currency].charges().update(
                    transaction.source,
                    ChargeUpdateParams.builder().putMetadata("payoutId", payoutId).build(),
                    RequestOptions.builder().setStripeAccount(accountId).build(),
                )
                else -> {}
            }
        }
    }

    fun markChargeAsSeen(charge: Charge) {
        if (charge.metadata["seen"] != null) {
            return
        }
        try {
            retry {
                charge.update(ChargeUpdateParams.builder().putMetadata("seen", Instant.now().toString()).build())
            }
        } catch (e: InvalidRequestException) {
            log.fatal("Cannot mark ${charge.id} as seen: ${e.message}")
        }
    }

    fun getOriginalChargeByGroup(
        groupId: String,
        currency: Currency,
    ): Charge {
        val charges = clients[currency].charges()
            .list(ChargeListParams.builder().setTransferGroup(groupId).build())
            .data

        if (charges.isEmpty()) {
            error("There was no charge found for group $groupId.")
        }
        if (charges.size > 2) {
            error("There should never be more charges for single group ($groupId): ${charges.map { it.id }}")
        }

        return charges.first()
    }

    fun listPayoutTransactions(
        sourceAccountId: String,
        payoutId: String,
        currency: Currency,
    ): Iterable<BalanceTransaction> =
        clients[currency].balanceTransactions().list(
            BalanceTransactionListParams.builder()
                .setLimit(100)
                .setPayout(payoutId)
                .addAllExpand(
                    listOf(
                        // regular charges
                        "data.source.source_transfer.source_transaction",
                        // refunds
                        "data.source.charge.source_transfer",
                        // Fetching `source_transaction` below would be very helpful for processing
                        // refunds, however 4-level expands are forbidden by Stripe. Therefore,
                        // we need to read these manually.
                        // "data.source.charge.source_transfer.source_transaction",
                    ),
                )
                .build(),
            sourceAccountId
                .stripeAccountRequestOptions(),
        ).autoPagingIterable()

    data class CardDeclinedResponse(
        val message: String?,
        val charge: String?,
        val declineCode: String?,
        val code: String?,
        val param: String?,
        val requestId: String?,
    )

    fun getAccount(
        stripeAccountId: String,
        currency: Currency,
    ): Account =
        retry {
            clients[currency].accounts().retrieve(stripeAccountId)
        }

    fun getLoginLink(
        stripeAccountId: String,
        currency: Currency,
    ): LoginLink =
        retry {
            clients[currency].accounts().loginLinks().create(
                stripeAccountId,
                AccountLoginLinkCreateParams.builder().build(),
                RequestOptions.getDefault(),
            )
        }

    fun requirements(
        user: User,
        source: String,
        currency: Currency,
    ): StripeRequirements {
        if (user.creator.stripeAccountId == null) {
            throw BadRequestException("User does not have yet stripeAccountId associated.")
        }
        val account = getAccount(user.creator.stripeAccountId!!, currency)
        val requirements = account.requirements()
        log.info(
            "Stripe user account verification response from $source, valid: ${requirements.valid}.",
            requirements.map() + mapOf("userId" to user.id),
        )
        return requirements
    }

    fun onboardingFinished(
        user: User,
        currency: Currency,
    ): Boolean = user.creator.stripeAccountId?.let { getAccount(it, currency).detailsSubmitted } ?: false

    private fun Account.requirements(): StripeRequirements =
        StripeRequirements(
            stripeAccountId = id,
            deleted = deleted == true,
            currentlyDue = requirements.currentlyDue,
            disabledReason = requirements.disabledReason,
            eventuallyDue = requirements.eventuallyDue,
            pastDue = requirements.pastDue,
            errors = requirements.errors.map { it.code },
            pendingVerification = requirements.pendingVerification,
        )

    // when account has some balance, it cannot be properly deleted nor cancelled –
    // for that reason, we only set manual payouts to "pretend" account to be deleted.
    private fun manualPayouts(
        reason: String,
        deletedBy: String,
    ): AccountUpdateParams =
        AccountUpdateParams.builder()
            .setSettings(
                AccountUpdateParams.Settings.builder().setPayouts(
                    Payouts.builder().setSchedule(
                        Payouts.Schedule.builder().setInterval(Payouts.Schedule.Interval.MANUAL).build(),
                    ).build(),
                ).build(),
            )
            .putMetadata("deletedAt", Instant.now().toString())
            .putMetadata("deletedReason", reason)
            .putMetadata("deletedBy", "https://herohero.co/$deletedBy")
            .build()

    fun rejectAccount(
        accountId: String,
        currency: Currency,
        reason: String,
        deletedBy: String,
    ) {
        retry {
            val account = clients[currency].accounts().retrieve(accountId)
            account.update(manualPayouts(reason, deletedBy))
            account.reject(AccountRejectParams.builder().setReason("terms_of_service").build())
        }
    }

    fun deleteAccount(
        accountId: String,
        currency: Currency,
        reason: String,
        deletedBy: String,
    ) {
        retry {
            val account = clients[currency].accounts().retrieve(accountId)
            account.update(manualPayouts(reason, deletedBy))
            account.delete()
                .let { log.info("Deleted account $accountId with result ${it.deleted}.") }
        }
    }

    fun charges(
        customerId: String,
        currency: Currency,
        startingAfter: String?,
        pageLimit: Long = PAGINATION_LIMIT,
    ): Pair<List<Charge>, Boolean> {
        val params = ChargeListParams.builder()
            .setStartingAfter(startingAfter)
            .setLimit(pageLimit)
            .setCustomer(customerId)
            .addExpand("data.payment_intent.payment_method")
            .build()
        val chargesResponse = clients[currency].charges().list(params)
        val hasNext = chargesResponse.hasMore
        val charges = chargesResponse.data
        return charges.take(pageLimit.toInt()) to hasNext
    }

    fun charge(
        chargeId: String,
        currency: Currency,
        accountId: String? = null,
    ): Charge =
        retry {
            clients[currency].charges().retrieve(
                chargeId,
                ChargeRetrieveParams.builder().addAllExpand(
                    listOf(
                        "source_transfer.source_transaction",
                        "transfer.destination_payment",
                    ),
                ).build(),
                if (accountId != null) RequestOptions.builder().setStripeAccount(accountId).build() else null,
            )
        }

    fun refund(
        refundId: String,
        currency: Currency,
        accountId: String? = null,
    ): Refund =
        retry {
            clients[currency].refunds().retrieve(
                refundId,
                RefundRetrieveParams.builder().addAllExpand(
                    listOf("charge.source_transfer.source_transaction"),
                ).build(),
                if (accountId != null) RequestOptions.builder().setStripeAccount(accountId).build() else null,
            )
        }

    fun customer(
        customerId: String,
        currency: Currency,
    ): Customer =
        retry {
            clients[currency].customers().retrieve(customerId)
        }

    fun invoice(
        invoiceId: String,
        currency: Currency,
        expand: List<String>,
    ): Invoice =
        retry {
            clients[currency].invoices().retrieve(
                invoiceId,
                InvoiceRetrieveParams.builder().addAllExpand(expand).build(),
                RequestOptions.getDefault(),
            )
        }

    fun paymentIntent(
        paymentIntentId: String,
        currency: Currency,
    ): PaymentIntent =
        retry {
            clients[currency].paymentIntents().retrieve(paymentIntentId)
        }

    fun createPaymentIntent(params: PaymentIntentCreateParams): PaymentIntent =
        retry {
            try {
                clients[parseEnum<Currency>(params.currency)!!].paymentIntents().create(params)
            } catch (e: InvalidRequestException) {
                val message = (e.userMessage ?: e.message ?: "") + " [${e.code}]"
                if ("detached from a Customer" in message) {
                    throw ForbiddenException(e.userMessage)
                }
                if ("It's possible this PaymentMethod exists on one of your connected accounts" in message) {
                    throw ForbiddenException(e.userMessage)
                }
                throw e
            }
        }

    fun generateNextCouponId(): String {
        while (true) {
            val couponId = randomString(10).uppercase()
            // check if coupon with given id exists and return its value if not
            getCouponOrNull(couponId, Currency.EUR)
                ?: getCouponOrNull(couponId, Currency.USD)
                ?: return couponId
        }
    }

    fun fraudWarning(
        customerId: String,
        currency: Currency,
        reason: String,
    ) {
        val customer = retry {
            clients[currency].customers().retrieve(customerId)
        }

        log.warn("Warning about potential fraud of $customerId (${customer.name}): $reason")

        if (customer.metadata["fraudWarning"] != null) {
            // we don't want to repetitively spam admins when already notified
            return
        }

        retry {
            customer.update(
                CustomerUpdateParams.builder()
                    .putMetadata("fraudWarning", Instant.now().toString())
                    .build(),
            )
        }

        pubSub?.publish(
            EmailPublished(
                to = if (SystemEnv.isProduction) "<EMAIL>" else "<EMAIL>",
                template = "fraud-warning",
                variables = listOf(
                    "customer-name" to customer.name,
                    "stripe-link" to "https://dashboard.stripe.com/customers/${customer.id}",
                    "reason" to reason,
                ),
                language = "en",
            ),
        )
    }

    fun createCoupon(
        purchasedByUserId: String,
        couponId: String,
        creatorId: String?,
        tier: Tier?,
        price: Price?,
        percentOff: Int?,
        currency: Currency,
        months: Int?,
        days: Int?,
        campaign: String,
        redeemBy: Instant? = null,
        redemptions: Int = 1,
        extraMetadata: Map<String, String> = mapOf(),
    ): Coupon =
        retry {
            if (days != null && percentOff != 100) {
                error("When creating n-day trials, the percentOff must be 100.")
            }
            if (creatorId == null) {
                // https://gitlab.com/heroheroco/backend/-/merge_requests/542
                throw NotImplementedError("We don't support general Herohero vouchers yet.")
            }
            if (months == null && days == null && (tier != null || percentOff == null)) {
                error("For paid coupons, `months` or `days` must always be given.")
            }
            clients[currency].coupons().create(
                CouponCreateParams.builder()
                    .setId(couponId)
                    .setDuration(
                        when {
                            months != null && days != null -> error("Only one of `months` or `days` must be given.")
                            months != null -> Duration.REPEATING
                            days != null -> Duration.ONCE
                            else -> Duration.FOREVER
                        },
                    )
                    .setDurationInMonths(months?.toLong())
                    .setMaxRedemptions(redemptions.toLong())
                    .setRedeemBy(redeemBy?.epochSecond)
                    .let {
                        if (price != null) {
                            it.setAppliesTo(
                                CouponCreateParams.AppliesTo.builder()
                                    .addProduct(price.product)
                                    .build(),
                            )
                        } else {
                            it
                        }
                    }
                    .let {
                        if (days != null) {
                            it.setPercentOff("0.01".toBigDecimal())
                        } else if (percentOff != null) {
                            it.setPercentOff(percentOff.toBigDecimal())
                        } else {
                            if (tier == null) {
                                error("Tier must be given if percentOff is null.")
                            }
                            // this is not a total sum, but monthly fee off
                            it.setAmountOff(tier.priceCents.toLong())
                                .setCurrency(tier.currency.name.lowercase())
                        }
                    }
                    .setMetadata(
                        mapOf(
                            "purchasedByUserId" to purchasedByUserId,
                            // @DEPRECATED use couponTarget
                            "couponType" to if (creatorId != null) CT.CREATOR.name else CT.HEROHERO.name,
                            "couponTarget" to if (creatorId != null) CT.CREATOR.name else CT.HEROHERO.name,
                            "couponMethod" to if (percentOff != null) CM.TRIAL.name else CM.VOUCHER.name,
                            "creatorId" to creatorId,
                            "tierId" to tier?.id,
                            "months" to months?.toString(),
                            // https://linear.app/herohero/issue/HH-1918/1-week-long-trials
                            "days" to days?.toString(),
                            "campaign" to campaign,
                        ) + extraMetadata,
                    )
                    .build(),
            )
        }

    fun getCouponOrNull(
        couponId: String,
        currency: Currency,
    ): Coupon? =
        try {
            clients[currency].coupons().retrieve(couponId)
        } catch (e: InvalidRequestException) {
            if ("No such coupon" in e.message!!) {
                null
            } else {
                throw e
            }
        }
}

fun String.stripeAccountRequestOptions(): RequestOptions = RequestOptions.builder().setStripeAccount(this).build()

fun inferOnBehalfOf(
    currency: Currency,
    country: String?,
    stripeAccountIdOnBehalfOf: String?,
) = if (currency == Currency.USD || (country ?: "CZ") !in eurCountries)
    stripeAccountIdOnBehalfOf
else
    null

private const val PAGINATION_LIMIT = 10L

fun <T> nullIfNotFound(block: () -> T): T? =
    try {
        block()
    } catch (e: InvalidRequestException) {
        if ("No such" in e.message!!) {
            null
        } else {
            throw e
        }
    }
