package hero.auth.service

import hero.baseutils.plus
import hero.exceptions.http.UnauthorizedException
import hero.jwt.JwtUser
import hero.jwt.TokenType
import hero.jwt.toJwt
import hero.model.Session
import hero.model.SignInProvider.APPLE
import hero.model.SignInProvider.PASSWORD
import hero.sql.jooq.Tables.SESSION
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import hero.test.time.TestClock
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID
import kotlin.test.assertNotNull
import kotlin.time.Duration.Companion.seconds

class RefreshTokenCommandServiceIT : IntegrationTest() {
    @Test
    fun `should generate new refresh token and create a new active session`() {
        val now = Instant.ofEpochSecond(**********)
        val clock = TestClock(now)
        val underTest = RefreshTokenCommandService(
            TestRepositories.sessionRepository,
            TestCollections.usersCollection,
            lazyTestContext,
            clock,
        )
        testHelper.createUser("cestmir")

        val refreshToken = underTest.execute(
            GenerateRefreshToken(
                "cestmir",
                120,
                "Mozilla/5.0",
                "*************",
                "Prague, Prague, Czechia",
                PASSWORD,
                "deviceId",
            ),
        )

        assertThat(TestRepositories.sessionRepository.getById(refreshToken.sessionId)).isEqualTo(
            Session(
                id = refreshToken.sessionId,
                userId = "cestmir",
                userAgent = "Mozilla/5.0",
                createdAt = now,
                refreshedAt = now,
                revoked = false,
                deviceId = "deviceId",
                ipAddress = "*************",
                signInProvider = PASSWORD,
                signInLocation = "Prague, Prague, Czechia",
            ),
        )
    }

    @Test
    fun `should rotate the refresh token and update session's refreshedAt field, and deviceId if not set`() {
        // given
        val now = Instant.ofEpochSecond(**********)
        val clock = TestClock(now)
        val underTest = RefreshTokenCommandService(
            TestRepositories.sessionRepository,
            TestCollections.usersCollection,
            lazyTestContext,
            clock,
        )
        testHelper.createUser("cestmir")
        val generateCommand = GenerateRefreshToken(
            "cestmir",
            Instant.now().plusSeconds(100).epochSecond,
            "Mozilla/5.0",
            "*************",
            "Prague, Prague, Czechia",
            APPLE,
        )
        val refreshToken = underTest.execute(generateCommand)
        assertThat(TestRepositories.sessionRepository.getById(refreshToken.sessionId).deviceId).isNull()

        // when
        clock += 5.seconds
        val rotateCommand = RotateRefreshToken(refreshToken.token, 120, 10, "Mozilla/5.0", "*************", "deviceId")
        val (newRefreshToken, jwtUser) = underTest.execute(rotateCommand)

        // then
        val sessionId = newRefreshToken.sessionId
        assertNotNull(sessionId)
        assertThat(TestRepositories.sessionRepository.getById(sessionId)).isEqualTo(
            Session(
                id = sessionId,
                userId = "cestmir",
                userAgent = "Mozilla/5.0",
                createdAt = now,
                refreshedAt = now + 5.seconds,
                revoked = false,
                deviceId = "deviceId",
                ipAddress = "*************",
                signInLocation = "Prague, Prague, Czechia",
                // signInProvider should be still the same
                signInProvider = APPLE,
            ),
        )
        assertThat(jwtUser).isEqualTo(JwtUser("cestmir", 10, 0, sessionId))
    }

    @Test
    fun `should rotate the refresh token, but shouldn't change deviceId if it's already set`() {
        // given
        val underTest = RefreshTokenCommandService(
            TestRepositories.sessionRepository,
            TestCollections.usersCollection,
            lazyTestContext,
        )
        testHelper.createUser("cestmir")
        val expiresAt = Instant.now().plusSeconds(100).epochSecond
        val generateCommand = GenerateRefreshToken(
            "cestmir",
            expiresAt,
            "Mozilla/5.0",
            "*************",
            "Prague, Prague, Czechia",
            PASSWORD,
            "deviceId",
        )
        val refreshToken = underTest.execute(generateCommand)

        // when
        val rotateCommand = RotateRefreshToken(
            refreshToken.token,
            120,
            10,
            "Mozilla/5.0",
            "*************",
            "newDeviceId",
        )
        val (newRefreshToken) = underTest.execute(rotateCommand)

        assertThat(TestRepositories.sessionRepository.getById(newRefreshToken.sessionId).deviceId).isEqualTo("deviceId")
    }

    // sessions should also be created for users that already logged in and are just refreshing their tokens
    // these users are not going to call the `generate` method.
    @Disabled("This test is no longer true since we expect that every refresh token should have a session")
    @Test
    fun `should refresh the token and create new session entity - backward compatibility`() {
        // given
        val now = Instant.ofEpochSecond(**********)
        val clock = TestClock(now)
        val underTest = RefreshTokenCommandService(
            TestRepositories.sessionRepository,
            TestCollections.usersCollection,
            lazyTestContext,
            clock,
        )
        testHelper.createUser("cestmir")
        val refreshToken = JwtUser("cestmir", Instant.now().plusSeconds(100).epochSecond, 0)
            .toJwt(type = TokenType.REFRESH)

        // when
        val rotateTokenCommand = RotateRefreshToken(refreshToken, 120, 10, "Mozilla/5.0", "deviceId")
        val (newRefreshToken, jwtUser) = underTest.execute(rotateTokenCommand)

        // then
        val sessionId = newRefreshToken.sessionId
        assertNotNull(sessionId)
        assertThat(TestRepositories.sessionRepository.getById(sessionId)).isEqualTo(
            Session(
                id = sessionId,
                userId = "cestmir",
                userAgent = "Mozilla/5.0",
                // refreshedAt and createdAt should be equal, this way we know, it's fresh new session
                createdAt = now,
                refreshedAt = now,
                revoked = false,
                deviceId = "deviceId",
                ipAddress = "*************",
                signInLocation = null,
                // signInProvider is going to be null in this case since we have no way to know which sign in was used
                signInProvider = null,
            ),
        )
        assertThat(jwtUser).isEqualTo(JwtUser("cestmir", 10, 0, sessionId))
    }

    @Test
    fun `cannot refresh tokens for sessions that were revoked`() {
        val underTest = RefreshTokenCommandService(
            TestRepositories.sessionRepository,
            TestCollections.usersCollection,
            lazyTestContext,
        )

        testHelper.createUser("cestmir")
        val expiresAt = Instant.now().plusSeconds(100).epochSecond
        val generateCommand = GenerateRefreshToken(
            "cestmir",
            expiresAt,
            "Mozilla/5.0",
            "*************",
            "Prague, Prague, Czechia",
            PASSWORD,
        )
        val refreshToken = underTest.execute(generateCommand)

        testContext
            .update(SESSION)
            .set(SESSION.REVOKED, true)
            .where(SESSION.ID.eq(UUID.fromString(refreshToken.sessionId)))
            .execute()

        assertThatExceptionOfType(UnauthorizedException::class.java).isThrownBy {
            underTest.execute(RotateRefreshToken(refreshToken.token, 120, 10, "Mozilla/5.0", "*************"))
        }
    }

    @Test
    fun `creating new session on a device when there is active session on the device, should revoke the old session`() {
        // given
        val now = Instant.ofEpochSecond(**********)
        val clock = TestClock(now)
        val underTest = RefreshTokenCommandService(
            TestRepositories.sessionRepository,
            TestCollections.usersCollection,
            lazyTestContext,
            clock,
        )
        val oldSessionOnSameDevice = testHelper.createSession("cestmir", deviceId = "deviceId")
        val sessionOnAnotherDevice = testHelper.createSession("cestmir", deviceId = "anotherDeviceId")

        // when
        val refreshToken = underTest.execute(
            GenerateRefreshToken(
                "cestmir",
                10,
                "Mozilla/5.0",
                "*************",
                "Prague, Prague, Czechia",
                PASSWORD,
                "deviceId",
            ),
        )

        // then
        assertThat(TestRepositories.sessionRepository.getById(oldSessionOnSameDevice.id).revoked).isTrue()
        assertThat(TestRepositories.sessionRepository.getById(refreshToken.sessionId).revoked).isFalse()

        // session on another device is untouched
        assertThat(TestRepositories.sessionRepository.getById(sessionOnAnotherDevice.id).revoked).isFalse()
    }
}
