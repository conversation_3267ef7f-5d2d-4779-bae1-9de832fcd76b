package hero.http4k.serverless

import com.google.cloud.logging.Synchronicity
import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.sentry.initializeSentry
import hero.http4k.config.notFoundThrower
import hero.http4k.config.onRequest
import org.http4k.contract.ContractRoute
import org.http4k.contract.contract
import org.http4k.core.Filter
import org.http4k.core.HttpHandler
import org.http4k.core.NoOp
import org.http4k.core.then
import org.http4k.routing.routes
import org.http4k.serverless.AppLoader

abstract class CloudHttpFunction(
    private val environmentVariables: EnvironmentVariables = SystemEnv,
) : AppLoader {
    init {
        if (environmentVariables.environment != "test") {
            // https://github.com/googleapis/java-logging/issues/432
            log.synchronicity = Synchronicity.SYNC

            initializeSentry(SystemEnv.environment, mapOf("cf.name" to this::class.java.simpleName))
        }
    }

    protected abstract fun contractRoutes(): List<ContractRoute>

    override fun invoke(envs: Map<String, String>): HttpHandler =
        if (environmentVariables.environment != "test") {
            onRequest(environmentVariables.isProduction, isLocalHost = false)
        } else {
            Filter.NoOp
        }
            .then(notFoundThrower())
            .then(routes(contract { routes += contractRoutes() }))
}
