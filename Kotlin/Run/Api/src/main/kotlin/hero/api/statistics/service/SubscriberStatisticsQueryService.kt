package hero.api.statistics.service

import hero.baseutils.plusMinutes
import hero.exceptions.http.BadRequestException
import hero.sql.jooq.queries.FetchDailySubscriberStats
import hero.sql.jooq.tables.DailySubscriberStatistics.DAILY_SUBSCRIBER_STATISTICS
import org.jooq.DSLContext
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

class SubscriberStatisticsQueryService(
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    fun execute(query: GetSubscriberStatistics): List<SubscriberStatsData> {
        // allow one minute delta
        val now = Instant.now().plusMinutes(1)
        if (query.to.isAfter(now)) {
            throw BadRequestException("Cannot query stats into the future: ${query.to} > $now")
        }

        if (query.from.isAfter(query.to)) {
            throw BadRequestException("From ${query.from} cannot be after ${query.to}.")
        }

        val to = query.to.toLocalDate()
        val dailyStats = context
            .select(
                DAILY_SUBSCRIBER_STATISTICS.DATE,
                DAILY_SUBSCRIBER_STATISTICS.CREATOR_ID,
                DAILY_SUBSCRIBER_STATISTICS.TOTAL_SUBSCRIBERS,
                DAILY_SUBSCRIBER_STATISTICS.ACTIVE_SUBSCRIBERS,
                DAILY_SUBSCRIBER_STATISTICS.SUBSCRIBED,
                DAILY_SUBSCRIBER_STATISTICS.UNSUBSCRIBED,
                DAILY_SUBSCRIBER_STATISTICS.TOTAL_INCOME_CENTS,
            )
            .from(DAILY_SUBSCRIBER_STATISTICS)
            .where(
                DAILY_SUBSCRIBER_STATISTICS.CREATOR_ID.eq(query.creatorId)
                    .and(DAILY_SUBSCRIBER_STATISTICS.DATE.between(query.from.toLocalDate(), to)),
            )
            .orderBy(DAILY_SUBSCRIBER_STATISTICS.DATE)
            .fetch()
            .map {
                SubscriberStatsData(
                    date = it[DAILY_SUBSCRIBER_STATISTICS.DATE],
                    totalSubscribers = it[DAILY_SUBSCRIBER_STATISTICS.TOTAL_SUBSCRIBERS],
                    activeSubscribers = it[DAILY_SUBSCRIBER_STATISTICS.ACTIVE_SUBSCRIBERS],
                    subscribed = it[DAILY_SUBSCRIBER_STATISTICS.SUBSCRIBED],
                    unsubscribed = it[DAILY_SUBSCRIBER_STATISTICS.UNSUBSCRIBED],
                    totalIncomeCents = it[DAILY_SUBSCRIBER_STATISTICS.TOTAL_INCOME_CENTS],
                )
            }

        val fetchedDates = dailyStats.map { it.date }
        if (fetchedDates.contains(to)) {
            return dailyStats
        }

        val from = fetchedDates.maxOrNull()?.plusDays(1)?.toInstant() ?: query.from
        val calculatedDailyStats = FetchDailySubscriberStats.query(context, query.creatorId, from, query.to)
            .orderBy(FetchDailySubscriberStats.subscriptionDate)
            .fetch()
            .map {
                SubscriberStatsData(
                    date = it.value1(),
                    totalSubscribers = it[FetchDailySubscriberStats.totalSubscriptions],
                    activeSubscribers = it[FetchDailySubscriberStats.totalActiveSubscriptions],
                    subscribed = it[FetchDailySubscriberStats.createdOnDay],
                    unsubscribed = it[FetchDailySubscriberStats.cancelledOnDay],
                    totalIncomeCents = it[FetchDailySubscriberStats.totalIncomeCents],
                )
            }

        return dailyStats + calculatedDailyStats
    }
}

data class GetSubscriberStatistics(
    val creatorId: String,
    val from: Instant,
    val to: Instant,
    val includeCanceled: Boolean = true,
)

data class SubscriberStatsData(
    val date: LocalDate,
    val totalSubscribers: Int,
    val activeSubscribers: Int,
    val subscribed: Int,
    val unsubscribed: Int,
    val totalIncomeCents: Int,
)

private fun Instant.toLocalDate() = LocalDate.ofInstant(this, ZoneOffset.UTC)

private fun LocalDate.toInstant() = this.atStartOfDay().toInstant(ZoneOffset.UTC)
