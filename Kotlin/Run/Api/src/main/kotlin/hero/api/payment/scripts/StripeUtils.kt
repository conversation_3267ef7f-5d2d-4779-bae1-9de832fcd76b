@file:Suppress("ktlint:standard:filename")

package hero.api.payment.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.systemEnv
import hero.gcloud.FirestoreRef
import hero.gcloud.firestore
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeService

fun initializeStripeScript(production: Boolean = false): StripeScriptData {
    val projectId = SystemEnv.cloudProject
    val firestore = firestore(SystemEnv.cloudProject, production)
    val stripeClients = StripeClients(
        // this is a hacky way to read Stripe production variable when running devel code locally
        keysEu = if (production) systemEnv("STRIPE_API_KEY_EU_PROD") else SystemEnv.stripeKeyEu,
        // TODO once migrated to US Stripe, the above will be needed here too
        keysUs = SystemEnv.stripeKeyUs,
        production = production,
    )
    val service = StripeService(clients = stripeClients, pubSub = null)
    return StripeScriptData(firestore, production, projectId, service, stripeClients)
}

data class StripeScriptData(
    val firestore: FirestoreRef,
    val production: Boolean,
    val projectId: String,
    val stripe: StripeService,
    val clients: StripeClients,
)
