package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.logging.Logger
import hero.gcloud.PubSub
import hero.model.topics.Daily
import hero.model.topics.WriteSubscriberStatisticsRequested
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.tables.Subscription.SUBSCRIPTION
import org.jooq.DSLContext
import org.jooq.impl.DSL
import java.time.Instant
import java.time.LocalDate

@Suppress("unused")
class DailyActiveCreatorProcessor(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val pubsub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
    private val logger: Logger = log,
    systemEnv: EnvironmentVariables = SystemEnv,
) : PubSubSubscriber<Daily>(systemEnv) {
    // this must be lazy otherwise function deployment fails when the function is first instantiated
    private val context: DSLContext by lazyContext

    override fun consume(payload: Daily) {
        logger.info("Starting processing of active creators")
        val endsAt = DSL.coalesce(SUBSCRIPTION.ENDED_AT, SUBSCRIPTION.ENDS_AT)
        val activeCreators = context
            .selectDistinct(SUBSCRIPTION.CREATOR_ID)
            .from(SUBSCRIPTION)
            .where(DSL.value(Instant.now()).between(SUBSCRIPTION.STARTED_AT, endsAt))
            .fetch()
            .mapNotNull { it[SUBSCRIPTION.CREATOR_ID] }

        activeCreators
            .chunked(25)
            .forEach {
                pubsub.publish(WriteSubscriberStatisticsRequested(it.toSet(), LocalDate.now().minusDays(1)))
                logger.info("Sent $it for stats processing")
            }
    }
}

fun main() {
    DailyActiveCreatorProcessor().consume(Daily())
}
