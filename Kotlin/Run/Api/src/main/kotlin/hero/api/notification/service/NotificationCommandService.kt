package hero.api.notification.service

import hero.exceptions.http.ForbiddenException
import hero.model.Notification
import hero.repository.notification.NotificationRepository
import hero.sql.jooq.Tables.NOTIFICATION
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant

class NotificationCommandService(
    private val notificationRepository: NotificationRepository,
    lazyContext: Lazy<DSLContext>,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context by lazyContext

    fun execute(command: UpdateNotification): Notification {
        val notification = notificationRepository.getById(command.notificationId)

        if (notification.userId != command.userId) {
            throw ForbiddenException()
        }

        val updatedNotification = notification.copy(
            checkedAt = command.checkedAt ?: notification.checkedAt,
            seenAt = command.seenAt ?: notification.seenAt,
        ).apply {
            notificationRepository.save(this)
        }
        return updatedNotification
    }

    fun execute(command: MarkNotificationsAsSeen) {
        context
            .update(NOTIFICATION)
            .set(NOTIFICATION.SEEN_AT, Instant.now(clock))
            .where(NOTIFICATION.USER_ID.eq(command.userId))
            .and(NOTIFICATION.SEEN_AT.isNull)
            .execute()
    }
}

data class MarkNotificationsAsSeen(
    val userId: String,
)

data class UpdateNotification(
    val notificationId: String,
    val userId: String,
    val checkedAt: Instant?,
    val seenAt: Instant?,
)
