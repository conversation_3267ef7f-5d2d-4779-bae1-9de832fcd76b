package hero.api.payment.scripts.usmigration

import hero.api.payment.scripts.initializeStripeScript
import hero.baseutils.log
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Creator
import hero.model.Currency
import hero.model.User
import hero.stripe.service.StripeAccountService

fun main() {
    val (firestore, _, _, _, clients) = initializeStripeScript(migrationProduction)
    val accounts = StripeAccountService(
        clients = clients,
        hostname = "https://staging.herohero.co",
        hostnameServices = "https://svc-staging.herohero.co",
    )
    val usersCollection = firestore.typedCollectionOf(User)
    val usUsers = usersCollection
        .where(root(User::creator).path(Creator::tierId)).isGreaterThan("USD")
        .fetchAll()
        .filter { it.creator.stripeAccountOnboarded }

    TODO("RUN WITH CAUTION")
    usUsers.forEach { user ->
        try {
            val accountId = if (user.creator.stripeAccountIdInEu != null) {
                log.info(
                    "User ${user.id} was already migrated: " +
                        " ${user.creator.stripeAccountId} -> ${user.creator.stripeAccountIdInEu}",
                )
                user.creator.stripeAccountIdInEu!!
            } else {
                val clonedAccount = accounts.createAccount(user, forceClientCurrency = Currency.PLN)
                usersCollection[user.id].field(root(User::creator).path(Creator::stripeAccountIdInEu))
                    .update(clonedAccount.id)
                log.info(
                    "User ${user.id} was cloned: " +
                        " ${user.creator.stripeAccountId} -> ${clonedAccount.id}",
                )
                clonedAccount.id
            }
            val link = accounts.createAccountLink(user.id, accountId, "", Currency.PLN)
            log.info(link.url)
        } catch (e: Exception) {
            log.error("${e.message}")
        }
    }
}
