package hero.api.post.controller.dto

import hero.api.post.service.CreatorPostsSortingFields
import hero.api.post.service.GetCreatorPostsFilter
import hero.api.post.service.dto.PostInput
import hero.contract.api.dto.PostResponse
import hero.core.data.Sort
import java.time.Instant

data class CommentResponse(
    val comment: PostResponse,
    val rootParent: PostResponse,
    val parent: PostResponse,
)

data class CreateCommentRequest(
    val parentId: String,
    val siblingId: String?,
    val attributes: PostInput,
)

data class CreatePostRequest(
    val publishedAt: Instant?,
    val categories: Set<String>,
    val attributes: PostInput,
    val isAgeRestricted: Boolean,
    val isSponsored: Boolean,
)

data class UpdatePostRequest(
    val publishedAt: Instant?,
    val pinnedAt: Instant?,
    val categories: Set<String>,
    val attributes: PostInput,
    val isAgeRestricted: <PERSON><PERSON>an,
    val isSponsored: <PERSON><PERSON><PERSON>,
    val excludeFromRss: <PERSON><PERSON><PERSON>,
)

data class UpdateCommentRequest(
    val attributes: PostInput,
)

data class SearchPostsRequest(
    val creatorId: String?,
    val afterCursor: String?,
    val beforeCursor: String?,
    val pageSize: Int?,
    val filter: GetCreatorPostsFilter?,
    val sortBy: CreatorPostsSortingFields?,
    val sortDirection: Sort.Direction?,
)
