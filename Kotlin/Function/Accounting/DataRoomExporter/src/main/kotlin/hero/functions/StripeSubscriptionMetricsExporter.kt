package hero.functions

import com.github.doyaaaaaken.kotlincsv.dsl.csvReader
import hero.baseutils.log
import hero.gcloud.CellHorizontalAlignment.LEFT
import hero.gcloud.CellHorizontalAlignment.RIGHT
import hero.gcloud.Format
import hero.gcloud.FormattedValue
import hero.gcloud.SHEETS_DEFAULT_COLUMN_COUNT
import hero.gcloud.SharedDriveIds
import hero.gcloud.SheetsCurrency
import hero.gcloud.SortFileDirection.DESC
import hero.gcloud.SortFileFields.CREATED_AT
import hero.gcloud.SortFiles
import hero.gcloud.SpreadsheetRef
import hero.gcloud.addSheet
import hero.gcloud.appendFormatted
import hero.gcloud.batched
import hero.gcloud.currency
import hero.gcloud.emptyCell
import hero.gcloud.getFileContents
import hero.gcloud.insertColumns
import hero.gcloud.listFiles
import hero.gcloud.number
import hero.gcloud.resizeColumns
import hero.gcloud.string
import hero.gcloud.writeHeader

internal object StripeSubscriptionMetricsExporter : DataExporter {
    override fun invoke(spreadsheet: SpreadsheetRef) {
        val sheetId = try {
            spreadsheet.addSheet("Stripe subscription metrics")
        } catch (e: Exception) {
            log.fatal("Failed to create new sheet", cause = e)
            throw e
        }

        val (rows, valueHeaders) = getMetricsContents()
        val first = rows.first()

        val formattedHeaders = listOf(
            string("Metric", Format(horizontalAlignment = LEFT)),
        ) + valueHeaders.map { string(it, Format(horizontalAlignment = RIGHT)) }
        val sheetRows = rows.map { it.render() }

        spreadsheet
            .batched()
            .let {
                val maxValueColumnsForDefaultSize = SHEETS_DEFAULT_COLUMN_COUNT - 1
                if (first.values.size > maxValueColumnsForDefaultSize) {
                    it.insertColumns(
                        SHEETS_DEFAULT_COLUMN_COUNT + 1,
                        first.values.size - maxValueColumnsForDefaultSize,
                        sheetId = sheetId,
                    )
                } else {
                    it
                }
            }
            .writeHeader(formattedHeaders, sheetId)
            .resizeColumns(1, 1, 200, sheetId = sheetId)
            .appendFormatted(sheetRows, sheetId)
            .execute()
    }
}

private fun getMetricsContents(): Pair<List<SubscriptionMetric>, List<String>> {
    val metricsFileName = "Subscription_metrics_-_monthly"
    val files = listFiles(
        driveId = SharedDriveIds.DATA_ROOM,
        // Folder MUST be created by cloud-run service account,
        // otherwise will not be readable.
        parentName = "Stripe reports",
        fileNamePattern = metricsFileName,
        sort = SortFiles(CREATED_AT, DESC),
    )

    if (files.isEmpty()) {
        error("There were no files in `Stripe reports` folder with `$metricsFileName` prefix.")
    }

    val contents = files
        .first()
        .let { getFileContents(it.id) }

    val headerColumns = contents.substringBefore("\n").replace("\"", "").split(",")
    val metric = headerColumns[0]
    val unit = headerColumns[1]
    val valueHeaders = headerColumns.filter { it != metric && it != unit }

    return csvReader()
        .readAllWithHeader(contents)
        .map { row ->
            subscriptionMetric(
                metric = row[metric],
                unit = row[unit],
                values = row.entries.filter { it.key != metric && it.key != unit }.map { it.value },
            )
        }
        .let {
            it to valueHeaders
        }
}

private fun SubscriptionMetric.render(): List<FormattedValue> {
    val mappedValues = values.map { value ->
        val number = value.toDoubleOrNull()
        when {
            isSubHeader() -> emptyCell()
            unit == "percent" -> {
                number((number ?: 0.0) / 100.0, pattern = "#.00%")
            }

            unit == "count" -> {
                number(number, pattern = "#0")
            }

            unit == "years" -> {
                number(number, pattern = "#0.00")
            }

            unit.uppercase() in SheetsCurrency.entries.map { it.toString() }.toList() -> {
                currency(number ?: 0.0, currency = SheetsCurrency.valueOf(unit.uppercase()))
            }

            else -> {
                error("Missing mapping for unit $unit")
            }
        }
    }

    val metricCell = if (isSubHeader()) {
        string(metric, format = Format(bold = true))
    } else {
        string(metric)
    }

    return listOf(metricCell) + mappedValues
}

private data class SubscriptionMetric(val metric: String, val unit: String, val values: List<String>) {
    fun isSubHeader() = unit.isBlank()
}

private fun subscriptionMetric(
    metric: String?,
    unit: String?,
    values: List<String>,
) = SubscriptionMetric(metric ?: "", unit ?: "", values)
