package hero.api.payment.controller.dto

import hero.api.payment.controller.PaymentResponse
import hero.api.payment.controller.PaymentResponseAttributes
import hero.api.payment.controller.PaymentResponseRelationships
import hero.api.subscriber.repository.PaymentIntentStatus
import hero.baseutils.plusDays
import hero.model.CouponMethod
import hero.model.CouponProvider
import hero.model.CouponTarget
import hero.model.PostDtoRelationship
import hero.model.TierDtoRelationship
import hero.model.UserDtoRelationship
import java.time.Instant

val exampleGetCouponsResponse = CouponResponseDto(
    data = CouponDto(
        id = "JDDJWNWOOO",
        attributes = CouponDtoAttributes(
            months = 3,
            days = null,
            percentOff = 10,
            redemptions = 1,
            redeemBy = Instant.now().plusDays(30),
            target = CouponTarget.CREATOR,
            provider = CouponProvider.STRIPE,
            method = CouponMethod.TRIAL,
            campaign = "Airbank TV ad",
        ),
        relationships = CouponDtoRelationships(
            creator = UserDtoRelationship("creatorId"),
            tier = TierDtoRelationship("EUR05"),
        ),
    ),
    included = CouponResponseDtoIncluded(
        tiers = listOf(),
        users = listOf(),
    ),
)

val examplePaymentSucceeeded = PaymentResponse(
    PaymentResponseAttributes(
        status = PaymentIntentStatus.SUCCEEDED,
        subscriptionStatus = null,
        createdAt = Instant.now(),
        paymentIntentClientSecret = "secret-secret",
        couponCode = "ASDFGHJKL",
    ),
    PaymentResponseRelationships(
        user = UserDtoRelationship("user-user"),
        post = PostDtoRelationship("post-post"),
        creator = UserDtoRelationship("creator-creator"),
    ),
)

val examplePaymentRequiresAction = PaymentResponse(
    PaymentResponseAttributes(
        status = PaymentIntentStatus.REQUIRES_ACTION,
        subscriptionStatus = null,
        createdAt = Instant.now(),
        paymentIntentClientSecret = "secret-secret",
        couponCode = null,
    ),
    PaymentResponseRelationships(
        user = UserDtoRelationship("user-user"),
        creator = UserDtoRelationship("creator-creator"),
        post = null,
    ),
)

val examplePaymentCancelled = PaymentResponse(
    PaymentResponseAttributes(
        status = PaymentIntentStatus.CANCELLED,
        subscriptionStatus = null,
        createdAt = Instant.now(),
        paymentIntentClientSecret = "secret-secret",
        couponCode = null,
    ),
    PaymentResponseRelationships(
        user = UserDtoRelationship("user-user"),
        creator = UserDtoRelationship("creator-creator"),
        post = null,
    ),
)
