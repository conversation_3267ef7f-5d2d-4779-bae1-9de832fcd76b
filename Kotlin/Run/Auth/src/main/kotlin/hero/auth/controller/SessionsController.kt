package hero.auth.controller

import hero.auth.controller.dto.SessionsResponse
import hero.auth.controller.dto.exampleSessionsResponse
import hero.auth.controller.dto.toDto
import hero.auth.service.GetActiveSessions
import hero.auth.service.RevokeAllSessions
import hero.auth.service.RevokeSession
import hero.auth.service.SessionCommandService
import hero.auth.service.SessionQueryService
import hero.exceptions.http.BadRequestException
import hero.http4k.auth.getJwtUser
import hero.http4k.extensions.body
import hero.http4k.extensions.delete
import hero.http4k.extensions.get
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path

class SessionsController(
    private val sessionsQueryService: SessionQueryService,
    private val sessionCommandService: SessionCommandService,
) {
    @Suppress("Unused")
    val routeGetSessions: ContractRoute =
        ("/v1/sessions").get(
            summary = "Get user's active sessions",
            parameters = object {},
            responses = listOf(Status.OK to exampleSessionsResponse),
            tag = "Sessions",
            handler = { request, _ ->
                val user = request.getJwtUser()

                val sessions = sessionsQueryService.execute(GetActiveSessions(user.id))
                    .map { it.toDto() }

                Response(Status.OK)
                    .body(SessionsResponse(currentSessionId = user.sessionId, sessions = sessions))
            },
        )

    @Suppress("Unused")
    val routeRevokeAllSessions: ContractRoute =
        ("/v1/sessions").delete(
            summary = "Revoke all sessions except currently active one",
            parameters = object {},
            responses = listOf(Status.NO_CONTENT to Unit),
            tag = "Sessions",
            handler = { request, _ ->
                val user = request.getJwtUser()
                sessionCommandService.execute(RevokeAllSessions(user.id, setOfNotNull(user.sessionId)))

                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("Unused")
    val routeRevokeSingleSession: ContractRoute =
        ("/v1/sessions" / Path.of("sessionId")).delete(
            summary = "Revoke single session. Cannot revoke currently used session",
            parameters = object {},
            responses = listOf(Status.NO_CONTENT to Unit),
            tag = "Sessions",
            handler = { request, _, sessionId ->
                val user = request.getJwtUser()
                if (sessionId == user.sessionId) {
                    throw BadRequestException("You cannot revoke session you are currently using. Log out instead.")
                }
                sessionCommandService.execute(RevokeSession(sessionId = sessionId, userId = user.id))

                Response(Status.NO_CONTENT)
            },
        )
}
