package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.retryOn
import hero.core.logging.Logger
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.firestore
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Subscriber
import hero.model.SupportCounts
import hero.model.Tier
import hero.model.User
import hero.model.UserStatus
import hero.model.UserSubscriptionsStats
import hero.model.topics.SubscriberChanged
import hero.model.topics.SubscriberStatusChange.SUBSCRIBED
import hero.model.topics.SubscriberStatusChange.UNSUBSCRIBED
import hero.model.topics.SubscriberStatusChanged
import hero.repository.subscription.JooqSubscriptionHelper
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables.SUBSCRIPTION
import org.jooq.DSLContext
import java.util.concurrent.ExecutionException
import hero.baseutils.log as CloudLogger
import hero.gcloud.TypedCollectionReference as TCR

@Suppress("Unused")
class SubscriptionCounter(
    envVariables: EnvironmentVariables = SystemEnv,
    private val firestore: FirestoreRef = firestore(envVariables.cloudProject, envVariables.isProduction),
    private val subscribersCollection: TCR<Subscriber> = firestore.typedCollectionOf(Subscriber),
    private val usersCollection: TCR<User> = firestore.typedCollectionOf(User),
    private val pubSub: PubSub = PubSub(envVariables.environment, envVariables.cloudProject),
    private val subStatsCollection: TCR<UserSubscriptionsStats> = firestore.typedCollectionOf(UserSubscriptionsStats),
    private val log: Logger = CloudLogger,
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
) : PubSubSubscriber<SubscriberChanged>(envVariables) {
    private val context: DSLContext by lazyContext

    override fun consume(payload: SubscriberChanged) {
        val userId = payload.userId
        val creatorId = payload.creatorId
        val result = retryOn(ExecutionException::class) {
            processCounts(userId = userId, creatorId = creatorId)
        }

        // if changes were stored, we publish status changed and to the creators webhook
        if (result != null) {
            val (subscriber, user, creator) = result
            val isActive = subscriber.status.isActive
            publishSubscribersStatusChanged(userId, creatorId, isActive, subscriber, user, creator, payload.doNotNotify)
        }
    }

    private fun processCounts(
        userId: String,
        creatorId: String,
    ): Triple<Subscriber, User?, User?>? {
        val subscriber = findSubscription(userId, creatorId)
        if (subscriber == null) {
            log.fatal("No subscription for $userId -> $creatorId was found. Skipping.")
            return null
        }

        val user = usersCollection[subscriber.userId].fetch()
        val creator = usersCollection[subscriber.creatorId].fetch()

        try {
            // always update creator counts
            updateCreatorCounts(creatorId)
        } catch (e: Exception) {
            log.fatal(
                "Failed to update creator $creatorId counts for user id $userId",
                mapOf("creatorId" to creatorId, "userId" to userId),
                cause = e,
            )
        }
        val updateStatsResult = updateStats(subscriber) ?: return null
        updateUserCounts(userId, updateStatsResult)

        return Triple(subscriber, user, creator)
    }

    private fun updateStats(subscriber: Subscriber): UserSubscriptionsStats? {
        val isActive = subscriber.status.isActive

        val subscriptionStatsReference = subStatsCollection[subscriber.userId]

        val subscriptionStats = subscriptionStatsReference.fetch() ?: UserSubscriptionsStats()

        if (isActive && subscriber.creatorId in subscriptionStats.creatorIds) {
            log.debug("Creator is already in subscriptions, skipping.")
            return null
        }

        if (!isActive && subscriber.creatorId !in subscriptionStats.creatorIds) {
            log.debug("Creator is already removed from subscriptions, skipping.")
            return null
        }

        if (isActive) {
            subscriptionStats.creatorIds[subscriber.creatorId] = subscriber.tierId
        } else {
            subscriptionStats.creatorIds.remove(subscriber.creatorId)
        }

        subscriptionStatsReference.set(subscriptionStats)

        return subscriptionStats
    }

    private fun updateUserCounts(
        userId: String,
        stats: UserSubscriptionsStats,
    ) {
        val payments = stats.creatorIds.values.sumOf { it.replace("[A-Z]+".toRegex(), "").toLong() * 100 }
        val creatorsCount = stats.creatorIds.size.toLong()
        val userTransactionReference = usersCollection[userId]

        userTransactionReference.field(root(User::counts).path(SupportCounts::supporting)).update(creatorsCount)
        userTransactionReference.field(root(User::counts).path(SupportCounts::payments)).update(payments)
    }

    private fun updateCreatorCounts(creatorId: String) {
        val creator = usersCollection[creatorId]

        val subCount = fetchSubCount(creatorId)
        val tier = Tier.ofId(creator.get().creator.tierId)
        val incomes = subCount * tier.priceCents
        val incomesClean = incomes - incomes * DEFAULT_FEE / 100

        creator.field(root(User::counts).path(SupportCounts::supporters)).update(subCount.toLong())
        creator.field(root(User::counts).path(SupportCounts::incomes)).update(incomes.toLong())
        creator.field(root(User::counts).path(SupportCounts::incomesClean)).update(incomesClean)
        if (subCount >= 100) {
            // See: https://linear.app/herohero/issue/HH-3792/release-livestreams#comment-0f3f723d
            // We cannot just pass `hasLivestream` to be `true` to clients as we generate
            // livestream meta in Gjirafa based on this field.
            creator.field(User::hasLivestreams).update(true)
        }
    }

    private fun fetchSubCount(creatorId: String) =
        context
            .selectCount()
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.CREATOR_ID.eq(creatorId).and(JooqSubscriptionHelper.activeSubscription))
            .fetchOne()
            ?.value1() ?: 0

    private fun publishSubscribersStatusChanged(
        userId: String,
        creatorId: String,
        isActive: Boolean,
        subscriber: Subscriber,
        user: User?,
        creator: User?,
        doNotNotify: Boolean,
    ) {
        pubSub.publish(
            SubscriberStatusChanged(
                userId = userId,
                creatorId = creatorId,
                statusChange = if (isActive) SUBSCRIBED else UNSUBSCRIBED,
                refused = subscriber.refused,
                cancelledReason = subscriber.cancelledReason,
                refunded = subscriber.refunded,
                ended = subscriber.cancelAtPeriodEnd,
                // prevent notifying deleted users or when needed for migrations
                doNotNotify = user?.status != UserStatus.ACTIVE || creator?.status != UserStatus.ACTIVE || doNotNotify,
            ),
        )
    }

    private fun findSubscription(
        userId: String,
        creatorId: String,
    ): Subscriber? =
        subscribersCollection
            // there might be more subscriber entities as invites are separate
            .where(Subscriber::userId)
            .isEqualTo(userId)
            .and(Subscriber::creatorId)
            .isEqualTo(creatorId)
            .fetchAll()
            .minByOrNull { if (it.status.isActive) 0 else 1 }
}

// TODO uses this fee for ALL tiers, not just the one the user is subscribed to
@Deprecated("Use the tier's fee instead")
private const val DEFAULT_FEE = 10L
