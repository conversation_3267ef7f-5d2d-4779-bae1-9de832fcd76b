package hero.auth.service

import hero.exceptions.http.ForbiddenException
import hero.repository.session.SessionRepository
import hero.sql.jooq.Tables.SESSION
import org.jooq.DSLContext
import java.util.UUID

class SessionCommandService(
    private val sessionRepository: SessionRepository,
    lazyContext: Lazy<DSLContext>,
) {
    private val context by lazyContext

    fun execute(command: RevokeSession) {
        // some users could log out before they refresh any tokens and a session is created for them
        // we can remove this in the future
        val session = sessionRepository.findById(UUID.fromString(command.sessionId)) ?: return

        // should never happen
        if (session.userId != command.userId) {
            throw ForbiddenException()
        }

        val revokedSession = session.copy(revoked = true)
        sessionRepository.save(revokedSession)
    }

    fun execute(command: RevokeAllSessions) {
        context
            .update(SESSION)
            .set(SESSION.REVOKED, true)
            .where(SESSION.USER_ID.eq(command.userId))
            .and(SESSION.ID.notIn(command.exclude))
            .execute()
    }
}

data class RevokeSession(val sessionId: String, val userId: String)

data class RevokeAllSessions(val userId: String, val exclude: Set<String> = setOf())
