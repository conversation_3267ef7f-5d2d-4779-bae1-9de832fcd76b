package hero.api.payment.scripts.usmigration

import com.stripe.param.CouponListParams
import hero.api.payment.scripts.initializeStripeScript
import hero.baseutils.SystemEnv
import hero.gcloud.PubSub
import hero.gcloud.typedCollectionOf
import hero.model.Currency
import hero.model.Currency.EUR
import hero.model.Tier
import hero.stripe.model.StripePrice
import hero.stripe.service.StripeService

fun main() {
    val (firestore, _, _, _, clients) = initializeStripeScript(migrationProduction)
    val pubSub = PubSub(migrationEnvironment, SystemEnv.cloudProject)
    val pricesCollection = firestore.typedCollectionOf(StripePrice)
    val stripeService = StripeService(clients, pubSub)
    val usCreators = setOf<String>()
    clients[EUR].coupons().list(CouponListParams.builder().setLimit(10000).build())
        .autoPagingIterable()
        .filter { it.metadata["creatorId"] in usCreators }
        .forEach { oldCoupon ->
            val tierId = oldCoupon.metadata["tierId"] ?: error("Missing tierId")
            val creatorId = oldCoupon.metadata["creatorId"] ?: error("Missing creatorId")
            val purchaseByUserId = oldCoupon.metadata["purchasedByUserId"] ?: error("Missing purchaseByUserId")
            val months = oldCoupon.metadata["months"]?.toInt()
            val days = oldCoupon.metadata["days"]?.toInt()

            val stripePriceId = pricesCollection["$creatorId|$tierId"].get().stripeId
            val price = clients[EUR].prices().retrieve(stripePriceId)

            val couponCopied = stripeService.createCoupon(
                purchasedByUserId = purchaseByUserId,
                couponId = oldCoupon.id,
                creatorId = creatorId,
                tier = Tier.ofId(tierId),
                currency = Currency.USD,
                price = price,
                months = months,
                days = days,
                extraMetadata = mapOf("copiedFrom" to creatorId),
                percentOff = oldCoupon.percentOff.toInt(),
                campaign = oldCoupon.metadata["campaign"] ?: "",
            )

            println("couponCopied: $couponCopied")
        }
}
