package hero.api.subscriber.controller.dto

import hero.api.subscriber.service.Subscription
import hero.api.user.controller.dto.toResponse
import hero.model.CouponMethod.TRIAL
import hero.model.CouponMethod.VOUCHER
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.SubscriptionsDtoStatus
import hero.model.Tier
import java.time.Instant

fun Subscription.toResponse(fullAccess: Boolean) =
    SubscriptionResponse(
        id = subscriber.id,
        subscribedAt = subscriber.subscribed,
        details = if (fullAccess) subscriber.toResponse() else null,
        subscriber = user.toResponse(listOf()),
        creator = creator.toResponse(listOf()),
        tier = if (fullAccess) tier.toResponse() else null,
    )

private fun Subscriber.toResponse(): SubscriptionDetailsResponse {
    val isCouponActive = (couponExpiresAt?.isAfter(Instant.now()) == true) ||
        // trial forever
        (couponMethod == TRIAL && couponExpiresAt == null && couponPercentOff != null)
    val resolvedCouponMethod = if (couponAppliedForMonths != null && couponMethod == null) VOUCHER else couponMethod
    return SubscriptionDetailsResponse(
        status = when (status) {
            SubscriberStatus.PAST_DUE -> SubscriptionsDtoStatus.PAST_DUE
            else -> if (status.isActive) SubscriptionsDtoStatus.ACTIVE else SubscriptionsDtoStatus.INACTIVE
        },
        cancelAtPeriodEnd = cancelAtPeriodEnd,
        expires = expires,
        couponAppliedForMonths = if (isCouponActive) couponAppliedForMonths else null,
        couponAppliedForDays = if (isCouponActive) couponAppliedForDays else null,
        tierId = tierId,
        type = subscriberType,
        // TODO remove the if section in 2025 once old subscriptions are not relevant
        couponMethod = if (isCouponActive) resolvedCouponMethod else null,
        couponPercentOff = if (isCouponActive) couponPercentOff else null,
        couponExpiresAt = if (isCouponActive) couponExpiresAt else null,
    )
}

fun Tier.toResponse() =
    TierResponse(
        id = id,
        priceCents = priceCents,
        currency = currency,
        default = default,
        hidden = hidden,
    )
