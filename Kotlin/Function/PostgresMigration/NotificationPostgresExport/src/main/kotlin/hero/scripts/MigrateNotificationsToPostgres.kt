package hero.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.retryOn
import hero.core.data.Sort
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Notification
import hero.model.Post
import hero.model.StorageEntityType
import hero.repository.notification.NotificationRepository
import hero.repository.post.PostRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import org.jooq.exception.IntegrityConstraintViolationException
import java.util.concurrent.ExecutionException

fun main() {
    val context = JooqSQL.context(ConnectorConnectionPool.dataSource)
    val regex = Regex("""Key \(object_post_id\)=\(([a-zA-Z0-9\-]+)\)""")
    val firestore = firestore(SystemEnv.cloudProject, true)
    val notificationsCollection = firestore.typedCollectionOf(Notification)
    val notificationRepository = NotificationRepository(context)

    val postsCollection = firestore.typedCollectionOf(Post)
    val postRepository = PostRepository(context)
    val userIdRegex = Regex("""prod-users/([^/?]+)""")

    var lastId: String? = "vitspoustaqxjvyxxw-1649261116"
    var processed = 0

    fun saveNotifications(notifications: List<Notification>) {
        try {
            notificationRepository.saveAll(notifications)
            return
        } catch (e: IntegrityConstraintViolationException) {
            println("Failed integrity")
            val match = regex.find(e.message.toString())

            val objectPostId = match?.groupValues?.get(1)
            if (objectPostId != null && e.message?.contains("is not present in table \"post\"") == true) {
                val post = postsCollection[objectPostId].fetch()
                if (post == null) {
                    val notificationsToDelete = notificationsCollection
                        .where(Notification::objectId)
                        .isEqualTo(objectPostId)
                        .fetchAll()

                    println("Will delete ${notificationsToDelete.size} notifications for unexisting post $objectPostId")
                    notificationsToDelete
                        .filter { it.objectType == StorageEntityType.POST }
                        .forEach {
                            notificationsCollection[it.id].delete()
                        }
                } else {
                    val posts = postsCollection.where(Post::userId).isEqualTo(post.userId).fetchAll()

                    val ids = context.select(Tables.POST.ID)
                        .from(Tables.POST)
                        .where(Tables.POST.USER_ID.eq(post.userId))
                        .fetch()
                        .map { it[Tables.POST.ID] }
                        .toSet()

                    println("Migrating posts from post ${post.id} and ${post.userId}, will migrate ${posts.size}")
                    val filteredPosts = posts
                        .filter { it.id !in ids }
                    println("Filtered, will migrate only ${filteredPosts.size}")

                    filteredPosts
                        .map {
                            if (it.userId.contains("prod-users/")) {
                                println("Post $${it.id} has some weird user name")
                                val match1 = userIdRegex.find(it.userId)
                                val userId = match1?.groupValues?.get(1)!!
                                it.copy(userId = userId)
                            } else {
                                it
                            }
                        }
                        .chunked(200)
                        .forEachIndexed { index, posts1 ->
                            postRepository.saveAll(posts1)
                            println("Migrated ${(index + 1) * 200} posts")
                        }
                }
            } else {
                throw e
            }
        }
    }

    fun wrapper(notifications: List<Notification>) {
        for (tries in 0..notifications.size) {
            try {
                saveNotifications(notifications)
                println("done")
                return
            } catch (e: IntegrityConstraintViolationException) {
                println("will retry ${tries <= notifications.size}")
            }
        }
    }

    do {
        println("Processing notifications from id $lastId")
        val notifications = retryOn(ExecutionException::class) {
            notificationsCollection.where(Notification::id).isNotEqualTo("")
                .orderBy(Notification::id, Sort.Direction.DESC)
                .limit(200)
                .startAfterIfNotNull(lastId)
                .fetchAll()
        }

        if (notifications.isEmpty()) {
            break
        }

        wrapper(notifications)

        processed += notifications.size
        lastId = notifications.last().id
        println("Processed $processed notifications")
    } while (true)
}
