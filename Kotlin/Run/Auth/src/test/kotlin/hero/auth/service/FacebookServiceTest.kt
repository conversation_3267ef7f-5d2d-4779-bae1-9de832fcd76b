package hero.auth.service

import hero.baseutils.instantOf
import hero.baseutils.systemEnv
import hero.exceptions.http.ForbiddenException
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

class FacebookServiceTest {
    private val service = FacebookService(systemEnv("FACEBOOK_SECRET"))

    @Test
    fun `signed request is correctly extracted`() {
        val testString = "signed_request=X-Nq0SsJo39QPXXFuyDnK6OWz9H5qkW7RiNVFBSA1iQ." +
            "eyJ1c2VyX2lkIjoiMTIyMTE5MzYxNDkyNDkxNjg2IiwiYWxnb3JpdGhtIjoiSE1BQy1TSEEyNTYiLCJpc3N1ZWRfYXQiOjE3Mzg1NzYyOTN9"
        val parsedRequest = service.parseDeletionRequest(testString)

        assertEquals("122119361492491686", parsedRequest.userId)
        assertEquals("HMAC-SHA256", parsedRequest.algorithm)
        assertEquals(instantOf("2025-02-03T09:51:33Z"), parsedRequest.issuedAt)
    }

    @Test
    fun `wrongly signed request is correctly denied`() {
        val testString = "signed_request=X-Nq0SsJo39QPXXFuyDnK6OWz9H5qkW7RiNVFBSA111." +
            "eyJ1c2VyX2lkIjoiMTIyMTE5MzYxNDkyNDkxNjg2IiwiYWxnb3JpdGhtIjoiSE1BQy1TSEEyNTYiLCJpc3N1ZWRfYXQiOjE3Mzg1NzYyOTN9"
        assertThrows<ForbiddenException> {
            service.parseDeletionRequest(testString)
        }
    }
}
