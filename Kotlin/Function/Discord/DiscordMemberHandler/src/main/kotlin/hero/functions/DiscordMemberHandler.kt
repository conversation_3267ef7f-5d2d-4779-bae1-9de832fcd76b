package hero.functions

import com.google.cloud.logging.Severity
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.nullIfEmpty
import hero.baseutils.plusMinutes
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.fetchSingle
import hero.gcloud.firestore
import hero.gcloud.get
import hero.gcloud.typedCollectionOf
import hero.model.DiscordCredentials
import hero.model.DiscordMeta
import hero.model.User
import hero.model.topics.DiscordMemberChanged
import hero.model.topics.SubscriberStatusChange
import java.net.HttpURLConnection.HTTP_MULT_CHOICE
import java.time.Instant

@Suppress("Unused")
class DiscordMemberHandler(
    private val production: Boolean = SystemEnv.isProduction,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val credentials: DiscordCredentials = firestore.firestore["constants"]["oauth-discord"]
        .fetchSingle()!!,
    private val client: DiscordClient =
        DiscordClient(credentials.appId, credentials.appSecret, credentials.botToken),
) : PubSubSubscriber<DiscordMemberChanged>() {
    override fun consume(payload: DiscordMemberChanged) {
        val user = usersCollection[payload.userId].fetch()
        if (user?.discord?.id == null) {
            return
        }
        val creator = usersCollection[payload.creatorId].fetch()
        if (creator?.discord?.id == null || creator.discord?.guildId == null) {
            log.info(
                "Creator ${payload.creatorId} does not have discord set up. Skipping.",
                mapOf("userId" to payload.userId),
            )
            return
        }

        val creatorDiscord = creator.discord
        val userDiscord = user.discord
        require(creatorDiscord != null && userDiscord != null)

        val guildId = creatorDiscord.guildId
        require(guildId != null)

        val creatorId = creator.id
        val userId = user.id

        val roleId = getRoleId(creatorDiscord, guildId, creatorId)
        val accessToken = refreshAccessToken(userDiscord, userId, creatorId).getOrNull() ?: return

        val statusChange = payload.statusChange
        val userDiscordId = userDiscord.id
        require(userDiscordId != null)
        val response = when (statusChange) {
            SubscriberStatusChange.SUBSCRIBED -> {
                val responseAddingToGuild = client.addUserToGuild(
                    discordUserId = userDiscord.id!!,
                    guildId = guildId,
                    accessToken = accessToken,
                )
                if (responseAddingToGuild.second.statusCode < HTTP_MULT_CHOICE) {
                    client.addUserGuildRole(
                        guildId = guildId,
                        discordUserId = userDiscordId,
                        roleId = roleId,
                    )
                } else {
                    responseAddingToGuild
                }
            }

            SubscriberStatusChange.UNSUBSCRIBED ->
                client.removeUserGuildRole(
                    discordUserId = userDiscordId,
                    guildId = guildId,
                    roleId = roleId,
                    accessToken = accessToken,
                )
        }
        val statusCode = response.second.statusCode
        val responseMessage = response.second.responseMessage
        val bytes = response.second.body().toByteArray()
        val severity = if (statusCode < HTTP_MULT_CHOICE || statusCode == 404) Severity.INFO else Severity.ALERT
        log.log(
            severity,
            """
                Operation $statusChange for User ${user.id} on Guild $guildId,
                Role $roleId of ${creator.id} finished with $statusCode: 
                ${String(bytes).nullIfEmpty() ?: responseMessage}.
            """.trimIndent().replace("(\n*)\n".toRegex(), "$1"),
            mapOf("userId" to user.id, "creatorId" to creator.id),
        )
    }

    private fun getRoleId(
        creatorDiscord: DiscordMeta,
        guildId: String,
        creatorId: String,
    ): String {
        val guildRoles = client.getGuildRoles(guildId)
        val guildRoleId = creatorDiscord.roleId
        return if (guildRoleId == null || guildRoleId !in guildRoles) {
            if (creatorDiscord.roleId != null) {
                log.error(
                    "Creator $creatorId probably removed original Discord role $guildRoleId " +
                        "(existing: $guildRoles), recreating.",
                )
            }

            try {
                client.createGuildRole(
                    name = "Herohero",
                    color = 8027360,
                    mentionable = true,
                    hoist = true,
                    guildId = guildId,
                ).also {
                    creatorDiscord.roleId = it
                    usersCollection[creatorId].field(User::discord).update(creatorDiscord)
                }
            } catch (error: Exception) {
                log.fatal(
                    "Failed to create guild role for creator's($creatorId) guild $guildId",
                    mapOf("creatorId" to creatorId),
                )
                throw error
            }
        } else {
            guildRoleId
        }
    }

    private fun refreshAccessToken(
        userDiscord: DiscordMeta,
        userId: String,
        creatorId: String,
    ): Result<String> {
        if (userDiscord.tokenExpiresAt!! < Instant.now().plusMinutes(EXPIRES_AT_THRESHOLD)) {
            // TODO implement via OAuthController
            log.info("Refreshing Discord token of User $userId.")
            return try {
                val newTokens = client.refreshToken(userDiscord.refreshToken!!)
                userDiscord.accessToken = newTokens.accessToken
                userDiscord.refreshToken = newTokens.refreshToken
                userDiscord.tokenExpiresAt = newTokens.expiresIn?.let { Instant.now().plusSeconds(it) }
                usersCollection[userId].field(User::discord).update(userDiscord)
                Result.success(newTokens.accessToken)
            } catch (e: Exception) {
                log.fatal(
                    "Cannot refresh token for $userId (subscribing $creatorId): ${e.message}",
                    mapOf("userId" to userId),
                )
                Result.failure(e)
            }
        }
        val accessToken = userDiscord.accessToken ?: error("Access token is null")
        return Result.success(accessToken)
    }
}

private const val EXPIRES_AT_THRESHOLD = 10L
