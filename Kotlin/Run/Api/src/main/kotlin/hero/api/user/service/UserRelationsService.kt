package hero.api.user.service

import com.google.cloud.firestore.Query
import hero.gcloud.TypedCollectionReference
import hero.gcloud.where
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.SubscriptionRelationType
import hero.model.SubscriptionRelationType.GROUP
import hero.model.SubscriptionRelationType.HIMSELF
import hero.model.SubscriptionRelationType.IS_SUBSCRIBED_BY
import hero.model.SubscriptionRelationType.IS_SUBSCRIBED_TO
import hero.model.SubscriptionRelationType.IS_SUBSCRIBED_TO_SAME_CREATOR

class UserRelationsService(
    private val subscribersCollection: TypedCollectionReference<Subscriber>,
) {
    fun canInteract(
        userId: String,
        includeCommunity: Boolean,
        requestedUserIds: List<String>,
    ): Boolean =
        userRelationsTo(userId, includeCommunity, requestedUserIds)
            .canInteract(requestedUserIds)

    fun userRelationsTo(
        userId: String,
        isCommunity: Boolean,
        requestedUserIds: List<String>,
    ): Set<UserSubscriptionRelation> {
        // my subscriptions
        val subscriptions = userSubscribes(userId) - setOf(userId)

        // return intersection of requirements with my subscriptions
        val userSubscriptions = subscriptions
            .intersect(requestedUserIds.toSet())
            .map { UserSubscriptionRelation(it, IS_SUBSCRIBED_TO) }
            .toSet()

        val userSubscribers = creatorSubscribedBy(isCommunity, userId, requestedUserIds)
            .map { UserSubscriptionRelation(it, IS_SUBSCRIBED_BY) }

        val sameCreator = subscriptions
            .flatMap { creator -> creatorSubscribedBy(isCommunity, creator, requestedUserIds).map { creator to it } }
            .map { (creator, sub) -> UserSubscriptionRelation(sub, IS_SUBSCRIBED_TO_SAME_CREATOR, creator) }

        val userRelation = setOf(UserSubscriptionRelation(userId, HIMSELF))

        return (userSubscriptions + userSubscribers + sameCreator + userRelation).distinctBy { it.userId }.toSet()
    }

    fun userSubscribes(userId: String): List<String> =
        subscribersCollection
            .where(Subscriber::userId).isEqualTo(userId)
            .and(Subscriber::status).isIn(SubscriberStatus.activeStatuses)
            .fetchAll()
            .map { it.creatorId }
            .plus(userId)

    private fun creatorSubscribedBy(
        isCommunity: Boolean,
        creatorId: String,
        userIds: List<String>,
    ): List<String> =
        if (isCommunity) {
            val chunkSize = 10
            userIds
                .chunked(chunkSize)
                .flatMap { chunk ->
                    subscribersCollection
                        .where(Subscriber::creatorId).isEqualTo(creatorId)
                        .and(Subscriber::userId).isIn(chunk)
                        .orderBy(Subscriber::subscribed, Query.Direction.DESCENDING)
                        .fetchAll()
                }
                // This must be filtered afterward as there can only be one .whereIn in the query.
                .filter { it.status in SubscriberStatus.activeStatuses }
                .map { it.userId }
        } else {
            emptyList()
        }
}

data class UserSubscriptionRelation(
    val userId: String,
    val relationship: SubscriptionRelationType,
    val subbedTo: String? = null,
)

fun Collection<UserSubscriptionRelation>.canInteractWithRelationAndCreators(
    userId: String,
    requestedUserIds: List<String>,
): Triple<Boolean, SubscriptionRelationType, List<String>> {
    val canInteract = this.map { it.userId }
        .containsAll(requestedUserIds)

    val relationSize = this.filter { it.userId in requestedUserIds && it.userId != userId }.size
    val relation = if (relationSize > 1) {
        GROUP
    } else {
        this.first().relationship
    }

    val subbedTo = if (relation == IS_SUBSCRIBED_TO_SAME_CREATOR && this.isNotEmpty()) {
        this.first().subbedTo?.let { listOf(it) } ?: emptyList()
    } else {
        emptyList()
    }
    return Triple(canInteract, relation, subbedTo)
}

private fun Collection<UserSubscriptionRelation>.canInteract(requestedUserIds: List<String>) =
    this.map { it.userId }
        .containsAll(requestedUserIds)
