package hero.api.library.controller.dto

import hero.contract.api.dto.PostResponse
import hero.core.data.SimplePageResponse
import java.time.Instant

data class SavedPostResponse(
    val id: String,
    val savedAt: Instant,
    val post: PostResponse,
)

data class AddPostToLibraryRequest(
    val postId: String,
)

data class PagedSavedPostResponse(
    override val content: List<SavedPostResponse>,
    override val hasNext: Boolean,
    override val afterCursor: String? = null,
    override val beforeCursor: String? = null,
) : SimplePageResponse<SavedPostResponse>
