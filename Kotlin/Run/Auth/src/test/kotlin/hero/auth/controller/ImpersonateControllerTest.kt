package hero.auth.controller

import hero.baseutils.plusDays
import hero.exceptions.http.ForbiddenException
import hero.http4k.auth.withAccessTokenCookie
import hero.jwt.IMPERSONATION_TOKEN
import hero.jwt.JwtUser
import hero.jwt.jwtParser
import hero.jwt.toJwt
import hero.model.Role
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.cookie.cookies
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.Instant
import kotlin.test.assertEquals

internal class ImpersonateControllerTest {
    private val controller = ImpersonateController()

    @Test
    fun testImpersonateForbiddenNoUser() {
        val request = Request(Method.POST, "/v1/users/evelyn/impersonate")
        // missing auth header is unfortunately not FORBIDDEN
        assertThrows<ForbiddenException> {
            controller.routeImpersonate(request)
        }
    }

    @Test
    fun testImpersonateForbiddenNoModerator() {
        val user = Jwt<PERSON>ser("annais", Instant.now().plusDays(1).epochSecond, Role.USER.ordinal, null)
        val request = Request(Method.POST, "/v1/users/evelyn/impersonate")
            .withAccessTokenCookie(user.toJwt())
        assertThrows<ForbiddenException> {
            controller.routeImpersonate(request)
        }
    }

    @Test
    fun testImpersonate() {
        val user = JwtUser("annais", Instant.now().plusDays(1).epochSecond, Role.MODERATOR.ordinal, null)
        val request = Request(Method.POST, "/v1/users/evelyn/impersonate")
            .withAccessTokenCookie(user.toJwt())
        val response = controller.routeImpersonate(request)
        val impersonateToken = response.cookies().first { it.name == IMPERSONATION_TOKEN }.value
        val impersonateId = jwtParser().parseSignedClaims(impersonateToken).payload.subject
        assertEquals("evelyn", impersonateId)
    }
}
