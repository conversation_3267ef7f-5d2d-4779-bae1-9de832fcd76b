package hero.connect.subscription.controller

import hero.baseutils.minusDays
import hero.connect.auth.validateApi<PERSON>ey
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.UnauthorizedException
import hero.http4k.auth.parseToken
import hero.http4k.extensions.body
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.jwt.jwtParser
import hero.model.OAuthScopes
import hero.repository.subscription.JooqSubscriptionHelper
import hero.repository.user.UserRepository
import hero.sql.jooq.Tables.SUBSCRIPTION
import hero.sql.jooq.Tables.USER
import hero.sql.jooq.tables.records.SubscriptionRecord
import org.http4k.contract.ContractRoute
import org.http4k.contract.Tag
import org.http4k.contract.meta
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Query
import org.http4k.lens.int
import org.http4k.security.BearerAuthSecurity
import org.jooq.DSLContext
import java.time.Instant
import javax.crypto.SecretKey

fun subscriptionController(
    secretKey: <PERSON><PERSON>ey,
    lazyContext: Lazy<DSLContext>,
) = ("/v1/subscription").get(
    summary = "Get subscription detail of authenticated user",
    tag = "Subscriptions",
    parameters = object {},
    responses = listOf(Status.OK to ConnectSubscriptionResponse(Instant.now(), ConnectSubscriptionStatus.ACTIVE)),
    handler = { request, _ ->
        val token = request.getBearerToken(secretKey)

        if (OAuthScopes.SUBSCRIPTION_READ !in token.scopes) {
            throw ForbiddenException()
        }

        val context = lazyContext.value

        val subscription = if (token.creatorId == token.userId) {
            SubscriptionRecord().apply {
                this.startedAt = Instant.now().minusDays(30)
                this.status = JooqSubscriptionHelper.activeStatuses.first()
            }
        } else {
            context.selectFrom(SUBSCRIPTION)
                .where(SUBSCRIPTION.USER_ID.eq(token.userId))
                .and(SUBSCRIPTION.CREATOR_ID.eq(token.creatorId))
                .orderBy(SUBSCRIPTION.UPDATED_AT.desc())
                .fetchOne()
        }

        val response = subscription?.let {
            ConnectSubscriptionResponse(
                subscribedAt = it.startedAt,
                status = when {
                    it.status in JooqSubscriptionHelper.activeStatuses -> ConnectSubscriptionStatus.ACTIVE
                    else -> ConnectSubscriptionStatus.EXPIRED
                },
            )
        } ?: ConnectSubscriptionResponse(
            subscribedAt = null,
            status = ConnectSubscriptionStatus.NEVER_SUBSCRIBED,
        )

        Response(Status.OK).body(response)
    },
)

fun subscriptionsController(
    lazyContext: Lazy<DSLContext>,
    userRepository: UserRepository,
): ContractRoute {
    val pageSizeQuery = Query.int().defaulted("size", 10)
    val pageIndexQuery = Query.int().defaulted("page", 0)
    return "/v1/subscriptions" meta {
        queries += pageSizeQuery
        queries += pageIndexQuery
        tags += Tag("Subscriptions")
        summary = "Get paginated list of active subscriptions"
        security = BearerAuthSecurity(validateApiKey(lazyContext))
        description = """
            Get paginated list of active subscriptions for the authenticated creator.
            Returns subscriber information including subscription dates, names, and emails (if privacy policy allows).
            Supports pagination with size and page parameters.
        """.trimIndent()
        returning(Status.OK, lens<ConnectPagedSubscriptionsResponse>() to connectPagedSubscriptionsResponse)
    } bindContract Method.GET to { req ->
        val creator = userRepository.getById(req.getUserId())
        val context = lazyContext.value
        val pageSize = pageSizeQuery(req).coerceAtMost(100)
        val pageIndex = pageIndexQuery(req)

        val data = context
            .select(USER.NAME, USER.EMAIL, SUBSCRIPTION.ENDS_AT, SUBSCRIPTION.STARTED_AT)
            .from(SUBSCRIPTION)
            .join(USER).on(USER.ID.eq(SUBSCRIPTION.USER_ID))
            .where(SUBSCRIPTION.CREATOR_ID.eq(creator.id))
            .and(JooqSubscriptionHelper.activeSubscription)
            .orderBy(SUBSCRIPTION.STARTED_AT.desc())
            .limit(pageSize)
            .offset(pageSize * pageIndex)
            .fetch()
            .map {
                val hasPrivacyFlag = creator.privacyPolicyEffectiveAt?.isBefore(Instant.now()) == true
                ConnectSubscriptionsResponse(
                    subscribedAt = it[SUBSCRIPTION.STARTED_AT],
                    expiresAt = it[SUBSCRIPTION.ENDS_AT],
                    name = it[USER.NAME],
                    email = if (hasPrivacyFlag) it[USER.EMAIL] else null,
                )
            }

        val response = ConnectPagedSubscriptionsResponse(
            data = data,
            pageSize = pageSize,
            currentPage = pageIndex,
        )

        Response(Status.OK).body(response)
    }
}

data class ConnectPagedSubscriptionsResponse(
    val currentPage: Int,
    val pageSize: Int,
    val data: List<ConnectSubscriptionsResponse>,
)

data class ConnectSubscriptionsResponse(
    val subscribedAt: Instant,
    val expiresAt: Instant?,
    val name: String,
    val email: String?,
)

fun Request.getBearerToken(secretKey: SecretKey): ConnectBearerToken {
    val authorizationHeader = header("Authorization") ?: throw UnauthorizedException()
    val bearerDelimiter = "Bearer "
    if (!authorizationHeader.startsWith(bearerDelimiter)) {
        throw UnauthorizedException()
    }

    val token = authorizationHeader.substringAfter(bearerDelimiter)
    val claims = parseToken(token, jwtParser(secretKey = secretKey)) ?: throw UnauthorizedException()

    val scopes = (claims["scopes"] as? List<*>)
        ?.mapNotNull { it as? String }
        ?.map { OAuthScopes.parse(it) }
        ?: emptyList()

    return ConnectBearerToken(
        claims.getValue("sub").toString(),
        claims.getValue("clientId").toString(),
        claims.getValue("creatorId").toString(),
        scopes,
    )
}

fun Request.getUserId(): String {
    return header("userId") ?: throw UnauthorizedException()
}

data class ConnectBearerToken(
    val userId: String,
    val clientId: String,
    val creatorId: String,
    val scopes: List<OAuthScopes>,
)

data class ConnectSubscriptionResponse(
    val subscribedAt: Instant?,
    val status: ConnectSubscriptionStatus,
)

enum class ConnectSubscriptionStatus {
    ACTIVE,
    EXPIRED,
    NEVER_SUBSCRIBED,
}

val connectPagedSubscriptionsResponse = ConnectPagedSubscriptionsResponse(
    data = listOf(
        ConnectSubscriptionsResponse(
            subscribedAt = Instant.now(),
            expiresAt = Instant.now(),
            name = "name",
            email = "email",
        ),
    ),
    pageSize = 10,
    currentPage = 0,
)
