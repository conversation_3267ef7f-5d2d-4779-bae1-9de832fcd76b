package hero.api.user.scripts

import hero.baseutils.SystemEnv
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Subscriber
import java.time.Instant
import java.time.LocalDate
import java.time.YearMonth
import java.time.ZoneOffset

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)

    // TODO we should be able to generate google sheet directly by
    // - creator
    // - time from - to
    // - granularity - day/month

    val creatorId = "infosdnynrsy"
    val yearMonth = YearMonth.of(2023, 6)

    val subs = firestore.typedCollectionOf(Subscriber)
        .where(Subscriber::creatorId).isEqualTo(creatorId)
        .fetchAll()

    val startOfMonth = yearMonth.atDay(1).atStartOfDay(ZoneOffset.UTC).toInstant()
    val endOfMonth = yearMonth.plusMonths(1).atDay(1).atStartOfDay(ZoneOffset.UTC).toInstant()

    val newSubs = subs
        .filter { it.subscribed > startOfMonth }
        .map { it.subscribed.atZone(ZoneOffset.UTC).toLocalDate() }
        .groupingBy { it }
        .eachCount()
        .toSortedMap()

    val endedSubs = subs
        .filter { it.expires != null && it.expires!! > startOfMonth && it.expires!! < minOf(endOfMonth, Instant.now()) }
        .map { it.expires!!.atZone(ZoneOffset.UTC).toLocalDate() }
        .groupingBy { it }
        .eachCount()
        .toSortedMap()

    println("$creatorId\tSubscribers\tLeavers")
    (1..yearMonth.lengthOfMonth())
        .map { dayOfMonth -> LocalDate.of(yearMonth.year, yearMonth.month, dayOfMonth) }
        .forEach {
            val subscribers = newSubs[it] ?: 0
            val leavers = endedSubs[it] ?: 0
            println("$it\t$subscribers\t-$leavers")
        }
}
