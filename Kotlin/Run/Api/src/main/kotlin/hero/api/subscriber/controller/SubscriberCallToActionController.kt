package hero.api.subscriber.controller

import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.http4k.auth.getJwtUser
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.model.User
import hero.model.topics.EmailPublished
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class SubscriberCallToActionController(
    private val usersCollection: TypedCollectionReference<User>,
    private val pubSub: PubSub,
    private val hostname: String,
) {
    @Suppress("unused")
    val routeSendCTASubscribeInfoMail: ContractRoute =
        ("/v1/ctas/subscribe-info-mail").post(
            summary = "Send info about subscribe info",
            tag = "CTA",
            responses = listOf(Status.OK to Unit),
            parameters = object {},
            receiving = CTASubscribeInfoMailRequest("cestmir"),
            handler = { request, _ ->
                val jwtUser = request.getJwtUser()
                val body = lens<CTASubscribeInfoMailRequest>(request)
                val user = usersCollection[jwtUser.id].get()
                val creator = usersCollection[body.creatorId].get()

                val email = user.email
                if (email != null) {
                    pubSub.publish(
                        EmailPublished(
                            from = creator.name,
                            to = email,
                            template = "subscribe-web-info",
                            variables = listOf(
                                "user-name" to user.name,
                                "creator-link" to "$hostname/${creator.path}/subscribe",
                                "creator-name" to creator.name,
                            ),
                            language = user.language,
                        ),
                    )
                }

                Response(Status.NO_CONTENT)
            },
        )
}

data class CTASubscribeInfoMailRequest(
    val creatorId: String,
)
