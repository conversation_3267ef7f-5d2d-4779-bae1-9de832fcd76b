package hero.api.user.controller

import hero.api.user.controller.dto.exampleUserResponse
import hero.api.user.controller.dto.toResponse
import hero.api.user.service.FeaturedCreatorsQueryService
import hero.api.user.service.GetCreatorsSortedByMostNewSubs
import hero.api.user.service.GetCreatorsSortedByRecentPosts
import hero.api.user.service.GetCreatorsSortedBySubs
import hero.api.user.service.GetCreatorsSortedByVerifiedAt
import hero.baseutils.minusDays
import hero.core.data.PageRequest
import hero.core.data.toResponse
import hero.exceptions.http.ForbiddenException
import hero.http4k.controller.HeaderUtils
import hero.http4k.controller.QueryUtils
import hero.http4k.extensions.body
import hero.http4k.extensions.enum
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Query
import java.time.Instant

class FeaturedCreatorsController(
    private val featuredCreatorsQueryService: FeaturedCreatorsQueryService,
) {
    @Suppress("Unused")
    val routeGetPopularCreators: ContractRoute =
        ("/v1/featured-creators").get(
            summary = "Get featured creators",
            tag = "Users",
            hideFromOpenApi = true,
            parameters = object {
                val type = Query.enum<FeaturedByMetric>().required("type", "Featured by type")
                val pageSize = QueryUtils.pageSize()
            },
            responses = listOf(Status.OK example exampleUserResponse),
            handler = { request, parameters ->
                if (!HeaderUtils.hasValidApiKey(request)) {
                    throw ForbiddenException()
                }

                val type = parameters.type(request)
                val pageSize = parameters.pageSize(request)
                val pageable = PageRequest(pageSize = pageSize)

                val featuredCreators = when (type) {
                    FeaturedByMetric.POPULAR -> {
                        featuredCreatorsQueryService.execute(GetCreatorsSortedBySubs(pageable, ignoredCreatorIds))
                    }

                    FeaturedByMetric.RECENTLY_ACTIVE -> {
                        featuredCreatorsQueryService.execute(
                            GetCreatorsSortedByRecentPosts(
                                pageable,
                                ignoredCreatorIds,
                            ),
                        )
                    }

                    FeaturedByMetric.TRENDING -> {
                        val since = Instant.now().minusDays(7)
                        featuredCreatorsQueryService.execute(
                            GetCreatorsSortedByMostNewSubs(
                                pageable,
                                since,
                                ignoredCreatorIds,
                            ),
                        )
                    }

                    FeaturedByMetric.NEW_CREATORS -> {
                        featuredCreatorsQueryService.execute(GetCreatorsSortedByVerifiedAt(pageable, ignoredCreatorIds))
                    }
                }

                val toResponse = featuredCreators.toResponse { it.toResponse(listOf()) }
                Response(Status.OK)
                    .body(toResponse)
            },
        )
}

enum class FeaturedByMetric {
    POPULAR,
    TRENDING,
    RECENTLY_ACTIVE,
    NEW_CREATORS,
}

private val ignoredCreatorIds = listOf("infoheroherokamniiih")
