package hero.functions

import com.google.cloud.functions.BackgroundFunction
import com.google.cloud.functions.Context
import com.google.cloud.logging.Synchronicity
import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.fromBase64
import hero.baseutils.log
import hero.baseutils.retryOn
import hero.core.sentry.initializeSentry
import hero.exceptions.http.HttpStatusException
import hero.jackson.jackson
import io.sentry.Sentry
import io.sentry.SentryLevel
import java.io.IOException
import java.lang.reflect.ParameterizedType
import java.time.Duration
import java.time.Instant
import java.util.concurrent.ExecutionException
import kotlin.reflect.KClass

// https://cloud.google.com/functions/docs/calling/pubsub#functions_calling_pubsub-java
// https://github.com/GoogleCloudPlatform/java-docs-samples/blob/master/functions/pubsub/subscribe-to-topic/src/main/java/functions/SubscribeToTopic.java
abstract class PubSubSubscriber<T : Any>(
    environmentVariables: EnvironmentVariables = SystemEnv,
    private val retryable: Boolean = false,
) : BackgroundFunction<PubSubMessage> {
    // https://stackoverflow.com/questions/66074297/kotlin-class-generics-without-duplication/66088037#66088037
    private val topic: KClass<T> = extractTypeParam<T>(0).kotlin

    init {
        // https://github.com/googleapis/java-logging/issues/432
        if (environmentVariables.environment != "test") {
            log.synchronicity = Synchronicity.SYNC
            log.debug("Consumer of ${topic.simpleName} is being started.")
        }

        initializeSentry(environmentVariables.environment, mapOf("cf.name" to this::class.java.simpleName))
    }

    abstract fun consume(payload: T)

    private fun <T> extractTypeParam(paramIndex: Int): Class<T> {
        require(PubSubSubscriber::class.java == javaClass.superclass) {
            "PubSubSubscriber subclass $javaClass should directly extend PubSubSubscriber"
        }
        @Suppress("UNCHECKED_CAST")
        return (javaClass.genericSuperclass as ParameterizedType).actualTypeArguments[paramIndex] as Class<T>
    }

    override fun accept(
        message: PubSubMessage,
        context: Context,
    ) {
        val now = Instant.now()
        val seconds = Duration.between(Instant.parse(context.timestamp()), now).seconds
        if (retryable && seconds > 3600) {
            log.fatal("Not retrying event ${context.eventId()} anymore, has been failing for $seconds seconds")
            Sentry.configureScope {
                it.setContexts("Now timestamp", now)
                Sentry.captureMessage("Failed to consume ${context.eventId()}")
            }

            return
        }
        Sentry.pushScope().use {
            try {
                val json = message.data.fromBase64()
                val payload = jackson.readValue(json, topic.java)
                Sentry.configureScope {
                    it.setContexts("Pub-Sub message", payload)
                }
                retryOn(IOException::class, ExecutionException::class) {
                    consume(payload)
                }
            } catch (e: HttpStatusException) {
                log.error(e.message)
                reportError(SentryLevel.WARNING, e)
                if (retryable) {
                    log.fatal("Failed to consume event from topic ${topic.simpleName} but will retry")
                    throw e
                }
            } catch (e: Throwable) {
                log.fatal("Failed to consume ${topic.simpleName}: ${e.message}", cause = e)
                reportError(SentryLevel.FATAL, e)
                if (retryable) {
                    log.fatal("Failed to consume event from topic ${topic.simpleName} but will retry")
                    throw e
                }
            }
        }
        log.debug("Message ${topic.simpleName} was processed.")
    }

    private fun reportError(
        level: SentryLevel,
        exception: Throwable,
    ) {
        Sentry.configureScope {
            it.level = level
        }

        Sentry.captureException(exception)
    }
}

data class PubSubMessage(
    val data: String,
    var attributes: Map<String, String> = mapOf(),
    var messageId: String,
    var publishTime: String,
)
