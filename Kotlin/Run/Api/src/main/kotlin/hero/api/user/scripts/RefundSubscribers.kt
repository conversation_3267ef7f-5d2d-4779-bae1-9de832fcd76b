package hero.api.user.scripts

import com.stripe.param.SubscriptionSearchParams
import hero.baseutils.SystemEnv
import hero.gcloud.PubSub
import hero.model.CancelledByRole
import hero.model.Currency
import hero.model.topics.RefundMethod
import hero.model.topics.SubscriptionCancelRequest
import hero.stripe.service.StripeClients

fun main() {
    val stripeClients = StripeClients(
        keysEu = SystemEnv.stripeKeyEu,
        keysUs = SystemEnv.stripeKeyUs,
    )
    val pubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject)
    val creatorsToRefund = listOf("creatorId")
    val currency = Currency.EUR

    for (creatorId in creatorsToRefund) {
        stripeClients[currency]
            .subscriptions()
            .search(
                SubscriptionSearchParams.builder().setQuery(
                    "metadata[\"creatorId\"]:\"$creatorId\" AND status:\"active\"",
                ).build(),
            )
            .autoPagingIterable()
            .forEach {
                pubSub.publish(
                    SubscriptionCancelRequest(
                        subscriptionId = it.id,
                        cancelledBy = "infoheroherokamniiih",
                        cancelledByRole = CancelledByRole.MODERATOR,
                        atPeriodEnd = false,
                        refundMethod = RefundMethod.REFUND_IF_NOT_PAID_OUT,
                        currency = currency,
                    ),
                )
            }
        println(
            "https://console.cloud.google.com/firestore/databases/-default-/data/panel/prod-users" +
                "/$creatorId?project=heroheroco",
        )
    }
}
