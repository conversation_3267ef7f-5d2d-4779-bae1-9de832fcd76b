package hero.test.time

import java.time.Clock
import java.time.Instant
import java.time.ZoneId
import kotlin.time.Duration
import kotlin.time.toJavaDuration

class TestClock(private var instant: Instant, private val zone: ZoneId = ZoneId.of("UTC")) : Clock() {
    override fun getZone(): ZoneId {
        return zone
    }

    override fun withZone(zone: ZoneId): Clock {
        return TestClock(instant, zone)
    }

    override fun instant(): Instant {
        return instant
    }

    operator fun plusAssign(amount: Duration) {
        instant += amount.toJavaDuration()
    }

    operator fun minusAssign(amount: Duration) {
        instant -= amount.toJavaDuration()
    }
}
