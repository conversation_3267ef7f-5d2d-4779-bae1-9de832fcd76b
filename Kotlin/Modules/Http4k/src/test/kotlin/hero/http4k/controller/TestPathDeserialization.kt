package hero.http4k.controller

import org.http4k.contract.div
import org.http4k.contract.meta
import org.http4k.core.HttpHandler
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path
import org.http4k.lens.regex
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class TestPathDeserialization {
    // internal test for https://github.com/http4k/http4k/issues/536
    @Test
    fun `should deserialize path`() {
        lateinit var receivedPath: String
        val handler: HttpHandler = ("path" / Path.regex("([a-z|]+)").of("userId")) meta {
        } bindContract Method.POST to { path: String ->
            {
                receivedPath = path
                Response(Status.NO_CONTENT)
            }
        }

        handler(Request(Method.POST, "/path/one%7Ctwo%7Cthree"))
        assertEquals("one|two|three", receivedPath)
    }
}
