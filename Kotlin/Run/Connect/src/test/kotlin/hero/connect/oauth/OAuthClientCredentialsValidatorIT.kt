package hero.connect.oauth

import dev.forkhandles.result4k.failureOrNull
import dev.forkhandles.result4k.valueOrNull
import hero.test.IntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.security.oauth.server.ClientId
import org.http4k.security.oauth.server.InvalidClientCredentials
import org.http4k.security.oauth.server.TokenRequest
import org.http4k.security.oauth.server.accesstoken.GrantType
import org.junit.jupiter.api.Test
import java.util.UUID

class OAuthClientCredentialsValidatorIT : IntegrationTest() {
    @Test
    fun `should return failure if secret do not match`() {
        val underTest = OAuthClientCredentialsValidator(lazyTestContext)

        testHelper.createOAuthClient(
            id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
            redirectUris = listOf("https://redirect.url"),
            secret = "SECRET",
        )

        val result = underTest.validateCredentials(
            Request(Method.GET, ""),
            TokenRequest(
                GrantType.AuthorizationCode,
                ClientId("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
                "INVALID_SECRET",
                null,
                null,
                listOf(),
                null,
                null,
                null,
                null,
            ),
        )

        assertThat(result.failureOrNull()).isEqualTo(InvalidClientCredentials)
    }

    @Test
    fun `should return success if correct secret was used`() {
        val underTest = OAuthClientCredentialsValidator(lazyTestContext)

        testHelper.createOAuthClient(
            id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
            redirectUris = listOf("https://redirect.url"),
            secret = "SECRET",
        )

        val result = underTest.validateCredentials(
            Request(Method.GET, ""),
            TokenRequest(
                GrantType.AuthorizationCode,
                ClientId("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
                "SECRET",
                null,
                null,
                listOf(),
                null,
                null,
                null,
                null,
            ),
        )

        assertThat(result.valueOrNull()).isNotNull()
    }
}
