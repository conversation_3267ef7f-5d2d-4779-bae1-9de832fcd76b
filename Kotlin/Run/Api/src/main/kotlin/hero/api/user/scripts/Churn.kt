package hero.api.user.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.plusDays
import hero.baseutils.toYearMonth
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.get
import hero.model.Subscriber
import hero.model.SubscriberStatus
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant
import java.time.YearMonth
import java.util.TreeMap

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject)

    val collection = firestore["${production.envPrefix}-${Subscriber.collectionName}"]
    val list = collection.fetchAll<Subscriber>()
    val subscribers = TreeMap<YearMonth, Int>()
    val leavers = TreeMap<YearMonth, Int>()
    val leaversAtPeriodEnd = TreeMap<YearMonth, Int>()
    val leaversExpired = TreeMap<YearMonth, Int>()
    val tiers = TreeMap<YearMonth, MutableList<Int>>()
    list.forEach {
        var yearMonth = it.subscribed.toYearMonth()
        val expires = it.expires
            // if cancelAtPeriodEnd is not set, we expect the subscription to continue
            ?.let { exp -> if (it.cancelAtPeriodEnd || exp < Instant.now()) exp else exp.plusDays(31) }
            ?.toYearMonth()
        val end = (expires ?: YearMonth.now().plusMonths(1))
        while (yearMonth <= end) {
            subscribers[yearMonth] = subscribers.getOrDefault(yearMonth, 0) + 1
            tiers.getOrPut(yearMonth) { mutableListOf() }.add(it.tierId.replace("[A-Z]*".toRegex(), "").toInt())
            yearMonth = yearMonth.plusMonths(1)
        }
        leavers[end] = leavers.getOrDefault(end, 0) + 1
        if (it.cancelAtPeriodEnd) {
            leaversAtPeriodEnd[end] = leaversAtPeriodEnd.getOrDefault(end, 0) + 1
        }
        if (it.status == SubscriberStatus.INCOMPLETE_EXPIRED) {
            leaversExpired[end] = leaversExpired.getOrDefault(end, 0) + 1
        }
    }
    val churn = TreeMap<YearMonth, BigDecimal>()
    subscribers.keys.forEach {
        churn[it] = leavers.getOrDefault(it, 0).toBigDecimal().setScale(4)
            .divide(subscribers.getOrDefault(it, 0).toBigDecimal().setScale(4), RoundingMode.HALF_UP)
            .movePointRight(2)
    }
    val tierAvg = tiers.map { (yearMonth, tiers) ->
        yearMonth to tiers.sum().toBigDecimal().setScale(2).divide(tiers.size.toBigDecimal(), RoundingMode.HALF_UP)
    }.toMap()

    println("subscribers: " + subscribers.filterDates())
    println("leavers: " + leavers.filterDates())
    println("leavers at period end: " + leaversAtPeriodEnd.filterDates())
    println("leavers expired: " + leaversExpired.filterDates())
    println("churn: " + churn.filterDates())
    println("avg tier: " + tierAvg.filterDates())
}

fun Map<YearMonth, Number>.filterDates() = filter { it.key >= YearMonth.of(2021, 1) && it.key <= YearMonth.now() }
