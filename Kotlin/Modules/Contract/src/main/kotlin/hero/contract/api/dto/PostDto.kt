package hero.contract.api.dto

import hero.core.data.SimplePageResponse
import hero.model.Chapter
import hero.model.PostAssetDto
import hero.model.PostCounts
import hero.model.topics.PostState
import java.time.Instant

data class PostResponse(
    val id: String,
    val state: PostState,
    val text: String?,
    val textHtml: String?,
    val textDelta: String?,
    val fullAsset: Boolean?,
    val excludeFromRss: Boolean?,
    val pinnedAt: Instant?,
    val assets: List<PostAssetDto>,
    val categories: List<CategoryResponse>,
    val counts: PostCounts,
    val publishedAt: Instant?,
    val price: Long?,
    val assetsCount: Int?,
    val savedPostInfo: SavedCreatorPostInfoResponse?,
    val relationships: PostRelationships,
    val chapters: List<Chapter>,
    val isAgeRestricted: Boolean,
    val isSponsored: Boolean,
    val pollId: String?,
)

data class PostRelationships(
    val userId: String,
    val parentId: String?,
    val siblingId: String?,
    val messageThreadId: String?,
)

data class SavedCreatorPostInfoResponse(val id: String, val savedAt: Instant)

data class PagedPostResponse(
    override val content: List<PostResponse>,
    override val hasNext: Boolean,
    override val afterCursor: String? = null,
    override val beforeCursor: String? = null,
) : SimplePageResponse<PostResponse>

data class CategoryResponse(
    val id: String,
    val name: String,
    val slug: String,
)
