package hero.api.statistics.controller

import hero.api.statistics.controller.dto.ExpectedIncomeResponse
import hero.api.statistics.controller.dto.MostViewedPostsResponse
import hero.api.statistics.controller.dto.SubscriberDailyStatisticsResponse
import hero.api.statistics.controller.dto.exampleExpectedIncomeResponse
import hero.api.statistics.controller.dto.exampleMostViewedPostsResponse
import hero.api.statistics.controller.dto.examplePostWithCompleteStatsResponse
import hero.api.statistics.controller.dto.exampleSubscriberDailyStatisticsResponse
import hero.api.statistics.controller.dto.toDto
import hero.api.statistics.service.GetCreatorsMostViewedPosts
import hero.api.statistics.service.GetExpectedIncome
import hero.api.statistics.service.GetPostViewStats
import hero.api.statistics.service.GetSubscriberStatistics
import hero.api.statistics.service.IncomeQueryService
import hero.api.statistics.service.PostStatisticsQueryService
import hero.api.statistics.service.SubscriberStatisticsQueryService
import hero.exceptions.http.ForbiddenException
import hero.http4k.auth.getJwtUser
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.body
import hero.http4k.extensions.get
import hero.model.Role
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.instant
import org.http4k.lens.int
import org.http4k.lens.string

class StatisticsController(
    private val subscriberStatisticsQueryService: SubscriberStatisticsQueryService,
    private val postStatisticsQueryService: PostStatisticsQueryService,
    private val incomeQueryService: IncomeQueryService,
) {
    @Suppress("unused")
    val routeGetSubscriptions: ContractRoute =
        ("/v1/users" / creatorIdPath / "statistics" / "subscribers").get(
            summary = "Get subscriber daily statistics",
            tag = "Statistics",
            parameters = object {
                val from = fromQueryParam
                val to = toQueryParam
            },
            responses = listOf(Status.OK to exampleSubscriberDailyStatisticsResponse),
            handler = { request, parameters, creatorId, _, _ ->
                val user = request.getJwtUser()
                val from = parameters.from(request)
                val to = parameters.to(request)

                if (user.id != creatorId && user.roleIndex != Role.MODERATOR.ordinal) {
                    throw ForbiddenException()
                }

                val query = GetSubscriberStatistics(creatorId, from, to)
                val stats = subscriberStatisticsQueryService.execute(query)

                Response(Status.OK).body(SubscriberDailyStatisticsResponse(stats))
            },
        )

    @Suppress("unused")
    val routeGetMostViewed: ContractRoute =
        ("/v1/users" / creatorIdPath / "statistics" / "most-viewed-posts").get(
            summary = "Get 5 most viewed posts for a creator",
            tag = "Statistics",
            parameters = object {
                val from = fromQueryParam
                val to = toQueryParam
                val limit = Query.int().defaulted(
                    "limit",
                    5,
                    "Max number of most viewed posts to return, default is 5",
                )
            },
            responses = listOf(Status.OK to exampleMostViewedPostsResponse),
            handler = { request, parameters, creatorId, _, _ ->
                val user = request.getJwtUser()
                val from = parameters.from(request)
                val to = parameters.to(request)
                val limit = parameters.limit(request)

                if (user.id != creatorId && user.roleIndex != Role.MODERATOR.ordinal) {
                    throw ForbiddenException()
                }

                val stats = postStatisticsQueryService
                    .execute(GetCreatorsMostViewedPosts(creatorId = creatorId, from = from, to = to, limit = limit))
                    .map { it.toDto() }

                Response(Status.OK).body(MostViewedPostsResponse(stats))
            },
        )

    @Suppress("unused")
    val routeGetPostViewStats: ContractRoute =
        ("/v1/users" / creatorIdPath / "statistics" / "posts" / postIdPath).get(
            summary = "Get statistics for specific post",
            tag = "Statistics",
            parameters = object {},
            responses = listOf(Status.OK to examplePostWithCompleteStatsResponse),
            handler = { request, _, creatorId, _, _, postId ->
                val user = request.getJwtUser()

                if (user.id != creatorId && user.roleIndex != Role.MODERATOR.ordinal) {
                    throw ForbiddenException()
                }

                val stats = postStatisticsQueryService.execute(GetPostViewStats(postId, creatorId))

                Response(Status.OK).body(stats.toDto())
            },
        )

    @Suppress("unused")
    val routeGetExpectedIncome: ContractRoute =
        ("/v1/users" / creatorIdPath / "statistics" / "income" / "expected").get(
            summary = "Get expected income statistics for next month",
            tag = "Statistics",
            parameters = object {},
            responses = listOf(Status.OK to exampleExpectedIncomeResponse),
            handler = { request, _, creatorId, _, _, _ ->
                val user = request.getJwtUser()

                if (user.id != creatorId && user.roleIndex != Role.MODERATOR.ordinal) {
                    throw ForbiddenException()
                }

                val expectedIncome = incomeQueryService.execute(GetExpectedIncome(creatorId))

                Response(Status.OK).body(
                    ExpectedIncomeResponse(
                        grossIncomeCents = expectedIncome.grossIncomeCents,
                        netIncomeCents = expectedIncome.netIncomeCents,
                    ),
                )
            },
        )
}

private val toQueryParam = Query.instant().required(
    "to",
    "End date for statistics (required; inclusive; cannot be in the future)",
)

private val fromQueryParam = Query.instant().required(
    "from",
    "Start date for statistics (required; inclusive)",
)
private val postIdPath = Path.string().of("postId", "Id of a post")
private val creatorIdPath = Path.userId().of("creatorId")
