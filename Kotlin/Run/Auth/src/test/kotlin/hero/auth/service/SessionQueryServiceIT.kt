package hero.auth.service

import hero.baseutils.minus
import hero.baseutils.plus
import hero.exceptions.http.ForbiddenException
import hero.http4k.auth.REFRESH_TOKEN_VALIDITY_SECONDS
import hero.test.IntegrationTest
import hero.test.TestRepositories
import hero.test.time.TestClock
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class SessionQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetActiveSessions {
        @Test
        fun `should query only non revoked and refreshable sessions`() {
            val now = Instant.ofEpochSecond(1729514035)
            val testClock = TestClock(now)
            val underTest = SessionQueryService(TestRepositories.sessionRepository, lazyTestContext, testClock)
            val activeSession = testHelper.createSession("cestmir", refreshedAt = now + 5.seconds)
            // stale session, cannot be refreshed
            testHelper.createSession("cestmir", refreshedAt = now.minusSeconds(REFRESH_TOKEN_VALIDITY_SECONDS))
            // revoked session
            testHelper.createSession("cestmir", revoked = true)

            val sessions = underTest.execute(GetActiveSessions("cestmir"))

            assertThat(sessions).containsOnly(activeSession)
        }

        @Test
        fun `should order sessions by refreshedAt`() {
            val now = Instant.ofEpochSecond(1729514035)
            val testClock = TestClock(now)
            val underTest = SessionQueryService(TestRepositories.sessionRepository, lazyTestContext, testClock)
            val session1 = testHelper.createSession("cestmir", refreshedAt = now + 5.seconds)
            val session2 = testHelper.createSession("cestmir", refreshedAt = now - 5.seconds)
            val session3 = testHelper.createSession("cestmir", refreshedAt = now)

            val sessions = underTest.execute(GetActiveSessions("cestmir"))

            assertThat(sessions).containsExactly(
                session1,
                session3,
                session2,
            )
        }
    }

    @Nested
    inner class FindSession {
        @Test
        fun `should find the session`() {
            val underTest = SessionQueryService(TestRepositories.sessionRepository, lazyTestContext)
            val session = testHelper.createSession("cestmir")

            val result = underTest.execute(FindSession(session.id, "cestmir"))

            assertThat(result).isEqualTo(session)
        }

        @Test
        fun `should find session even if it's revoked`() {
            val underTest = SessionQueryService(TestRepositories.sessionRepository, lazyTestContext)
            val session = testHelper.createSession("cestmir", revoked = true)

            val result = underTest.execute(FindSession(session.id, "cestmir"))

            assertThat(result).isEqualTo(session)
        }

        @Test
        fun `only owner of the session can query it`() {
            val underTest = SessionQueryService(TestRepositories.sessionRepository, lazyTestContext)
            val session = testHelper.createSession("cestmir", revoked = true)

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(FindSession(session.id, "libor"))
            }
        }
    }
}
