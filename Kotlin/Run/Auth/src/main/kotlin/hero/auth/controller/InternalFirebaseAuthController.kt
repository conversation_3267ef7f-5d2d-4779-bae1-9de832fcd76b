package hero.auth.controller

import com.google.firebase.auth.FirebaseAuth
import hero.exceptions.http.ForbiddenException
import hero.http4k.controller.HeaderUtils
import hero.http4k.extensions.delete
import hero.http4k.extensions.example
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path

class InternalFirebaseAuthController(
    private val firebaseAuth: FirebaseAuth,
) {
    @Suppress("Unused")
    val routeDeleteUser: ContractRoute =
        ("/internal/v1/firebase" / Path.of("firebaseId")).delete(
            summary = "Internal endpoint to delete user's email",
            tag = "Authentication",
            hideFromOpenApi = true,
            parameters = object {},
            responses = listOf(Status.NO_CONTENT example Unit),
            handler = { request, _, firebaseId ->
                if (!HeaderUtils.hasValidApiKey(request)) {
                    throw ForbiddenException()
                }

                firebaseAuth.deleteUser(firebaseId)

                Response(Status.NO_CONTENT)
            },
        )
}
