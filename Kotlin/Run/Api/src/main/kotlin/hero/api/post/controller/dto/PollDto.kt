package hero.api.post.controller.dto

import java.time.Instant

data class PollResponse(
    val id: String,
    val options: List<PollOptionResponse>,
    val deadline: Instant,
)

data class PollOptionResponse(
    val id: String,
    val title: String,
    val voteCount: Long,
    val hasVotedFor: Boolean,
)

data class CastVotesRequest(
    val votes: List<VoteRequest>,
)

data class VoteRequest(
    val optionId: String,
)

data class UpdatePollRequest(
    val hasEnded: Boolean,
)
