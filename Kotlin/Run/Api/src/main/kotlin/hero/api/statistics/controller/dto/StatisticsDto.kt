package hero.api.statistics.controller.dto

import hero.api.statistics.service.PostCompleteStats
import hero.api.statistics.service.PostViewStats
import hero.api.statistics.service.SubscriberStatsData
import hero.contract.api.dto.PostResponse

data class SubscriberDailyStatisticsResponse(
    val data: List<SubscriberStatsData>,
)

data class MostViewedPostsResponse(
    val data: List<PostWithViewStatsResponse>,
)

data class PostWithViewStatsResponse(
    val post: PostResponse,
    val stats: PostViewStats,
)

data class PostWithCompleteStatsResponse(
    val post: PostResponse,
    val stats: PostCompleteStats,
)

data class ExpectedIncomeResponse(
    val grossIncomeCents: Int,
    val netIncomeCents: Int,
)
