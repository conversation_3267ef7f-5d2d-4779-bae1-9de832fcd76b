package hero.api.post.controller

import com.google.cloud.firestore.Query.Direction
import hero.api.category.repository.CategoriesRepository
import hero.api.post.controller.dto.postDtoExample
import hero.api.post.repository.PostRepositoryDeprecated
import hero.api.post.service.GetPostByIdQuery
import hero.api.post.service.PostQueryService
import hero.api.post.service.PostServiceDeprecated
import hero.api.post.service.toDto
import hero.api.user.repository.UsersRepository
import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.UnauthorizedException
import hero.http4k.auth.parseJwtUser
import hero.http4k.config.PAGE_SIZE_MAX
import hero.http4k.controller.QueryUtils
import hero.http4k.extensions.ErrorResponse
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.delete
import hero.http4k.extensions.enum
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.patch
import hero.http4k.extensions.post
import hero.http4k.extensions.userAgent
import hero.jackson.map
import hero.model.CategoryDto
import hero.model.ListResponseMeta
import hero.model.Post
import hero.model.PostDto
import hero.model.PostDtoListIncluded
import hero.model.PostDtoListResponse
import hero.model.PostRenderMeta
import hero.model.topics.PostState
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.boolean
import org.http4k.lens.nonBlankString
import org.http4k.lens.nonEmptyString
import org.http4k.lens.regex
import org.http4k.lens.string
import java.time.Instant

class PostsJsonApiController(
    private val postRepository: PostRepositoryDeprecated,
    private val postServiceDeprecated: PostServiceDeprecated,
    private val postQueryService: PostQueryService,
    private val usersRepository: UsersRepository,
    private val categoriesRepository: CategoriesRepository,
) {
    @Suppress("unused")
    val routeGetPost: ContractRoute =
        ("/v2/posts" / Path.string().of("postId")).get(
            summary = "Get post by Id.",
            tag = "Posts",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(
                Status.OK example postDtoExample,
            ),
            meta = { it.markAsDeprecated() },
            handler = { request, _, postId ->
                val jwtUser = request.parseJwtUser()
                Response(Status.OK).body(postQueryService.handle(GetPostByIdQuery(postId, jwtUser?.id)))
            },
        )

    @Suppress("unused")
    val routeGetPosts: ContractRoute =
        "/v2/posts".get(
            summary = "Lists posts.",
            tag = "Posts",
            parameters = object {
                val query = Query.nonBlankString().optional("query", "Query string to search for.")
                val userId = Query.regex("([a-z-]+)")
                    .optional("userId", "Filter posts by its author - requesting user must be subscriber or author.")
                val parentId = Query.regex("([a-z0-9|-]+)").optional(
                    "parentId",
                    "Filter posts by its parent. Requesting user must be subscriber or author of root parent.",
                )
                val postIds =
                    Query.regex("([a-z0-9|-]+)").multi.optional(
                        "postIds[]",
                        "Select multiple posts by their id.",
                    )
                val categoryId =
                    Query.regex("([a-z0-9|-]+)").optional("categoryId", "Filter posts by categoryId.")
                val messageThreadId =
                    Query.nonEmptyString()
                        .optional("messageThreadId", "Filter posts by its message thread.")
                val includeRelated =
                    Query.boolean()
                        .defaulted("includeRelated", false, "Selects also parent and sibling posts.")
                val listDeleted =
                    Query.boolean()
                        .defaulted("listDeleted", false, "List deleted users as empty entities.")
                val pageBefore =
                    Query.regex("([a-z0-9|-]+)")
                        .optional("pageBefore", "Selects post appearing before given id.")
                val pageAfter =
                    Query.regex("([a-z0-9|-]+)")
                        .optional("pageAfter", "Selects post appearing after given id.")
                val pageIndex = QueryUtils.pageIndex()
                val pageSize = QueryUtils.pageSize()
                val sorting = Query.enum<Direction>().optional(
                    "sorting",
                    "Direction of sorting. Possible values: $directionValues, default is $descending.",
                )
                val include = Query.string()
                    .defaulted("include", "", "Entities to include. Possible values: ${listOf("categories", "user")}")
            },
            responses = listOf(
                Status.OK example PostDtoListResponse(
                    ListResponseMeta(0, true),
                    listOf(postDtoExample),
                    PostDtoListIncluded(),
                ),
            ),
            meta = { it.markAsDeprecated() },
            handler = { request, parameters ->
                val jwtUser = request.parseJwtUser()
                val query = parameters.query(request)
                if (query != null && query.length > 40) {
                    throw BadRequestException(
                        "Query string cannot be longer than 40 characters, given was ${query.length}.",
                    )
                }
                val parentId = parameters.parentId(request)
                val postIds = parameters.postIds(request)
                val includeRelated = parameters.includeRelated(request)
                val userId = parameters.userId(request)
                val messageThreadId = parameters.messageThreadId(request)
                val categoryId = parameters.categoryId(request)
                val sortingDirection = parameters.sorting(request) ?: Direction.DESCENDING
                val include = parameters.include(request).split(",")
                val listDeleted = parameters.listDeleted(request)
                val pageIndex = parameters.pageIndex(request)
                val pageAfter = parameters.pageAfter(request)
                val pageBefore = parameters.pageBefore(request)
                val pageSize = parameters.pageSize(request)
                if (pageSize > PAGE_SIZE_MAX) {
                    throw BadRequestException("Parameter pageSize is out of bounds.", mapOf())
                }

                val postTuples = when {
                    messageThreadId != null -> postRepository.getPostsByMessageThreadForPublicV1(
                        jwtUserId = jwtUser?.id,
                        messageThreadId = messageThreadId,
                        pageBefore = pageAfter,
                        pageAfter = pageBefore,
                        limit = pageSize + 1,
                        sortingDirection = sortingDirection,
                    )

                    postIds != null -> postRepository.getPostsByIdsForPublicV1(
                        jwtUserId = jwtUser?.id,
                        postIds = postIds,
                        includeRelated = includeRelated,
                        listDeleted = listDeleted,
                    )

                    userId != null -> postRepository.getPostsByUserPublic(
                        jwtUserId = jwtUser?.id,
                        userId = userId,
                        categoryId = categoryId,
                        listDeleted = listDeleted,
                        offset = pageIndex,
                        limit = pageSize + 1,
                        sortingDirection = sortingDirection,
                    )

                    parentId != null -> postRepository.getPostsByParentPublic(
                        jwtUserId = jwtUser?.id,
                        parentId = parentId,
                        listDeleted = listDeleted,
                        offset = pageIndex,
                        limit = pageSize + 1,
                        sortingDirection = sortingDirection,
                    )

                    else -> throw BadRequestException(
                        "One of messageThreadId, parentId, postIds or userId must be given.",
                    )
                }

                val posts = postTuples
                    // we must take pageSize BEFORE it gets filtered
                    .take(pageSize)
                    // only owners can see posts in PROCESSING state
                    .filter { (post, _) -> post.state != PostState.PROCESSING || post.userId == jwtUser?.id }
                    // only owners can see SCHEDULED posts
                    .filter { (post, _) -> post.published < Instant.now() || post.userId == jwtUser?.id }
                    // we always show text for messageThreads https://gitlab.com/heroheroco/general/-/issues/506
                    .let {
                        val (livestreams, other) = it.partition { (post, _) -> post.isLivestreamLive() }
                        livestreams + other
                    }
                    .mapNotNull { (post, renderMeta) -> toResponseCatching(post, renderMeta) }

                val categories = mutableListOf<CategoryDto>()
                val userIds = posts.map { it.relationships.user!!.id }.distinct()
                val users = usersRepository.getUsers(queryUserIds = userIds, limit = 1000, offset = 0)
                    .map {
                        val categoriesForUser = categoriesRepository.list(it.id)
                        categories += categoriesForUser
                        usersRepository.toDto(it, jwtUser?.id == it.id, categoriesForUser)
                    }

                Response(Status.OK)
                    .body(
                        PostDtoListResponse(
                            meta = ListResponseMeta(
                                pageIndex = pageIndex,
                                // this must be postTuples, BEFORE it gets filtered
                                hasNext = postTuples.size == pageSize + 1,
                            ),
                            posts = posts,
                            included = PostDtoListIncluded(
                                categories = categories,
                                users = users,
                            ),
                        ),
                    )
            },
        )

    private fun toResponseCatching(
        post: Post,
        renderMeta: PostRenderMeta,
    ) = try {
        post.toDto(renderMeta)
    } catch (e: Exception) {
        log.fatal("Cannot render post ${post.id}: ${e.message}", cause = e)
        null
    }

    @Suppress("unused")
    val routePostPosts: ContractRoute =
        "/v2/posts".post(
            summary = "Creates a new post.",
            tag = "Posts",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = postDtoExample,
            responses = listOf(
                Status.OK example postDtoExample,
            ),
            meta = { it.markAsDeprecated() },
            handler = { request, _ ->
                val jwtUser = request.parseJwtUser()
                    ?: throw UnauthorizedException()

                val updatesDto = lens<PostDto>(request)
                if (updatesDto.relationships.parent != null) {
                    // TODO validate user is allowed to post comments for certain post
                }
                if (
                    (updatesDto.relationships.parent != null || updatesDto.relationships.sibling != null) &&
                    updatesDto.relationships.messageThread != null
                ) {
                    throw BadRequestException(
                        "Cannot create post with both (parent||sibling) and messageThread relationship.",
                    )
                }

                // TODO revert later, see https://linear.app/herohero/issue/HH-2612
                val userAgent = request.userAgent
                val postDto = postServiceDeprecated.createPost(updatesDto, jwtUser.id, userAgent)
                if (postDto.relationships.parent == null) {
                    log.info("User created post.", updatesDto.map())
                }
                Response(Status.OK).body(postDto)
            },
        )

    @Suppress("unused")
    val routePatchPosts: ContractRoute =
        ("/v2/posts" / Path.string().of("postId")).patch(
            summary = "Updates given post.",
            tag = "Posts",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = postDtoExample,
            responses = listOf(
                Status.OK example postDtoExample,
                Status.FORBIDDEN to ErrorResponse("User can patch only their posts."),
            ),
            meta = { it.markAsDeprecated() },
            handler = { request, _, postId ->
                val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
                val updates = lens<PostDto>(request)

                // TODO revert later, see https://linear.app/herohero/issue/HH-2612
                val userAgent = request.userAgent
                val updatedPost = postServiceDeprecated.updatePost(postId, updates, jwtUser.id, userAgent)

                Response(Status.OK).body(updatedPost)
            },
        )

    @Suppress("unused")
    val routeDeletePosts: ContractRoute =
        ("/v1/posts" / Path.string().of("postId")).delete(
            summary = "Deletes given post.",
            tag = "Posts",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(
                Status.NO_CONTENT example Unit,
                Status.FORBIDDEN to ErrorResponse("User can delete only their posts."),
            ),
            handler = { request, _, postId ->
                val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
                postServiceDeprecated.deletePost(postId, jwtUser.id)
                Response(Status.NO_CONTENT)
            },
        )
}

private val directionValues = Direction.entries.map { it.name.lowercase() }
private val descending = Direction.DESCENDING.name.lowercase()
