package hero.stripe.model

import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.annotation.JsonProperty
import com.stripe.model.AccountLink
import com.stripe.model.Charge
import com.stripe.model.Refund
import com.stripe.param.AccountCreateParams
import hero.core.annotation.NoArg
import hero.core.data.EntityCollection
import hero.model.Currency
import java.time.Instant
import java.util.concurrent.TimeUnit

sealed class PayoutItem(val charge: Charge)

class PayoutChargeItem(charge: Charge) : PayoutItem(charge)

class PayoutRefundItem(val refund: Refund, charge: Charge) : PayoutItem(charge)

@NoArg
data class StripeKeys(
    val publicKey: String,
    val couponAccountId: String,
    val webhookSecrets: StripeWebhookSecrets,
)

@NoArg
data class StripeWebhookSecrets(
    val coupons: String,
    val paymentMethods: String,
    val charges: String,
    val subscriptions: String,
    val accounts: String,
    val payouts: String,
    val customers: String,
    val reports: String,
    val invoices: String,
)

data class StripeConnectResponse(
    val url: String,
    val expires: Instant,
) {
    companion object {
        fun of(stripeLink: AccountLink): StripeConnectResponse =
            StripeConnectResponse(
                stripeLink.url,
                Instant.ofEpochMilli(TimeUnit.SECONDS.toMillis(stripeLink.expiresAt)),
            )
    }
}

data class StripeLoginResponse(
    val url: String,
    val accountType: AccountCreateParams.Type,
)

@NoArg
data class StripePrice(
    val id: String,
    val tierId: String,
    val stripeId: String,
    val euOldStripeId: String? = null,
) {
    companion object : EntityCollection<StripePrice> {
        override val collectionName: String = "stripe-prices"
    }
}

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class StripeEventRequest<T : StripeEventDataInterface>(
    val id: String? = null,
    @JsonProperty("api_version")
    val apiVersion: String,
    val created: Long,
    val account: String? = null,
    @JsonProperty("data")
    val eventData: StripeEventData<T>? = null,
)

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class StripeEventData<T : StripeEventDataInterface>(
    @JsonProperty("object")
    val payload: T? = null,
)

data class StripeEventDataObject(
    @JsonProperty("object")
    override val objectType: String? = null,
    @JsonProperty("id")
    override val objectId: String? = null,
    @JsonProperty("api_version")
    override val apiVersion: String? = null,
    override val status: String? = null,
    @JsonProperty("customer")
    val customerId: String? = null,
    val currency: Currency? = null,
) : StripeEventDataInterface

data class StripeEventReportObject(
    @JsonProperty("object")
    override val objectType: String? = null,
    @JsonProperty("id")
    override val objectId: String? = null,
    @JsonProperty("api_version")
    override val apiVersion: String? = null,
    override val status: String? = null,
    @JsonProperty("data_available_start")
    val start: Long? = null,
    @JsonProperty("data_available_end")
    val end: Long? = null,
    val result: Map<String, Any>? = null,
) : StripeEventDataInterface

interface StripeEventDataInterface {
    val objectType: String?
    val objectId: String?
    val status: String?
    val apiVersion: String?
}

data class StripeFeeCsvReportGenerated(
    val currency: Currency,
    val url: String,
)

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class StripeAccountWebhook(
    @JsonProperty("object")
    override val objectType: String? = null,
    @JsonProperty("id")
    override val objectId: String? = null,
    @JsonProperty("api_version")
    override val apiVersion: String? = null,
    override val status: String? = null,
    val account: String? = null,
    val created: Int? = null,
    val livemode: Boolean? = null,
    @JsonProperty("pending_webhooks")
    val pendingWebhooks: Int? = null,
    val type: String? = null,
    val data: Map<String, Any?>? = null,
    val request: Map<String, Any?>? = null,
) : StripeEventDataInterface

enum class ChargeStatus {
    @JsonProperty("succeeded")
    SUCCEEDED,

    @JsonProperty("pending")
    PENDING,

    @JsonProperty("failed")
    FAILED,
}
