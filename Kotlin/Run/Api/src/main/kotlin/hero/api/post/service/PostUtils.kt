package hero.api.post.service

import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.where
import hero.model.Post
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.topics.PostState

// this file should be moved to some more common place

fun TypedCollectionReference<Subscriber>.fetchActiveSubscription(
    userId: String,
    creatorId: String,
) = this
    .where(Subscriber::userId).isEqualTo(userId)
    .and(Subscriber::creatorId).isEqualTo(creatorId)
    .and(Subscriber::status).isIn(SubscriberStatus.activeStatuses)
    .fetchSingle()

val allowedPostSatesForOwner = listOf(PostState.PUBLISHED, PostState.PROCESSING, PostState.SCHEDULED)

private val privatePostStates = setOf(PostState.DELETED, PostState.REVISION)

fun validatePostAccess(
    post: Post,
    requesterId: String?,
): Post {
    if (post.userId == requesterId) {
        if (post.state !in allowedPostSatesForOwner) {
            throw NotFoundException("Post ${post.id} was not found")
        }
    } else {
        if (post.state in privatePostStates) {
            throw NotFoundException("Post ${post.id} was not found")
        }
        if (post.state != PostState.PUBLISHED) {
            throw ForbiddenException("User $requesterId cannot access post ${post.id}")
        }
    }

    return post
}

fun TypedCollectionReference<Post>.getRootParent(comment: Post): Post {
    val parentPostId = comment.parentPostId
    if (parentPostId != null) {
        return this[parentPostId].get()
    }

    val parentId = comment.parentId
    if (parentId == null) {
        return comment
    } else {
        val parent = this[parentId].get()
        return getRootParent(parent)
    }
}

fun Post.toLabels() =
    mapOf(
        "userId" to userId,
        "postId" to id,
        "parentId" to parentId,
    )
