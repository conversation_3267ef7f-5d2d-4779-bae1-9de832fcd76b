package hero.stripe.service

import com.stripe.exception.ApiConnectionException
import com.stripe.exception.RateLimitException
import com.stripe.model.Coupon
import hero.baseutils.retryOn
import hero.baseutils.toAscii
import hero.model.CouponMethod
import java.io.IOException

private val characterFilter = "[^\\p{L}\\p{M}\\p{N}\\p{P}\\p{Z}\\p{Cf}\\p{Cs}\\s]".toRegex()

fun bankStatementDescriptor(creatorName: String): String {
    val cleanName = creatorName
        // avoid non-ascci characters in bank statements
        .toAscii()
        // avoid emojis
        .replace(characterFilter, "")
        // avoid forbidden characters
        .replace("[\"'.*]".toRegex(), " ")
        // join unnecessary spaces
        .replace("\\s+".toRegex(), " ")
        // bank statements always appear as uppercase
        .uppercase()

    val statementDescriptorRaw = "HEROHERO: $cleanName"
    return if (statementDescriptorRaw.length > 21) statementDescriptorRaw.take(21) + "…" else statementDescriptorRaw
}

/**
 * Stripe requests might either fail on IOExceptions or StripeExceptions like:
 * > Error while communicating with one of our backends.
 * We need to handle these to provide a smooth service.
 */
internal fun <T> retry(body: () -> T) =
    // we retry on IO and specific Stripe exceptions instantly and in case of RateLimitExceptions, we wait $RATE_LIMIT_WAIT millis
    retryOn(
        IOException::class,
        ApiConnectionException::class,
        body = { retryOn(RateLimitException::class, body = body, retryWaitMillis = RATE_LIMIT_WAIT) },
    )

/** public function to be used in not internal Stripe context */
fun <T> stripeRetry(body: () -> T) = retry(body)

fun Coupon.couponMethod() = if (percentOff != null) CouponMethod.TRIAL else CouponMethod.VOUCHER

internal const val RATE_LIMIT_WAIT = 10_000L
