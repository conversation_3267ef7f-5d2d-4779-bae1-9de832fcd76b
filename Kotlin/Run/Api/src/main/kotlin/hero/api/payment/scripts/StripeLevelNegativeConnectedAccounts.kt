package hero.api.payment.scripts

import com.stripe.net.RequestOptions
import com.stripe.param.AccountRejectParams
import com.stripe.param.AccountUpdateParams
import com.stripe.param.TransferCreateParams
import hero.baseutils.log
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Currency
import hero.model.User
import hero.model.UserStatus
import kotlin.concurrent.thread

fun main() {
    val (firestore, production, _, _, clients) = initializeStripeScript(true)
    val currency = Currency.EUR
    val rejectReason = AccountRejectParams.builder().setReason("terms_of_service").build()

    // val id = "alesfritschernaezpnii"
    val users = firestore.typedCollectionOf(User)
        .where(User::status).isEqualTo(UserStatus.DELETED)
        // .whereEqualTo(User::id.name, id)
        .fetchAll()

    users.forEach {
        thread {
            val destination = it.creator.stripeAccountId ?: return@thread
            try {
                val account = try {
                    clients[currency].accounts().retrieve(destination)
                } catch (e: Exception) {
                    return@thread
                }
                if ("rejected" in (account.requirements.disabledReason ?: "")) {
                    return@thread
                }
                val balance = clients[currency].balance().retrieve(
                    RequestOptions.builder().setStripeAccount(destination).build(),
                )
                val balanceAvailable = balance.available.firstOrNull()
                val availableAmount = balanceAvailable?.amount ?: 0L
                var refunded = false
                if (availableAmount < 0L) {
                    val balanceCurrency = balanceAvailable!!.currency
                    log.info(
                        "Balancing connected account ${it.id}/$destination with $balanceCurrency -$availableAmount.",
                    )
                    clients[currency].accounts()
                        .update(
                            account.id,
                            AccountUpdateParams.builder()
                                .setCapabilities(
                                    AccountUpdateParams.Capabilities.builder().setTransfers(
                                        AccountUpdateParams.Capabilities.Transfers.builder().setRequested(true).build(),
                                    )
                                        .build(),
                                )
                                .build(),
                        )
                    clients[currency].transfers().create(
                        TransferCreateParams.builder().setCurrency(balanceCurrency!!)
                            .setAmount(-availableAmount).setDestination(destination).build(),
                    )
                    refunded = true
                }

                val balancePending = balance.pending.firstOrNull()
                val pendingAmount = balancePending?.amount ?: 0L
                if (pendingAmount < 0L) {
                    val balanceCurrency = balancePending!!.currency
                    log.info("Balancing connected account $destination with $balanceCurrency -$pendingAmount.")
                    clients[currency]
                        .accounts()
                        .update(
                            account.id,
                            AccountUpdateParams.builder()
                                .setCapabilities(
                                    AccountUpdateParams.Capabilities.builder().setTransfers(
                                        AccountUpdateParams.Capabilities.Transfers.builder().setRequested(true).build(),
                                    )
                                        .build(),
                                )
                                .build(),
                        )
                    clients[currency].transfers().create(
                        TransferCreateParams.builder().setCurrency(balanceCurrency!!)
                            .setAmount(-pendingAmount).setDestination(destination).build(),
                    )
                    refunded = true
                }
                if (refunded) {
                    clients[currency].accounts().reject(account.id, rejectReason)
                }
            } catch (e: Exception) {
                log.error("${it.id}/$destination: ${e.message}")
            }
        }
    }

    /*
    val destination = invoice.chargeObject.destination
    val destinationPayment = invoice.chargeObject.transferObject.destinationPayment

    val payouts = stripe.listPayouts(destination).autoPagingIterable()
    val transactions = payouts
        .flatMap { stripe.listPayoutTransactions(destination, it.id).autoPagingIterable() }
        .filter { it.source.startsWith("py_") }
        .map { it.source }

    //val payment = client.charges().retrieve(transfer.destinationPayment, RequestOptions.builder().setStripeAccount("acct_1LQcF4PmRIAfVCUa").build())

    println(destinationPayment !in transactions)
     */

    // stripe.cancelSubscriptionsOfCreator("karolinasmolikovaecbpecps", atPeriodEnd = true, refundMethod = StripeService.RefundMethod.REFUND_NON_PAYOUT)
}
