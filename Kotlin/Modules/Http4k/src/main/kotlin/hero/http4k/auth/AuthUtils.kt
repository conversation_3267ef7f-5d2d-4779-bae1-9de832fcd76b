package hero.http4k.auth

import hero.baseutils.plusDays
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.UnauthorizedException
import hero.jwt.ACCESS_TOKEN
import hero.jwt.IMPERSONATION_TOKEN
import hero.jwt.JwtUser
import hero.jwt.REFRESH_TOKEN
import hero.jwt.ROLE_CLAIM
import hero.jwt.SESSION_ID
import hero.jwt.jwtParser
import hero.jwt.toJwt
import io.jsonwebtoken.Claims
import io.jsonwebtoken.ExpiredJwtException
import io.jsonwebtoken.JwtParser
import io.jsonwebtoken.UnsupportedJwtException
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.cookie.Cookie
import org.http4k.core.cookie.SameSite
import org.http4k.core.cookie.cookie
import java.security.SignatureException
import java.time.Instant

const val ACCESS_TOKEN_VALIDITY_SECONDS: Long = 10 * 60
const val REFRESH_TOKEN_VALIDITY_SECONDS: Long = 60 * 60 * 24 * 31

fun tokenExpirations(): Pair<Long, Long> {
    val now = Instant.now().epochSecond
    return Pair(now + ACCESS_TOKEN_VALIDITY_SECONDS, now + REFRESH_TOKEN_VALIDITY_SECONDS)
}

fun Request.authorizationOrNull(allowImpersonation: Boolean = true): String? {
    val impersonationCookieVal = cookie(IMPERSONATION_TOKEN)?.value
    if (!allowImpersonation && impersonationCookieVal != null) {
        throw ForbiddenException("Endpoint not accessible when impersonating.")
    }
    return impersonationCookieVal ?: cookie(ACCESS_TOKEN)?.value
}

@Suppress("ReturnCount")
// TODO: be warned, this method will not check if the user has been deleted
fun Request.parseJwtUser(allowImpersonation: Boolean = true): JwtUser? {
    // this must not be part of try-catch as it handles exceptions inside itself
    val token = authorizationOrNull(allowImpersonation) ?: return null
    return parseJwtUserInternal(token)
}

fun Request.isImpersonation(): Boolean = cookie(IMPERSONATION_TOKEN)?.value != null

fun Request.getJwtUser(allowImpersonation: Boolean = true): JwtUser =
    parseJwtUser(allowImpersonation) ?: throw UnauthorizedException()

fun parseJwtUser(token: String): JwtUser? = parseJwtUserInternal(token)

fun getJwtUser(token: String): JwtUser = parseJwtUserInternal(token) ?: throw UnauthorizedException()

fun Request.withAccessTokenCookie(token: String) = this.cookie(ACCESS_TOKEN, token)

fun Response.withAccessTokenCookie(token: String) = this.cookie(accessCookie(token))

fun Response.withRefreshTokenCookie(
    token: String,
    path: String,
) = this.cookie(refreshCookie(token, path))

fun Response.withDeletedRefreshTokenCookie(path: String) = this.cookie(refreshCookie("deleted", path).maxAge(0))

fun Response.withDeletedAccessTokenCookie() = this.cookie(accessCookie("deleted").maxAge(0))

fun Response.withImpersonateTokenCookie(token: String) = this.cookie(impersonateCookie(token))

fun Response.withDeletedImpersonateTokenCookie() = this.cookie(impersonateCookie("deleted").maxAge(0))

fun jwtFor(
    userId: String,
    expiry: Long = Instant.now().epochSecond + ACCESS_TOKEN_VALIDITY_SECONDS,
    role: Int = 0,
): String = JwtUser(userId, expiry, role).toJwt()

fun parseToken(
    token: String,
    jwtParser: JwtParser = jwtParser(),
): Claims? =
    try {
        val parsed = jwtParser.parseSignedClaims(token)
        parsed.payload as Claims
    } catch (e: UnsupportedJwtException) {
        throw UnauthorizedException()
    } catch (e: SignatureException) {
        throw UnauthorizedException()
    } catch (e: ForbiddenException) {
        // ForbiddenExceptions should be left unchanged.
        throw e
    } catch (e: Exception) {
        null
    }

private const val ONE_YEAR_DAYS = 365L

private fun parseJwtUserInternal(token: String): JwtUser? {
    val claims = parseToken(token) ?: return null
    try {
        val userId = claims.subject ?: return null
        val expiry = claims.expiration?.toInstant()?.epochSecond ?: return null
        val role = claims[ROLE_CLAIM]?.toString()?.toInt() ?: return null
        val sessionId = claims[SESSION_ID]?.toString()
        return JwtUser(userId, expiry, role, sessionId)
    } catch (
        @Suppress("SwallowedException") e: Exception,
    ) {
        return null
    }
}

/**
 * Parses token even if the token is expired.
 * This method must not leak into other modules, can be only used in this module for
 * logging and exception handling purposes.
 */
internal fun Request.parseJwtRelaxed(): Claims? {
    val authorization = authorizationOrNull() ?: return null
    return try {
        val parsed = jwtParser().parseSignedClaims(authorization)
        return parsed.payload as Claims
    } catch (eje: ExpiredJwtException) {
        return eje.claims
    } catch (
        @Suppress("SwallowedException") e: Exception,
    ) {
        null
    }
}

private fun accessCookie(token: String) =
    Cookie(ACCESS_TOKEN, token)
        .httpOnly()
        .secure()
        .path("/")
        .sameSite(SameSite.Lax)

private fun impersonateCookie(token: String) =
    Cookie(IMPERSONATION_TOKEN, token)
        .httpOnly()
        .secure()
        .path("/")
        .sameSite(SameSite.Lax)

private fun refreshCookie(
    token: String,
    path: String,
) = Cookie(REFRESH_TOKEN, token)
    .httpOnly()
    .secure()
    .path(path)
    .expires(Instant.now().plusDays(ONE_YEAR_DAYS))
    .sameSite(SameSite.Lax)
