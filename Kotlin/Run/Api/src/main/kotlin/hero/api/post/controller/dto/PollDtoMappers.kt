package hero.api.post.controller.dto

import hero.api.post.service.PollWithVotes
import hero.model.PollOption

fun PollWithVotes.toResponse() =
    PollResponse(
        id = poll.id,
        deadline = poll.deadline,
        options = poll.options.values.sortedBy { it.index }.map { it.toResponse(votes) },
    )

fun PollOption.toResponse(votes: List<String>) =
    PollOptionResponse(
        id = id,
        title = title,
        voteCount = voteCount,
        hasVotedFor = id in votes,
    )
