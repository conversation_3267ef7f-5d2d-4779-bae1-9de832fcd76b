package hero.api.post.service

import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.TypedCollectionReference
import hero.model.Poll
import hero.model.PollOptionId
import hero.model.Subscriber
import hero.repository.post.JooqPollHelper
import hero.sql.jooq.Tables.POLL
import hero.sql.jooq.Tables.POLL_OPTION
import hero.sql.jooq.Tables.POLL_OPTION_VOTE
import org.jooq.DSLContext

class PollQueryService(
    lazyContext: Lazy<DSLContext>,
    private val subscribersCollection: TypedCollectionReference<Subscriber>,
) {
    private val context: DSLContext by lazyContext

    fun execute(query: GetPoll): PollWithVotes {
        val pollRecord = context
            .select(JooqPollHelper.pollFields)
            .from(POLL)
            .where(POLL.ID.eq(query.pollId))
            .fetchOne()

        if (pollRecord == null) {
            throw NotFoundException("Poll ${query.pollId} not found")
        }

        val creatorId = pollRecord[POLL.USER_ID]
        val userId = query.userId
        if (userId != creatorId && subscribersCollection.fetchActiveSubscription(userId, creatorId) == null) {
            throw ForbiddenException(
                "User $userId does not subscribe $creatorId",
            )
        }

        val votes = context
            .select(POLL_OPTION_VOTE.POLL_OPTION_ID)
            .from(POLL_OPTION)
            .join(POLL_OPTION_VOTE).on(POLL_OPTION_VOTE.POLL_OPTION_ID.eq(POLL_OPTION.ID))
            .where(POLL_OPTION.POLL_ID.eq(query.pollId))
            .and(POLL_OPTION_VOTE.USER_ID.eq(query.userId))
            .map { it[POLL_OPTION_VOTE.POLL_OPTION_ID] }

        return PollWithVotes(JooqPollHelper.mapRecordToEntity(pollRecord), votes)
    }
}

data class GetPoll(val userId: String, val pollId: String)

data class PollWithVotes(
    val poll: Poll,
    val votes: List<PollOptionId>,
)
