package hero.api.scripts.post

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.gjirafa.GjirafaUploadsService
import hero.model.GjirafaStatus.COMPLETE
import hero.model.GjirafaStatus.PARTIALLY_COMPLETED
import hero.model.Post
import hero.model.PostAsset
import hero.model.topics.PostState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)
    val postsCollection = firestore.typedCollectionOf(Post)
    val gjirafaService = GjirafaUploadsService(
        projectId = SystemEnv.gjirafaProject,
        apiKey = SystemEnv.gjirafaApiKey,
        imageKey = SystemEnv.gjirafaImageKey,
    )

    var lastId = ""

    while (true) {
        val posts = postsCollection
            .where(Post::id).isGreaterThan(lastId)
            .limit(1000)
            .fetchAll()

        if (posts.isEmpty()) {
            break
        }

        lastId = posts.last().id

        val postsWithGjirafaAsset = posts
            .filter { it.state != PostState.REVISION }
            .filter { it.assets.any { asset -> asset.gjirafa != null } }

        runBlocking(Dispatchers.Default) {
            postsWithGjirafaAsset
                .map { post ->
                    async {
                        refreshGjirafaAssetsInPost(post, gjirafaService, postsCollection)
                    }
                }
        }
    }
}

fun refreshGjirafaAssetsInPost(
    post: Post,
    gjirafaService: GjirafaUploadsService,
    postCollection: TypedCollectionReference<Post>,
) {
    val refreshedAssets: List<PostAsset> = post
        .assets
        .map {
            val gjirafa = it.gjirafa
            if (gjirafa == null) {
                it
            } else {
                try {
                    // we fetch the full Gjirafa info for debugging (withDebug = true)
                    val asset = gjirafaService.getAsset(userId = null, assetId = gjirafa.id, withDebug = true)
                    log.info("Updated Gjirafa Asset ${asset.id}/${asset.status} for Post ${post.id}: ${post.state}")
                    // but we don't want to store the debug info in the database
                    it.copy(gjirafa = asset.copy(debugDetail = null))
                } catch (e: Exception) {
                    log.info("Failed to refresh asset for ${gjirafa.id} in post ${post.id}, exception: $e")
                    it
                }
            }
        }

    postCollection[post.id].field(Post::assets).update(refreshedAssets)
    if (
        post.state == PostState.PROCESSING &&
        refreshedAssets
            .filter { it.gjirafa != null }
            .all { it.gjirafa!!.status in setOf(COMPLETE, PARTIALLY_COMPLETED) }
    ) {
        log.info("Post ${post.id} marked as PUBLISHED (was PROCESSING).")
        postCollection[post.id].field(Post::state).update(PostState.PUBLISHED)
    }
}
