package hero.functions

import com.stripe.model.Charge
import com.stripe.model.PaymentIntent
import com.stripe.param.PaymentIntentListParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.contains
import hero.gcloud.entry
import hero.gcloud.firestore
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.jackson.parseEnum
import hero.model.Creator
import hero.model.Currency
import hero.model.Notification
import hero.model.NotificationType
import hero.model.PaymentType
import hero.model.StorageEntityType
import hero.model.User
import hero.model.topics.EmailPublished
import hero.model.topics.StripeChargeReceived
import hero.repository.notification.NotificationRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.stripe.model.ChargeStatus
import hero.stripe.model.StripeErrorCode
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeService
import org.jooq.DSLContext
import java.time.Instant

@Suppress("unused")
class StripeChargeFailedHandler(
    production: Boolean = SystemEnv.isProduction,
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
) : PubSubSubscriber<StripeChargeReceived>() {
    private val notificationRepository = NotificationRepository(lazyContext)
    private val userRepository = firestore.typedCollectionOf(User)
    private val stripeClients: StripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val stripeService = StripeService(clients = stripeClients, pubSub = null)

    private val pubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject)

    override fun consume(payload: StripeChargeReceived) {
        val charge = when (payload) {
            is StripeChargeReceived.WithoutId -> return
            is StripeChargeReceived.WithId -> stripeService.getCharge(payload.chargeId, payload.currency)
        }

        if (parseEnum<ChargeStatus>(charge.status) != ChargeStatus.FAILED) {
            log.debug("Charge ${charge.id} is not failed, skipping.")
            return
        }

        fraudCheck(charge.customer, payload.currency)

        log.info("Processing failed charge ${payload.chargeId}")
        val failureCode = parseEnum<StripeErrorCode>(charge.failureCode)
        if (failureCode == null) {
            log.fatal("Charge ${charge.id} is missing failure code")
            return
        }
        val notificationType = when (failureCode) {
            StripeErrorCode.CARD_DECLINED -> {
                NotificationType.PAYMENT_CARD_DECLINED
            }

            StripeErrorCode.BALANCE_INSUFFICIENT -> {
                NotificationType.PAYMENT_INSUFFICIENT_FUNDS
            }

            else -> {
                NotificationType.PAYMENT_FAILED
            }
        }

        val paymentIntent = stripeService.paymentIntent(charge.paymentIntent, parseEnum<Currency>(charge.currency)!!)
        val result =
            if (paymentIntent.metadata["type"]?.replace("-", "_") == PaymentType.POST_UNLOCK.name.lowercase()) {
                processFailedPostUnlocking(paymentIntent, charge, notificationType)
            } else {
                processFailedCharge(paymentIntent, notificationType)
            }

        if (result.isFailure) {
            log.fatal("Failed processing failed charge with reason: ${result.exceptionOrNull()?.message}")
            result.onFailure { throw it }
        } else {
            log.info("Finished processing failed charge ${payload.chargeId}")
        }
    }

    private fun fraudCheck(
        customerId: String,
        currency: Currency,
    ) {
        val recentPaymentIntents = stripeClients[currency].paymentIntents().list(
            PaymentIntentListParams
                .builder()
                .setCustomer(customerId)
                .setCreated(
                    PaymentIntentListParams.Created.builder()
                        .setGt(Instant.now().minusDays(1).epochSecond)
                        .build(),
                )
                .build(),
        ).autoPagingIterable().toList()

        if (recentPaymentIntents.size > 8 || recentPaymentIntents.map { it.paymentMethod }.distinct().size > 3) {
            stripeService.fraudWarning(customerId, currency, "Too many recent or failed payments.")
        }
    }

    private fun processFailedCharge(
        paymentIntent: PaymentIntent,
        notificationType: NotificationType,
    ): Result<Unit> {
        val customerId = paymentIntent.customer
            ?: return Result.failure(
                IllegalArgumentException("Missing customer id for payment intent ${paymentIntent.id}"),
            )
        val user = userRepository
            .where(root(User::customerIds).entry(paymentIntent.currency.uppercase())).isEqualTo(customerId)
            .fetchSingle()
            ?: return Result.failure(
                IllegalArgumentException("User for $customerId was not found related to ${paymentIntent.id}"),
            )

        val creatorId = paymentIntent.metadata["creatorId"]
        val stripeAccountId = paymentIntent.transferData.destination

        val creator = if (creatorId != null) {
            userRepository[creatorId].get()
        } else {
            fetchMatchStripeAccountId(stripeAccountId) ?: fetchSearchLegacyStripeIds(stripeAccountId)
        }

        if (creator == null) {
            return Result.failure(
                IllegalArgumentException(
                    "Creator for ${paymentIntent.id} " +
                        "was not found neither with $creatorId nor $stripeAccountId",
                ),
            )
        }
        saveNotification(
            user,
            creator.id,
            Instant.ofEpochSecond(paymentIntent.created),
            notificationType,
            StorageEntityType.USER,
        )

        val email = user.email
        if (email != null) {
            pubSub.publish(
                EmailPublished(
                    to = email,
                    template = "charge-failed",
                    language = user.language,
                    variables = listOf(
                        "user-name" to user.name,
                        "user-email" to user.email,
                        "creator-name" to creator.name,
                        "reason" to notificationType,
                    ),
                ),
            )
        }

        return Result.success(Unit)
    }

    private fun fetchMatchStripeAccountId(stripeAccountId: String?): User? =
        userRepository
            .where(root(User::creator).path(Creator::stripeAccountId))
            .isEqualTo(stripeAccountId)
            .fetchSingle()

    private fun fetchSearchLegacyStripeIds(stripeAccountId: String?): User? {
        if (stripeAccountId == null) {
            return null
        }

        return userRepository
            .where(root(User::creator).path(Creator::stripeAccountLegacyIds))
            .contains(stripeAccountId)
            .fetchSingle()
    }

    private fun processFailedPostUnlocking(
        paymentIntent: PaymentIntent,
        charge: Charge,
        notificationType: NotificationType,
    ): Result<Unit> {
        val postId = paymentIntent.metadata["postId"]
        val userId = paymentIntent.metadata["userId"]
        if (postId == null || userId == null) {
            return Result.failure(IllegalArgumentException("Missing meta information to process charge ${charge.id}."))
        }
        val user = userRepository[userId].get()
        saveNotification(
            user,
            postId,
            Instant.ofEpochSecond(charge.created),
            notificationType,
            StorageEntityType.POST,
        )

        return Result.success(Unit)
    }

    private fun saveNotification(
        user: User,
        objectId: String,
        timestamp: Instant,
        type: NotificationType,
        objectType: StorageEntityType,
    ) {
        Notification(
            userId = user.id,
            type = type,
            actorIds = listOf(user.id),
            objectId = objectId,
            objectType = objectType,
            created = timestamp,
            timestamp = timestamp,
        ).also {
            notificationRepository.save(it)
        }
    }
}
