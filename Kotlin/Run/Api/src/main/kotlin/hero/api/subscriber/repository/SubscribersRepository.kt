package hero.api.subscriber.repository

import com.google.cloud.firestore.Query
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.where
import hero.http4k.config.PAGE_SIZE_DEFAULT
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.SubscriberType
import hero.model.topics.SubscriberChanged

class SubscribersRepository(
    val collection: TypedCollectionReference<Subscriber>,
    private val pubSub: PubSub,
) {
    fun subscribedTo(
        userId: String? = null,
        creatorId: String? = null,
        sorting: SubscribersSort = SubscribersSort.NEWEST,
        expired: Boolean = false,
        offset: Int = 0,
        limit: Int = PAGE_SIZE_DEFAULT + 1,
    ): Pair<List<Subscriber>, Boolean> =
        collection
            .where(Subscriber::status).isIn(
                if (expired) SubscriberStatus.inactiveStatuses else SubscriberStatus.activeStatuses,
            )
            .let { if (userId != null) it.and(Subscriber::userId).isEqualTo(userId) else it }
            .let { if (creatorId != null) it.and(Subscriber::creatorId).isEqualTo(creatorId) else it }
            .let {
                if (creatorId != null && userId != null)
                    it
                else
                    when (sorting) {
                        SubscribersSort.OLDEST -> it.orderBy(Subscriber::subscribed, Query.Direction.ASCENDING)
                        SubscribersSort.NEWEST -> it.orderBy(Subscriber::subscribed, Query.Direction.DESCENDING)
                        SubscribersSort.LOWEST_PRICE -> it.orderBy(Subscriber::tierId, Query.Direction.ASCENDING)
                        SubscribersSort.HIGHEST_PRICE -> it.orderBy(Subscriber::tierId, Query.Direction.DESCENDING)
                        SubscribersSort.GIFTED_FIRST ->
                            it
                                .orderBy(Subscriber::couponAppliedForMonths, Query.Direction.DESCENDING)
                                .orderBy(Subscriber::subscribed, Query.Direction.DESCENDING)
                    }
            }
            .offset(offset * limit)
            .limit(limit + 1)
            .fetchAll()
            .let { subscribers -> Pair(subscribers.take(limit), subscribers.size > limit) }

    fun getSubscriber(
        userId: String,
        creatorId: String,
    ): Subscriber? =
        collection
            .where(Subscriber::userId).isEqualTo(userId)
            .and(Subscriber::creatorId).isEqualTo(creatorId)
            .and(Subscriber::subscriberType).isEqualTo(SubscriberType.STRIPE)
            .fetchSingle()

    fun subscriber(
        userId: String,
        creatorId: String,
    ): Subscriber? =
        collection
            .where(Subscriber::userId).isEqualTo(userId)
            .and(Subscriber::creatorId).isEqualTo(creatorId)
            .and(Subscriber::status).isIn(SubscriberStatus.activeStatuses)
            .fetchSingle()

    fun findByAppleReferenceId(appleReferenceId: String) =
        collection.where(Subscriber::appleReferenceId).isEqualTo(appleReferenceId)
            .fetchSingle()

    fun store(subscriber: Subscriber) {
        collection[subscriber.id].set(subscriber)
        pubSub.publish(
            SubscriberChanged(
                userId = subscriber.userId,
                creatorId = subscriber.creatorId,
            ),
        )
    }
}

enum class SubscribersSort {
    NEWEST,
    OLDEST,
    LOWEST_PRICE,
    HIGHEST_PRICE,
    GIFTED_FIRST,
}
