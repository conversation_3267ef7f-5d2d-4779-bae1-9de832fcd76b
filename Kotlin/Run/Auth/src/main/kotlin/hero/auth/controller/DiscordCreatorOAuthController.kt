package hero.auth.controller

import hero.gcloud.PubSub
import hero.model.DiscordCredentials

class DiscordCreatorOAuthController(
    hostnameServices: String,
    hostname: String,
    servicePath: String,
    credentials: DiscordCredentials,
    pubSub: PubSub,
) : DiscordBaseOAuthController(
        hostnameServices,
        hostname,
        servicePath,
        credentials,
        "creator",
        listOf("email", "identify", "guilds.join", "bot"),
        pubSub,
    )
