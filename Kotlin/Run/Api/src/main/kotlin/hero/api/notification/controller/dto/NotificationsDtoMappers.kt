package hero.api.notification.controller.dto

import hero.api.notification.service.NotificationWithData
import hero.api.user.controller.dto.UserResponse
import hero.api.user.controller.dto.toResponse
import hero.model.Notification
import hero.model.NotificationsEnabled

fun Notification.toResponse(lastActor: UserResponse? = null): NotificationResponse {
    val relationships = NotificationRelationships(
        objectId = objectId,
        objectType = objectType,
        lastActorId = actorIds.last(),
    )
    return NotificationResponse(
        id = id,
        type = type,
        createdAt = created,
        checkedAt = checkedAt,
        seenAt = seenAt,
        actorCount = actorIds.distinct().size,
        lastActor = lastActor,
        relationships = relationships,
    )
}

fun NotificationWithData.toResponse(): NotificationResponse {
    val lastActorResponse = lastActor?.toResponse(listOf())

    return notification.toResponse(lastActorResponse)
}

fun NotificationsEnabled.toResponse() =
    NotificationSettingsResponse(
        emailNewPost = emailNewPost,
        emailNewDm = emailNewDm,
        pushNewPost = pushNewPost,
        pushNewComment = pushNewComment,
        newsletter = newsletter,
        termsChanged = termsChanged,
    )
