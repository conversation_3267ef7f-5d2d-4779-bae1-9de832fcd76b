package hero.functions

import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.model.RssFeed
import hero.model.SpotifyCredentials
import hero.model.SubscriberStatus
import hero.sql.jooq.Tables
import hero.sql.jooq.tables.records.RssFeedRecord
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper
import hero.test.TestEnvironmentVariables
import hero.test.TestRepositories
import hero.test.gcloud.FirestoreTestDatabase
import hero.test.logging.TestLogger
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Status
import org.jooq.XML
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class RssFeedProviderIT : IntegrationTest() {
    /**
     * Provides access to the basic RSS feed used in standard podcast apps.
     *
     * Requirements:
     * - The user must have an active subscription.
     * - The generated RSS feed access must be stored in the `rss-feed` Firestore document.
     * - The `iat` (issued at) field in the token must match the one stored in the Firestore document.
     * - Creator must have rss enabled
     */
    @Nested
    inner class BasicRssFeed {
        @Test
        fun `should provide basic rss feed`() {
            val rssFeedCollection = TypedCollectionReference<RssFeed>(FirestoreTestDatabase.testCollection())
            testHelper.createUser("cestmir", name = "cestmir strakaty", hasRssFeed = true)
            testHelper.createSubscriber("cestmir", "johny", status = SubscriberStatus.ACTIVE)

            val underTest = RssFeedProvider(
                firestore = firestore,
                environment = "test",
                envPrefix = "test",
                usersCollection = IntegrationTestHelper.TestCollections.usersCollection,
                rssFeedsCollections = rssFeedCollection,
                subscribersCollection = IntegrationTestHelper.TestCollections.subscribersCollection,
                rssFeedGenerator = RssFeedGenerator(lazyTestContext, TestRepositories.postRepository, TestLogger),
                credentials = SpotifyCredentials("", "", "herohero", ""),
                systemEnvs = TestEnvironmentVariables,
            )

            val basicRssFeed = RssFeedRecord().apply {
                userId = "cestmir"
                type = RssFeedType.BASIC.name
                placeholderToken = "placeholder"
                xml = XML.valueOf("<rss>basic</rss>")
                createdAt = Instant.now()
                updatedAt = Instant.now()
            }

            val spotifyRssFeed = RssFeedRecord().apply {
                userId = "cestmir"
                type = RssFeedType.SPOTIFY.name
                placeholderToken = "placeholder"
                xml = XML.valueOf("<rss>spotify</rss>")
                createdAt = Instant.now()
                updatedAt = Instant.now()
            }

            testContext
                .insertInto(Tables.RSS_FEED)
                .set(basicRssFeed, spotifyRssFeed)
                .execute()
            rssFeedCollection["cestmir-johny"].set(RssFeed(iat = **********, listOf("test-user-agent")))

            val response = underTest(mapOf())(
                Request(Method.GET, "/")
                    .query("token", BASIC_TOKEN)
                    .header("User-Agent", "test-user-agent"),
            )

            assertThat(response.status).isEqualTo(Status.OK)
            val responseXml = response.bodyString()
            val expectedXml = "<rss>basic</rss>"

            assertThat(responseXml).isEqualTo(expectedXml)
        }
    }

    @Test
    fun `should throw if subscription is not active`() {
        val rssFeedCollection = TypedCollectionReference<RssFeed>(FirestoreTestDatabase.testCollection())
        testHelper.createUser("cestmir", name = "cestmir strakaty", hasRssFeed = true)
        testHelper.createSubscriber("cestmir", "johny", status = SubscriberStatus.CANCELLED)

        val underTest = RssFeedProvider(
            firestore = firestore,
            environment = "test",
            envPrefix = "test",
            usersCollection = IntegrationTestHelper.TestCollections.usersCollection,
            rssFeedsCollections = rssFeedCollection,
            subscribersCollection = IntegrationTestHelper.TestCollections.subscribersCollection,
            rssFeedGenerator = RssFeedGenerator(lazyTestContext, TestRepositories.postRepository, TestLogger),
            credentials = SpotifyCredentials("", "", "herohero", ""),
            systemEnvs = TestEnvironmentVariables,
        )

        assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
            underTest(mapOf())(
                Request(Method.GET, "/")
                    .query("token", BASIC_TOKEN)
                    .header("User-Agent", "test-user-agent"),
            )
        }.withMessage("User johny does not subscribe cestmir")
    }

    @Test
    fun `should throw if creator does not have rss enabled`() {
        val rssFeedCollection = TypedCollectionReference<RssFeed>(FirestoreTestDatabase.testCollection())
        testHelper.createUser("cestmir", name = "cestmir strakaty", hasRssFeed = false)
        testHelper.createSubscriber("cestmir", "johny", status = SubscriberStatus.ACTIVE)

        val underTest = RssFeedProvider(
            firestore = firestore,
            environment = "test",
            envPrefix = "test",
            usersCollection = IntegrationTestHelper.TestCollections.usersCollection,
            rssFeedsCollections = rssFeedCollection,
            subscribersCollection = IntegrationTestHelper.TestCollections.subscribersCollection,
            rssFeedGenerator = RssFeedGenerator(lazyTestContext, TestRepositories.postRepository, TestLogger),
            credentials = SpotifyCredentials("", "", "herohero", ""),
            systemEnvs = TestEnvironmentVariables,
        )

        assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
            underTest(mapOf())(
                Request(Method.GET, "/")
                    .query("token", BASIC_TOKEN)
                    .header("User-Agent", "test-user-agent"),
            )
        }.withMessage("Creator cestmir does not have RSS allowed.")
    }

    @Test
    fun `should throw if user does not have the rss feed access stored in firestore collection`() {
        val rssFeedCollection = TypedCollectionReference<RssFeed>(FirestoreTestDatabase.testCollection())
        testHelper.createUser("cestmir", name = "cestmir strakaty", hasRssFeed = true)
        testHelper.createSubscriber("cestmir", "johny", status = SubscriberStatus.ACTIVE)

        val underTest = RssFeedProvider(
            firestore = firestore,
            environment = "test",
            envPrefix = "test",
            usersCollection = IntegrationTestHelper.TestCollections.usersCollection,
            rssFeedsCollections = rssFeedCollection,
            subscribersCollection = IntegrationTestHelper.TestCollections.subscribersCollection,
            rssFeedGenerator = RssFeedGenerator(lazyTestContext, TestRepositories.postRepository, TestLogger),
            credentials = SpotifyCredentials("", "", "herohero", ""),
            systemEnvs = TestEnvironmentVariables,
        )

        assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
            underTest(mapOf())(
                Request(Method.GET, "/")
                    .query("token", BASIC_TOKEN)
                    .header("User-Agent", "test-user-agent"),
            )
        }.withMessage("User johny does not have does not have generated rss feed access.")
    }

    @Test
    fun `should throw if token iat and document iat are different`() {
        val rssFeedCollection = TypedCollectionReference<RssFeed>(FirestoreTestDatabase.testCollection())
        testHelper.createUser("cestmir", name = "cestmir strakaty", hasRssFeed = true)
        testHelper.createSubscriber("cestmir", "johny", status = SubscriberStatus.ACTIVE)

        val underTest = RssFeedProvider(
            firestore = firestore,
            environment = "test",
            envPrefix = "test",
            usersCollection = IntegrationTestHelper.TestCollections.usersCollection,
            rssFeedsCollections = rssFeedCollection,
            subscribersCollection = IntegrationTestHelper.TestCollections.subscribersCollection,
            rssFeedGenerator = RssFeedGenerator(lazyTestContext, TestRepositories.postRepository, TestLogger),
            credentials = SpotifyCredentials("", "", "herohero", ""),
            systemEnvs = TestEnvironmentVariables,
        )

        // iat in the token is **********
        rssFeedCollection["cestmir-johny"].set(RssFeed(iat = 10, listOf("test-user-agent")))
        assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
            underTest(mapOf())(
                Request(Method.GET, "/")
                    .query("token", BASIC_TOKEN)
                    .header("User-Agent", "test-user-agent"),
            )
        }.withMessage("User johny has generated a new rss feed token")
    }

    /**
     * Provides access to the Spotify RSS feed, which does not require a subscription since it is requested by Spotify.
     *
     * Requirements:
     * - The podcast creator must have `hasSpotifyExport` set to `true`.
     * - The `User-Agent` header must contain the string `"Spotify\"`.
     */
    @Nested
    inner class SpotifyRssFeed {
        @Test
        fun `should provide spotify rss feed, subscription is not needed, user agent must be spotify`() {
            testHelper.createUser("cestmir", name = "cestmir strakaty", hasRssFeed = true, hasSpotifyExport = true)

            val underTest = RssFeedProvider(
                firestore = firestore,
                environment = "test",
                envPrefix = "test",
                usersCollection = IntegrationTestHelper.TestCollections.usersCollection,
                rssFeedsCollections = TypedCollectionReference<RssFeed>(FirestoreTestDatabase.testCollection()),
                subscribersCollection = IntegrationTestHelper.TestCollections.subscribersCollection,
                rssFeedGenerator = RssFeedGenerator(lazyTestContext, TestRepositories.postRepository, TestLogger),
                credentials = SpotifyCredentials("", "", "herohero", ""),
                systemEnvs = TestEnvironmentVariables,
            )

            val basicRssFeed = RssFeedRecord().apply {
                userId = "cestmir"
                type = RssFeedType.BASIC.name
                placeholderToken = "placeholder"
                xml = XML.valueOf("<rss>basic</rss>")
                createdAt = Instant.now()
                updatedAt = Instant.now()
            }

            val spotifyRssFeed = RssFeedRecord().apply {
                userId = "cestmir"
                type = RssFeedType.SPOTIFY.name
                placeholderToken = "placeholder"
                xml = XML.valueOf("<rss>spotify</rss>")
                createdAt = Instant.now()
                updatedAt = Instant.now()
            }

            testContext
                .insertInto(Tables.RSS_FEED)
                .set(basicRssFeed, spotifyRssFeed)
                .execute()

            val response = underTest(mapOf())(
                Request(Method.GET, "/")
                    .query("token", SPOTIFY_TOKEN)
                    .header("User-Agent", "Spotify/User-Agent"),
            )

            assertThat(response.status).isEqualTo(Status.OK)
            val responseXml = response.bodyString()
            val expectedXml = "<rss>spotify</rss>"

            assertThat(responseXml).isEqualTo(expectedXml)
        }
    }

    @Test
    fun `should map placeholderToken to token passed from query`() {
        testHelper.createUser("cestmir", name = "cestmir strakaty", hasRssFeed = true, hasSpotifyExport = true)

        val underTest = RssFeedProvider(
            firestore = firestore,
            environment = "test",
            envPrefix = "test",
            usersCollection = IntegrationTestHelper.TestCollections.usersCollection,
            rssFeedsCollections = TypedCollectionReference<RssFeed>(FirestoreTestDatabase.testCollection()),
            subscribersCollection = IntegrationTestHelper.TestCollections.subscribersCollection,
            rssFeedGenerator = RssFeedGenerator(lazyTestContext, TestRepositories.postRepository, TestLogger),
            credentials = SpotifyCredentials("", "", "herohero", ""),
            systemEnvs = TestEnvironmentVariables,
        )

        val basicRssFeed = RssFeedRecord().apply {
            userId = "cestmir"
            type = RssFeedType.BASIC.name
            placeholderToken = "placeholder"
            xml = XML.valueOf("<rss>basic</rss>")
            createdAt = Instant.now()
            updatedAt = Instant.now()
        }

        val spotifyRssFeed = RssFeedRecord().apply {
            userId = "cestmir"
            type = RssFeedType.SPOTIFY.name
            placeholderToken = "placeholder"
            xml = XML.valueOf(
                """
<rss xmlns:atom="http://www.w3.org/2005/Atom" xmlns:itunes="http://www.itunes.com/dtds/podcast-1.0.dtd" xmlns:spotify="http://www.spotify.com/ns/rss" version="2.0">
    <channel>
        <title>cestmir strakaty</title>
        <description>bio</description>
        <language>cs-cz</language>
        <link>https://herohero.co/cestmir</link>
        <atom:link href="https://test.herohero.co/rss-feed/?token=placeholder" rel="self" type="application/rss+xml"/>
    </channel>
 </rss>
                """.trimIndent(),
            )
            createdAt = Instant.now()
            updatedAt = Instant.now()
        }

        testContext
            .insertInto(Tables.RSS_FEED)
            .set(basicRssFeed, spotifyRssFeed)
            .execute()

        val response = underTest(mapOf())(
            Request(Method.GET, "/")
                .query("token", SPOTIFY_TOKEN)
                .header("User-Agent", "Spotify/User-Agent"),
        )

        assertThat(response.status).isEqualTo(Status.OK)
        val responseXml = response.bodyString()
        val expectedXml =
            """
<rss xmlns:atom="http://www.w3.org/2005/Atom" xmlns:itunes="http://www.itunes.com/dtds/podcast-1.0.dtd" xmlns:spotify="http://www.spotify.com/ns/rss" version="2.0">
    <channel>
        <title>cestmir strakaty</title>
        <description>bio</description>
        <language>cs-cz</language>
        <link>https://herohero.co/cestmir</link>
        <atom:link href="https://test.herohero.co/rss-feed/?token=$SPOTIFY_TOKEN" rel="self" type="application/rss+xml"/>
    </channel>
 </rss>
            """.trimIndent()

        assertThat(responseXml).isEqualTo(expectedXml)
    }
}

const val BASIC_TOKEN = "eyJhbGciOiJSUzI1NiJ9.eyJ1Ijoiam9obnkiLCJjIjoiY2VzdG1pciIsInQiOjE3NDE4NjU1OTksInMiOmZhbHNlfQ" +
    ".jb7K0N7RR78dFoLyuJk7qGANIhWU73ygr6ZtM_nWFKKUXnTlLWbDiW0eqZObTC8m6zKRFo4Alopu56_z6zv6hoEy78xhYUfo9SJiSW62Q35-7h" +
    "029jXz9TfZbIbDMuvP99A-NczTHbX96oEBFdXS3ntzaZWwahCNAR-zRjyHi1_QjwqWaGazm4RrIpnbuGEZyL-4RbClNYd8B59cPVMco8p8kJYnb" +
    "tFmS-FcsF_XNfSKijKxmNkOb243V6vzxmRCEea0XyB5EX9745eoQ9uR1V2elmzA7LiYSnMXtkW9nTs3tVGYl_bcYu0p2sRmx7-FU7UNbcTkpwtm" +
    "ySMTuMK2Rg"

const val SPOTIFY_TOKEN = "eyJhbGciOiJSUzI1NiJ9.eyJ1IjoiY2VzdG1pciIsImMiOiJjZXN0bWlyIiwidCI6MTcxMzkxNjgwMCwicyI6dHJ1" +
    "ZX0.XD4lXM00yY0k94qbhQVmguJ2BxbeH2aDv0zqYZRG2ZCoJnEh-XlQvq3QYq78KTNmbGW9WBbh_4pRtyYH9C7EuptnClfqY2i5P8TFsMtpop6" +
    "iRbLJUMr_fPBjfEL_joDDa97Dt1YWnxEIH6y6MSujAAnmFLJ-xXSrvnx1gmcfbqHm9-MckbNDZzFz3zvZaSjZWdAMvnW1P_rG7__9eMOKvy2NLd" +
    "BBPvmwxc8xMKXjJ7xc115XsjXmfqWo1_TuME0GJZRCP7AERfnBDD1HJM_c5ejIYuS4vj6m-Yd1R9sDHSldcds5BpSdsvuMJuzDP4pyzgR9HxGz8" +
    "-OeRt6_4Q2e9Q"
