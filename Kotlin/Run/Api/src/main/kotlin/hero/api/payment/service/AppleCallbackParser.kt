package hero.api.payment.service

import com.apple.itunes.storekit.model.ResponseBodyV2DecodedPayload
import hero.api.payment.controller.dto.AppleCallbackPayload
import hero.baseutils.log
import hero.exceptions.http.ServerException
import hero.jackson.parseEnum
import hero.jackson.to
import hero.model.Currency
import hero.stripe.model.AdvancedCommerceInfo
import hero.stripe.service.AppleSigningService
import io.jsonwebtoken.Claims
import java.time.Instant

class AppleCallbackParser(
    private val signingService: AppleSigningService,
) {
    fun parse(payload: String): AppleCallbackPayload =
        parseRaw(payload)
            .let { format(it.first, it.second) }

    internal fun parseRaw(jwt: String): Triple<ResponseBodyV2DecodedPayload, Claims, Claims?> {
        val parsed = signingService.decodeNotification(jwt)
        val transactionInfo = signingService.parseX5cPayload(parsed.data.signedTransactionInfo)
        val renewalInfo = parsed.data.signedRenewalInfo?.let { signingService.parseX5cPayload(it) }
        return Triple(parsed, transactionInfo.payload, renewalInfo?.payload)
    }

    internal fun format(
        payload: ResponseBodyV2DecodedPayload,
        claims: Map<String, Any>,
    ): AppleCallbackPayload {
        try {
            val advancedCommerceInfo = claims["advancedCommerceInfo"]?.to<AdvancedCommerceInfo>()
            val items = advancedCommerceInfo?.items
            if (items?.size != 1) {
                throw ServerException("Apple subscription payload did not contain exactly 1 item: $items")
            }
            val (userId, creatorId, tierId) = items.first().SKU.split("_")
            return AppleCallbackPayload(
                transactionId = claims["transactionId"].toString(),
                requestReferenceId = advancedCommerceInfo.requestReferenceId,
                notificationType = payload.notificationType,
                subType = payload.subtype,
                // Apple productId cannot contain `-` so we replace these with `.` and here we
                // need to replace them back. This only concerns some very old userIds.
                userId = userId.replace(".", "-"),
                creatorId = creatorId.replace(".", "-"),
                tierId = tierId,
                purchaseDate = Instant.ofEpochMilli(claims["purchaseDate"] as Long),
                expiresDate = Instant.ofEpochMilli(claims["expiresDate"] as Long),
                price = claims["price"] as Int,
                storefront = claims["storefront"]?.toString(),
                type = claims["type"]?.toString(),
                currency = parseEnum<Currency>(claims["currency"] as String) ?: error("Unknown currency in $claims."),
            )
        } catch (e: Exception) {
            log.fatal("Failed claims: $claims")
            // remove unnecessary long payload
            payload.data.signedTransactionInfo = null
            payload.data.signedRenewalInfo = null
            log.error("Failed payload: $payload")
            throw ServerException("Error when parsing Apple Payload: ${e.message} (see logs for details)", cause = e)
        }
    }
}
