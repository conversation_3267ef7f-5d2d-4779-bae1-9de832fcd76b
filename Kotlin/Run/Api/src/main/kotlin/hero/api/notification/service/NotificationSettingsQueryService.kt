package hero.api.notification.service

import hero.model.NotificationsEnabled
import hero.repository.notification.NotificationSettingsRepository

class NotificationSettingsQueryService(private val notificationSettingsRepository: NotificationSettingsRepository) {
    fun execute(query: GetNotificationSettings): NotificationsEnabled =
        notificationSettingsRepository.getByUserId(query.userId)
}

data class GetNotificationSettings(val userId: String)
