package hero.api.post.service

import hero.api.post.repository.get
import hero.api.post.repository.getPaymentsForPost
import hero.api.user.service.UserRelationsService
import hero.exceptions.http.ConflictException
import hero.exceptions.http.NotFoundException
import hero.gcloud.TypedCollectionReference
import hero.model.Post
import hero.model.PostDto
import hero.model.PostPayment
import hero.model.PostRenderMeta
import hero.model.topics.PostState

class PostQueryService(
    private val postsCollection: TypedCollectionReference<Post>,
    private val userRelationsService: UserRelationsService,
    private val postPaymentRepository: TypedCollectionReference<PostPayment>,
) {
    fun handle(query: GetPostByIdQuery): PostDto {
        val post = getPost(query.postId)
        when (post.state) {
            PostState.REVISION, PostState.DELETED -> throw NotFoundException("Post ${query.postId} was not found.")
            PostState.PROCESSING -> {
                if (post.userId != query.userId) {
                    throw ConflictException("Post ${query.postId} is not ready and you are not an owner.")
                }
            }

            PostState.PUBLISHED, PostState.SCHEDULED -> {}
        }

        val isMessage = post.messageThreadId != null
        val canInteract by lazy {
            if (query.userId == null) {
                false
            } else {
                userRelationsService.canInteract(
                    query.userId,
                    isMessage,
                    listOf(post.parentUserId ?: post.userId),
                )
            }
        }
        val canViewPost by lazy { !isMessage || userCanViewMessage(post, query.userId) }
        val fullResponse = post.isUsersPost(query.userId) || (canInteract && canViewPost)
        val paymentsFromUserIds = if (post.isUsersPost(query.userId)) {
            postPaymentRepository.getPaymentsForPost(query.postId).map { it.userId }
        } else {
            emptyList()
        }

        return post.toDto(
            renderMeta = PostRenderMeta(
                fullResponse = fullResponse,
                showText = fullResponse || isMessage,
                paymentsFromUserIds = paymentsFromUserIds,
            ),
        )
    }

    private fun getPost(postId: String): Post = postsCollection[postId].get()

    private fun userCanViewMessage(
        post: Post,
        userId: String?,
    ): Boolean {
        if (userId == null) {
            return false
        }
        if (post.isUsersPost(userId) || post.isFreeMessage()) {
            return true
        }

        return postPaymentRepository.get(post.id, userId).fetch() != null
    }
}

private fun Post.isUsersPost(userId: String?): Boolean = this.userId == userId

data class GetPostByIdQuery(val postId: String, val userId: String?)
