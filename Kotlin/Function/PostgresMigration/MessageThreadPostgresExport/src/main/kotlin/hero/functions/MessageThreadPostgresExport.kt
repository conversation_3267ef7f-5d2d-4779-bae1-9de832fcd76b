package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.logging.Logger
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.MessageThread
import hero.repository.message.MessageThreadRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import org.jooq.DSLContext

@Suppress("Unused")
class MessageThreadPostgresExport(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    systemEnv: EnvironmentVariables = SystemEnv,
    private val firestore: FirestoreRef = firestore(systemEnv.cloudProject, systemEnv.isProduction),
    private val messageThreadsCollection: TypedCollectionReference<MessageThread> = firestore.typedCollectionOf(
        MessageThread,
    ),
    private val logger: Logger = log,
) : FirestoreEventSubcriber(environmentVariables = systemEnv, retryable = true) {
    private val messageThreadRepository = MessageThreadRepository(lazyContext)

    override fun consume(event: FirestoreEvent) {
        val messageThread = messageThreadsCollection[event.documentId].get()
        logger.info("Exporting message thread ${messageThread.id} to Postgres")
        messageThreadRepository.save(messageThread)
        logger.info("Done exporting message thread ${messageThread.id} to Postgres")
    }
}
