package hero.functions

import com.google.cloud.functions.HttpRequest
import com.google.cloud.functions.HttpResponse
import hero.jackson.map
import hero.jwt.toJwt
import hero.model.NotificationDisableRequest
import hero.model.NotificationType
import hero.model.NotificationsEnabled
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.io.BufferedWriter
import java.io.StringWriter
import java.util.Optional

internal class NotificationDisablerTest {
    private val unsubscriber = spyk(
        NotificationDisabler(
            mockk(),
            "devel.herohero.co",
            mockk(),
        ),
    )

    private val mockRequest: HttpRequest = mockk()
    private val mockResponse: HttpResponse = mockk()
    private val mockWriter = BufferedWriter(StringWriter())

    @BeforeEach
    fun beforeEach() {
        val mockWriter = BufferedWriter(StringWriter())
        every { mockResponse.writer } returns mockWriter
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
        unmockkAll()
        mockWriter.flush()
    }

    @Test
    fun unsubscribeNewPost() {
        val testRequest = NotificationDisableRequest("eliseleclerc", NotificationType.NEW_POST)

        every { mockRequest.queryParameters } returns mapOf("request" to listOf(testRequest.map().toJwt()))
        every {
            unsubscriber.disableNotificationForNewPost(testRequest.userId, any(), NotificationsEnabled::emailNewPost)
        } just runs
        every { mockRequest.getFirstQueryParameter("enabled") } returns Optional.of("true")

        unsubscriber.service(mockRequest, mockResponse)

        verify(exactly = 1) {
            unsubscriber.disableNotificationForNewPost(testRequest.userId, any(), NotificationsEnabled::emailNewPost)
        }
    }

    @Test
    fun notUnsubscribeOnIgnoredNotification() {
        val testRequest = NotificationDisableRequest("eliseleclerc", NotificationType.NEW_COMMENT)

        every { mockRequest.queryParameters } returns mapOf("request" to listOf(testRequest.map().toJwt()))
        every { mockRequest.getFirstQueryParameter("enabled") } returns Optional.of("true")

        unsubscriber.service(mockRequest, mockResponse)

        verify(exactly = 0) {
            unsubscriber.disableNotificationForNewPost(testRequest.userId, any(), NotificationsEnabled::emailNewPost)
        }
    }
}
