package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.plusDays
import hero.core.logging.Logger
import hero.model.topics.WriteSubscriberStatisticsRequested
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.queries.FetchDailySubscriberStats
import hero.sql.jooq.tables.DailySubscriberStatistics.DAILY_SUBSCRIBER_STATISTICS
import io.sentry.Sentry
import org.jooq.DSLContext
import org.jooq.impl.DSL
import java.time.Instant
import java.time.LocalDate
import java.time.Month
import java.time.ZoneOffset

@Suppress("unused")
class SubscriberStatisticsWriter(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val logger: Logger = log,
    systemEnv: EnvironmentVariables = SystemEnv,
) : PubSubSubscriber<WriteSubscriberStatisticsRequested>(systemEnv) {
    // this must be lazy otherwise function deployment fails when the function is first instantiated
    private val context: DSLContext by lazyContext

    override fun consume(payload: WriteSubscriberStatisticsRequested) {
        payload.creatorIds.forEach {
            val result = runCatching {
                processCreator(it, payload.until.atStartOfDay().toInstant(ZoneOffset.UTC))
            }

            result.exceptionOrNull()?.run {
                logger.fatal("Failed to process stats for $it", mapOf("creatorId" to it), this)
                Sentry.captureException(this)
            }
        }
    }

    private fun processCreator(
        creatorId: String,
        until: Instant,
    ) {
        val labels = mapOf("creatorId" to creatorId)
        logger.info("Starting processing stats of $creatorId", labels)
        val maxField = DSL.max(DAILY_SUBSCRIBER_STATISTICS.DATE)
        val maxDate = context
            .select(maxField)
            .from(DAILY_SUBSCRIBER_STATISTICS)
            .where(DAILY_SUBSCRIBER_STATISTICS.CREATOR_ID.eq(creatorId))
            .fetchSingle()
            .let {
                it[maxField] ?: firstSubscriptionDate
            }
            .atStartOfDay().toInstant(ZoneOffset.UTC)

        if (maxDate >= until) {
            logger.info(
                "Subscriber stats for $creatorId already done until $until, skipping",
                labels,
            )
            return
        }

        context
            .insertInto(
                DAILY_SUBSCRIBER_STATISTICS,
                DAILY_SUBSCRIBER_STATISTICS.DATE,
                DAILY_SUBSCRIBER_STATISTICS.CREATOR_ID,
                DAILY_SUBSCRIBER_STATISTICS.TOTAL_SUBSCRIBERS,
                DAILY_SUBSCRIBER_STATISTICS.ACTIVE_SUBSCRIBERS,
                DAILY_SUBSCRIBER_STATISTICS.SUBSCRIBED,
                DAILY_SUBSCRIBER_STATISTICS.UNSUBSCRIBED,
                DAILY_SUBSCRIBER_STATISTICS.TOTAL_INCOME_CENTS,
            )
            .select(
                FetchDailySubscriberStats.query(context, creatorId, maxDate.plusDays(1), until),
            )
            .execute()
    }
}

private val firstSubscriptionDate = LocalDate.of(2020, Month.DECEMBER, 9)
