import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { PaginationModel } from '../models/pagination'
import { MessageModel, MessageThreadDetailsModel, MessageThreadModel } from '../models/message-thread'
import {
    MessageThreadDetailsResponse,
    PagedMessageThreadsResponse,
    PagedPostResponse,
    PostResponse,
} from '../generated/api'
import { GraphQLError } from 'graphql/error'
import { mapToMessage, mapToUser } from './common-mappers'
import { PaginationParams, paginationParamsToQueryParams } from './common-utils'

export class MessageThreadAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async getMessageThreads(
        paginationParams: PaginationParams
    ): Promise<{ messageThreads: MessageThreadModel[]; pagination: PaginationModel }> {
        const params = paginationParamsToQueryParams(paginationParams)
        const response = await this.get<PagedMessageThreadsResponse>(`/v2/message-threads`, { params })
        const messageThreads = response.content.map((messageThread) => {
            const lastMessage = messageThread.lastMessage
            return {
                id: messageThread.id,
                participants: messageThread.participants.map((user) => mapToUser(user)),
                participantIds: messageThread.participantIds,
                createdAt: messageThread.createdAt,
                seenAt: messageThread.seenAt,
                checkedAt: messageThread.checkedAt,
                lastMessageAt: messageThread.lastMessageAt,
                lastMessage: mapToMessage(lastMessage),
            }
        })

        return {
            messageThreads,
            pagination: {
                hasNextPage: response.hasNext,
                endCursor: response.afterCursor,
            },
        }
    }

    async getMessageThreadDetails(messageThreadId: string): Promise<MessageThreadDetailsModel> {
        if (messageThreadId.length == 0) {
            throw new GraphQLError('Message thread id must not be empty', { extensions: { code: 'BAD_REQUEST' } })
        }

        const response = await this.get<MessageThreadDetailsResponse>(`/v2/message-threads/${messageThreadId}`)
        return {
            id: response.id,
            participants: response.participants.map((user) => mapToUser(user)),
            participantIds: response.participantIds,
            createdAt: response.createdAt,
            canPost: response.canPost,
            relation: response.relation,
            commonCreators: response.commonCreators,
            seenAt: response.seenAt,
            checkedAt: response.checkedAt,
            lastMessageAt: response.lastMessageAt,
            deletedAt: response.deletedAt,
            deleted: response.deleted,
            archived: response.archived,
        }
    }

    async getMessages(
        messageThreadId: string,
        paginationParams: PaginationParams
    ): Promise<{ messages: MessageModel[]; pagination: PaginationModel }> {
        const params = paginationParamsToQueryParams(paginationParams)
        const response = await this.get<PagedPostResponse>(`/v2/message-threads/${messageThreadId}/messages`, {
            params,
        })

        const messages = response.content.map(mapToMessage)

        return {
            messages,
            pagination: {
                hasNextPage: response.hasNext,
                endCursor: response.afterCursor,
            },
        }
    }

    async postMessages({ messageThreadId, text }: { messageThreadId: string; text: string }): Promise<MessageModel> {
        const body = { text }
        const response = await this.post<PostResponse>(`/v2/message-threads/${messageThreadId}/messages`, {
            body,
        })

        return mapToMessage(response)
    }

    async getMessage(messageId: string): Promise<MessageModel> {
        const response = await this.get<PostResponse>(`/v1/messages/${messageId}`)

        return mapToMessage(response)
    }
}
