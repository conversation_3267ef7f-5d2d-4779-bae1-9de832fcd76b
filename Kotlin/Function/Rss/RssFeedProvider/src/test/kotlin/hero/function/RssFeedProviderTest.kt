package hero.function

import hero.functions.parseToken
import org.junit.jupiter.api.Test
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class RssFeedProviderTest {
    @Test
    fun `current token is accepted()`() {
        val token =
            "eyJhbGciOiJSUzI1NiJ9.eyJ1Ijoib2xpbmF0YWJvcnNrYWp6b3NqZWJqIiwiYyI6Impvc2VmaG9seWV1cWxmZGhxIiwidCI6MTY4MT" +
                "M3NDc1MH0.G9XMLzcSaOFcpzA4iCe0ivrD9XJf1-BzKkCki2axnnnNySZzdB6K_tmERdJ4te3lb0adUBMaLAC35HFHkY6aJyTU4" +
                "rxn4NETcBapcEBpehWpvlNNSZaM7v4b0ZlZnBtMg6NqVlmUMXzQSFoI6rPsUIY0BehM4it5jZq8mYZDp8d7ZHyLzS7LUfXbGHeC" +
                "-BajZWtwz-0VkTrGt3Gj-tYpkJ1QqX_ITrdvzVf7zJ7vJPPjJ4LTLzszJy8YErvzatBJzb6t_7gN1XP-89-Dw58gLiGoOMEyoIA" +
                "AruzphKQPXgnoBDWQIH6GpvYDFUhSZHwAZLqWhjmUK1mApWZDEECNXQ"
        assertNotNull(parseToken(token))
    }

    @Test
    fun `invalid token is refused()`() {
        val token =
            "eyJhbGciOiJSUzI1NiJ9.eyJ1Ijoib2xpbmF0YWJvcnNrYWp6b3NqZWJqIiwiYyI6Impvc2VmaG9seWV1cWxmZGhxIiwidCI6MTY4MT" +
                "M3NDc1MH0.kzBTez-DRqDGCIIbs-bRfD1R9q3VOG51ID1qxnrnpzmBb2ynXcHcBit3EWgxBypnY840mLKSSN_oaYaOmzjbXWUcG" +
                "bsptKz1oUfCzFLCyq8g7mIx7y4YHDtvKawl9j2Ndzyp5mVgUgXH9gL7bhIpLlA9tTqsqLwUanuIsC1vL4BaK_od6Ff-dPEDVvh-" +
                "_sSOnf4HvvY3QaS-VM3_keV89dOJlgbFO2mk7a2OdoP6w2UZ1ocRsE5L7oAl27d0r3LnFOFhNmnD8v_r9uu8u7cY4dpJ8wfZ-ya" +
                "zpSYSnRbM9wOjgY1CzJIUhdUuTW6jB3pDP"
        assertNull(parseToken(token))
    }
}
