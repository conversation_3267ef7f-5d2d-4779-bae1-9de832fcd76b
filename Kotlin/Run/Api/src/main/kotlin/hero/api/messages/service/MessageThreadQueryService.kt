package hero.api.messages.service

import com.google.cloud.firestore.Query
import hero.api.user.service.UserRelationsService
import hero.api.user.service.canInteractWithRelationAndCreators
import hero.baseutils.fromBase64
import hero.baseutils.toBase64
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.exceptions.http.ForbiddenException
import hero.gcloud.MAX_ARRAY_SIZE_IN_ARRAY_OPERATOR
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.util.paginate
import hero.gcloud.where
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.MessageThread
import hero.model.Post
import hero.model.SubscriptionRelationType
import hero.model.User
import hero.model.topics.PostState
import java.time.Instant

class MessageThreadQueryService(
    private val messageThreadsCollection: TypedCollectionReference<MessageThread>,
    private val postsCollection: TypedCollectionReference<Post>,
    private val usersCollection: TypedCollectionReference<User>,
    private val userRelationsService: UserRelationsService,
) {
    fun execute(query: GetUsersActiveMessageThreads): Page<MessageThreadWithLastMessage> {
        val afterCursor = query.pageable.afterCursor
        val cursor = afterCursor?.fromBase64()?.fromJson<MessageThreadQueryServiceCursor>()?.lastMessageAt

        val (messageThreads, hasNext) = messageThreadsCollection
            .where(MessageThread::activeFor).contains(query.userId)
            .and(MessageThread::lastMessageAt).isNotEqualTo(null)
            .orderBy(MessageThread::lastMessageAt, Query.Direction.DESCENDING)
            .let {
                if (cursor != null) {
                    it.startAfter(cursor)
                } else {
                    it
                }
            }
            .paginate(query.pageable.pageSize)

        val participantIds = messageThreads.flatMap { it.userIds }.toSet()
        val users = participantIds
            .chunked(MAX_ARRAY_SIZE_IN_ARRAY_OPERATOR)
            .flatMap { usersCollection.where(User::id).isIn(it).fetchAll() }
            .associateBy { it.id }

        val threadWithLastMessage = messageThreads.map {
            // lastMessageId filed should be added MessageThread remove this n + 1 problem
            val lastMessage = getLastMessage(it.id)
            val participants = it.userIds.mapNotNull { userId -> users[userId] }.toSet()
            MessageThreadWithLastMessage(it, lastMessage, participants)
        }

        return Page(
            threadWithLastMessage,
            nextPageable(query.userId, query.pageable, messageThreads),
            hasNext,
        )
    }

    fun execute(query: GetMessageThread): MessageThreadWithPermissions {
        val messageThread = messageThreadsCollection[query.messageThreadId].get()
        if (query.userId !in messageThread.userIds) {
            throw ForbiddenException()
        }

        val userRelations = userRelationsService.userRelationsTo(query.userId, true, messageThread.userIds)
        val (canPost, relation, creators) = userRelations.canInteractWithRelationAndCreators(
            query.userId,
            messageThread.userIds,
        )

        val users = usersCollection.where(User::id).isIn(messageThread.userIds).fetchAll().associateBy { it.id }
        val participants = messageThread.userIds.mapNotNull { users[it] }.toSet()
        val permissions = MessageThreadPermissions(canPost, relation, creators)

        return MessageThreadWithPermissions(messageThread, permissions, participants)
    }

    private fun getLastMessage(messageThreadId: String) =
        postsCollection
            .where(Post::messageThreadId).isEqualTo(messageThreadId)
            .and(Post::state).isEqualTo(PostState.PUBLISHED)
            .orderBy(Post::id, Query.Direction.DESCENDING)
            .fetchSingle()
            ?: error("Message thread $messageThreadId does not have any messages")

    private fun nextPageable(
        userId: String,
        pageable: Pageable,
        messageThreads: List<MessageThread>,
    ): Pageable {
        val returnCursor = messageThreads
            .mapNotNull { it.lastMessageAt }
            .lastOrNull()
            ?.let {
                encodeCursor(userId, it)
            }

        return PageRequest(-1, pageable.pageSize, afterCursor = returnCursor)
    }

    private fun encodeCursor(
        userId: String,
        lastMessageAt: Instant,
    ): String = MessageThreadQueryServiceCursor(userId, lastMessageAt).toJson().toBase64()
}

data class MessageThreadWithLastMessage(
    val messageThread: MessageThread,
    val lastMessage: Post,
    val participants: Set<User>,
)

data class MessageThreadWithPermissions(
    val messageThread: MessageThread,
    val permissions: MessageThreadPermissions,
    val participants: Set<User>,
)

data class MessageThreadPermissions(
    val canPost: Boolean,
    val relation: SubscriptionRelationType,
    val creators: List<String>,
)

data class GetUsersActiveMessageThreads(val userId: String, val pageable: Pageable)

data class GetMessageThread(val userId: String, val messageThreadId: String)

private data class MessageThreadQueryServiceCursor(val userId: String, val lastMessageAt: Instant)
