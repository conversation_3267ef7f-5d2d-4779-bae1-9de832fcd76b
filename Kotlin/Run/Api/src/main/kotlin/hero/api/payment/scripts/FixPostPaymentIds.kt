package hero.api.payment.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.PostPayment

fun main() {
    val firestore = firestore(SystemEnv.cloudProject, false)
    val postPaymentsCollection = firestore.typedCollectionOf(PostPayment)

    val postPayments = postPaymentsCollection.fetchAll()

    postPayments
        .filter { it.id != PostPayment.id(userId = it.userId, postId = it.postId) }
        .forEach {
            val newId = PostPayment.id(userId = it.userId, postId = it.postId)
            val newPostPayment = it.copy(id = newId)
            log.info("Fixing from post payment id ${it.id} to $newId")
            postPaymentsCollection[it.id].delete()
            postPaymentsCollection[newId].set(newPostPayment)
        }
}
