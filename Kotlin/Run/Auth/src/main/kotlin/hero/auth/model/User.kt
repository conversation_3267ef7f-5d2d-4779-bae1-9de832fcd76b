package hero.auth.model

import com.fasterxml.jackson.annotation.JsonProperty
import org.http4k.security.oauth.core.RefreshToken
import org.http4k.security.openid.IdToken

data class DiscordUser(
    val id: String,
    val email: String?,
    @JsonProperty("username")
    val userName: String,
    val avatar: String?,
    val flags: Int,
    val locale: String,
    val discriminator: String,
    @JsonProperty("public_flags")
    val publicFlags: String? = null,
    val verified: Boolean,
)

/** Duplicate of Http4K's `AccessTokenDetail` has now `AccessToken` class private. */
data class AccessTokenDetails(
    val accessToken: AccessToken,
    val idToken: IdToken? = null,
)

/** Duplicate of Http4K's `AccessToken` which is now private. */
data class AccessToken(
    val value: String,
    val type: String?,
    val expiresIn: Long?,
    val scope: String?,
    val refreshToken: RefreshToken?,
)
