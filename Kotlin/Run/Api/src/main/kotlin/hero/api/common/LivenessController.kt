package hero.api.common

import hero.baseutils.log
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.sql.ConnectorConnectionPool
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class LivenessController() {
    @Suppress("unused")
    val routeGetLiveness: ContractRoute =
        ("/liveness").get(
            summary = "Liveness probe",
            tag = "Monitoring",
            parameters = object {},
            responses = listOf(Status.OK example Unit),
            handler = { _, _ ->
                val stats = ConnectorConnectionPool.stats()

                if (stats.totalConnections == 0) {
                    log.fatal("Connection pool is empty with stats $stats")

                    Response(Status.INTERNAL_SERVER_ERROR)
                } else {
                    // temporary debug log
                    log.info("Connection pool is healthy with stats $stats")

                    Response(Status.OK)
                }
            },
        )
}
