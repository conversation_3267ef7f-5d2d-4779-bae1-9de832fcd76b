package hero.api.library.controller.dto

import hero.api.library.service.dto.SavedPostWithData
import hero.api.post.controller.dto.toResponse
import hero.api.post.service.SavedCreatorPostInfo
import hero.model.PostRenderMeta

fun SavedPostWithData.toResponse() =
    SavedPostResponse(
        id = entity.id,
        savedAt = entity.savedAt,
        post = post.toResponse(
            PostRenderMeta(fullResponse = entity.subscriptionActive),
            savedCreatorPostInfo = SavedCreatorPostInfo(entity.id, entity.savedAt),
        ),
    )
