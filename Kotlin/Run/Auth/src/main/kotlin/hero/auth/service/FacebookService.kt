package hero.auth.service

import com.fasterxml.jackson.annotation.JsonProperty
import hero.baseutils.fromBase64
import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.HttpStatusException
import hero.jackson.fromJson
import java.time.Instant
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

class FacebookService(private val secret: String) {
    /** https://developers.facebook.com/docs/development/create-an-app/app-dashboard/data-deletion-callback/ */
    internal fun parseDeletionRequest(signedRequest: String): DeletionRequest {
        try {
            val splitRequest = signedRequest.split("=", limit = 2)
            if (splitRequest.size != 2 && splitRequest[0] != "signed_request") {
                log.error("Wrong signed request received: $signedRequest")
                throw BadRequestException("Signed request must be in form of `signed_request=####`")
            }
            val (signatureB64, payloadB64) = splitRequest[1]
                // the payload needs these replacements to be base64
                .replace("-", "+")
                .replace("_", "/")
                .split(".", limit = 2)
            val expectedSignature = signatureB64.fromBase64()
            if (payloadB64.hmac() != expectedSignature) {
                log.error("Invalid signature when parsing: $signedRequest")
                throw ForbiddenException("Signature was invalid.")
            }
            val payload = payloadB64.fromBase64()
            return payload.fromJson<DeletionRequest>()
        } catch (e: HttpStatusException) {
            throw e
        } catch (e: IllegalArgumentException) {
            throw ForbiddenException("Invalid signature when parsing: $signedRequest", cause = e)
        } catch (e: Exception) {
            throw IllegalStateException("Error when parsing: $signedRequest", e)
        }
    }

    private fun String.hmac(): String {
        val algorithm = "HmacSHA256"
        val mac = Mac.getInstance(algorithm)
        val secretKey = SecretKeySpec(secret.toByteArray(), algorithm)
        mac.init(secretKey)
        return String(mac.doFinal(this.toByteArray()))
    }
}

data class DeletionRequest(
    @JsonProperty("user_id")
    val userId: String,
    val algorithm: String,
    @JsonProperty("issued_at")
    private val issuedAtRaw: Long,
) {
    val issuedAt: Instant
        get() = Instant.ofEpochSecond(issuedAtRaw)
}
