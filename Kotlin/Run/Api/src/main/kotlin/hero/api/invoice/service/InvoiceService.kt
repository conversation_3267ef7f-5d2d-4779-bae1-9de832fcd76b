package hero.api.invoice.service

import com.github.kittinunf.fuel.httpPost
import hero.baseutils.internalFunctionCall
import hero.baseutils.log
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.SharedDriveIds.INVOICE_REPORTS
import hero.gcloud.TypedCollectionReference
import hero.gcloud.batched
import hero.gcloud.createSpreadsheet
import hero.gcloud.resizeColumns
import hero.gcloud.shareableLink
import hero.gcloud.string
import hero.gcloud.where
import hero.gcloud.writeRangeFormatted
import hero.jackson.toJson
import hero.model.GenerateInvoiceReportRequest
import hero.model.Invoice
import hero.model.User

class InvoiceService(
    private val invoiceRepository: TypedCollectionReference<Invoice>,
    private val userRepository: TypedCollectionReference<User>,
) {
    fun createReportSheet(
        invoiceId: String,
        creatorId: String,
        authToken: String,
        forceRefresh: Boolean,
    ): Invoice {
        val invoice = invoiceRepository
            .where(Invoice::userId).isEqualTo(creatorId)
            .and(Invoice::invoiceId).isEqualTo(invoiceId)
            .fetchSingle()
            ?: throw NotFoundException("Invoice $invoiceId was not found.")

        if (authToken != invoice.authToken) {
            throw ForbiddenException()
        }

        if (!forceRefresh && invoice.sheetReportLink != null) {
            return invoice
        }

        val user = userRepository[creatorId].get()
        val fileName = "${user.name} report for ${invoice.invoiceId}"

        val spreadsheet = createSpreadsheet(fileName, INVOICE_REPORTS, creatorId)
            .apply {
                val message = string("Report is being generated, please come back in few minutes")
                batched()
                    .writeRangeFormatted(listOf(listOf(message)), 1, 1)
                    .resizeColumns(1, 1, 600)
                    .execute()
            }

        return invoice.copy(sheetReportLink = spreadsheet.shareableLink())
            .also {
                invoiceRepository[it.id].set(it)
                internalFunctionCall("invoice-report-generator")
                    .httpPost()
                    .body(
                        GenerateInvoiceReportRequest(
                            invoiceId = it.invoiceId,
                            currency = invoice.currencyInvoice,
                            spreadsheetId = spreadsheet.spreadsheetId,
                            authToken = authToken,
                        ).toJson(),
                    )
                    .response { _, _, result ->
                        log.info("Invoice report generation responded with $result")
                    }
            }
    }
}
