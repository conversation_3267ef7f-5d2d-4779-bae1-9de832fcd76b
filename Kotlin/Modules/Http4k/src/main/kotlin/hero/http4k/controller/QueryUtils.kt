package hero.http4k.controller

import hero.core.data.Sort
import hero.http4k.config.PAGE_SIZE_DEFAULT
import hero.http4k.config.PAGE_SIZE_MAX
import org.http4k.lens.BiDiPathLensSpec
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.enum
import org.http4k.lens.int
import org.http4k.lens.regex
import org.http4k.lens.string

object QueryUtils {
    fun pageIndex() = Query.int().defaulted("pageIndex", 0, "Pagination index (starts from 0).")

    fun pageSize(
        size: Int = PAGE_SIZE_DEFAULT,
        pageMax: Int? = PAGE_SIZE_MAX,
    ) = Query.int().defaulted(
        "pageSize",
        size,
        "Size of retrieved page. Default is $size${if (pageMax != null) ", maximum value is $PAGE_SIZE_MAX" else ""}.",
    )

    fun Path.userId(): BiDiPathLensSpec<String> = regex("([a-z-]+)")

    fun afterCursor() =
        Query
            .string()
            .optional("afterCursor", "Returns the elements that come after the specified cursor.")

    fun beforeCursor() =
        Query
            .string()
            .optional("beforeCursor", "Returns the elements that come before the specified cursor.")

    fun sortDirection() =
        Query
            .enum<Sort.Direction>()
            .optional("sort", "Direction to sort by, either ${Sort.Direction.DESC} or ${Sort.Direction.ASC}")
}
