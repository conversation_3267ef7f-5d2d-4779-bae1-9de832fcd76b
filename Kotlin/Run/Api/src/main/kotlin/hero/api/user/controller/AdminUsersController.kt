package hero.api.user.controller

import hero.api.user.controller.dto.exampleUserDetailsResponse
import hero.api.user.controller.dto.toDetailsResponse
import hero.api.user.service.GetUserDetails
import hero.api.user.service.UserQueryService
import hero.exceptions.http.ForbiddenException
import hero.http4k.auth.getJwtUser
import hero.http4k.auth.isImpersonation
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.model.Role
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path
import org.http4k.lens.string

class AdminUsersController(
    private val userQueryService: UserQueryService,
) {
    @Suppress("Unused")
    val routeGetUserById: ContractRoute =
        ("/admin/v1/users" / Path.string().of("userId")).get(
            summary = "Get user's details",
            tag = "Admin",
            parameters = object {},
            responses = listOf(Status.OK example exampleUserDetailsResponse),
            handler = { request, _, userId ->
                val user = request.getJwtUser()
                if (user.roleIndex != Role.MODERATOR.ordinal && !request.isImpersonation()) {
                    throw ForbiddenException()
                }
                val result = userQueryService.execute(GetUserDetails(userId))

                // maybe we should use different response here, for now we can just use the details response
                Response(Status.OK).body(result.toDetailsResponse())
            },
        )
}
