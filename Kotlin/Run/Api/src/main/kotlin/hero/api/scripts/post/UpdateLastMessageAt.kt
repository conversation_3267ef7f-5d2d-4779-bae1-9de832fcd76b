package hero.api.scripts.post

import com.google.cloud.firestore.Query
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.MessageThread
import hero.model.Post
import java.time.Instant

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)

    val collection = firestore.typedCollectionOf(MessageThread)
    val postCollection = firestore.typedCollectionOf(Post)

    for (thread in collection.fetchAll()) {
        val post = postCollection
            .where(Post::messageThreadId).isEqualTo(thread.id)
            .orderBy(Post::published, Query.Direction.DESCENDING)
            .limit(1)
            .fetchSingle()

        if (post != null) {
            log.info("Setting lastMessageAt ${thread.id} to ${post.published}.")
            collection[thread.id].field(MessageThread::lastMessageAt).update(post.published)
            collection[thread.id].field(MessageThread::lastMessageBy).update(post.userId)
            log.info("${thread.id} ${thread.emailNotified} -> ${post.published < Instant.now().minusDays(7)}")
            collection[thread.id].field(MessageThread::emailNotified)
                .update(post.published < Instant.now().minusDays(7))
        }
    }
}
