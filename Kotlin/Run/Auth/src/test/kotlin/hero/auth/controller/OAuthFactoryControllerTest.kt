package hero.auth.controller

import hero.auth.model.AccessToken
import hero.auth.model.AccessTokenDetails
import hero.baseutils.mockNow
import hero.baseutils.plusDays
import hero.jackson.map
import hero.jwt.toJwt
import hero.model.ExtractedUser
import hero.model.OAuthProvider
import hero.model.Role
import hero.model.UserIdResponse
import hero.model.UserStateChange
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.security.openid.IdToken
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import java.time.Instant
import kotlin.test.assertEquals

internal class OAuthFactoryControllerTest {
    @ParameterizedTest
    @ValueSource(strings = ["true", "false"])
    fun testUserFactory(production: Boolean) {
        val domain = if (production) "https://herohero.co" else "https://devel.herohero.co"
        val controller = spyk(
            DiscordCreatorOAuthController(
                hostnameServices = domain,
                hostname = domain,
                servicePath = "/auth",
                credentials = mockk(),
                pubSub = mockk(relaxed = true),
            ),
        )

        mockNow("2030-01-01T00:00:00Z")
        val tokenExpiresAt = Instant.now().plusDays(1)
        val extractedUser = ExtractedUser(
            id = "evelyn",
            userId = null,
            secondaryId = null,
            provider = OAuthProvider.DISCORD,
            name = "Anne Le Vauban",
            email = "<EMAIL>",
            imageUrl = "anne.jpg",
            accessToken = "123",
            refreshToken = "456",
            tokenExpiresAt = tokenExpiresAt,
        )
        val tokenDetails =
            AccessTokenDetails(
                AccessToken(value = "123", type = null, expiresIn = null, scope = null, refreshToken = null),
                IdToken(value = "345"),
            )
        val request = Request(Method.GET, "/discord-creator/v1/tokens?tokens=${tokenDetails.map().toJwt()}")
        every {
            controller.handleAccessToken(
                tokenDetails,
                any(),
                request,
            )
        } returns (extractedUser to domain)
        every {
            controller.userFactory(extractedUser)
        } returns UserIdResponse(extractedUser.id, Role.USER, UserStateChange.CREATED, false)
        val response: Response = controller.routeTokens(request)
        assertEquals(Status.FOUND, response.status)
        assertEquals(
            "$domain/oauth-callback?status=200&service=discord-creator&message=OK&userId=evelyn&state=CREATED",
            response.header("Location"),
        )
    }
}
