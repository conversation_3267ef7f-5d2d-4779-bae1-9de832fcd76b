package hero.api.user.scripts

import com.stripe.StripeClient
import com.stripe.param.SubscriptionSearchParams
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.gcloud.PubSub
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.get
import hero.gcloud.set
import hero.model.CancelledByRole
import hero.model.Tier
import hero.model.User
import hero.model.UserStatus
import hero.model.topics.RefundMethod
import hero.model.topics.SubscriptionCancelRequest
import java.time.Instant

fun main() {
    val firestore = firestore(SystemEnv.cloudProject)
    val production = true
    val stripeClient = StripeClient(SystemEnv.stripeKeyEu)
    val pubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject)

    val usersCollection = firestore["${production.envPrefix}-users"]

    val deletedCreators = usersCollection
        .whereEqualTo(User::status.name, UserStatus.DELETED)
        .fetchAll<User>()
        .filter { (it.counts.incomes ?: 0) > 0 || it.counts.supporters > 0 }

    for (creator in deletedCreators) {
        if ("202" !in creator.email!!) {
            println(creator.email + " is being overwritten")
            usersCollection[creator.id, User::email] = creator.email + "-" + (creator.deletedAt ?: Instant.now())
        }
        val currency = Tier.ofId(creator.creator.tierId).currency
        stripeClient
            .subscriptions()
            .search(
                SubscriptionSearchParams.builder().setQuery(
                    "metadata[\"creatorId\"]:\"${creator.id}\" AND status:\"active\"",
                ).build(),
            )
            .autoPagingIterable()
            .forEach {
                pubSub.publish(
                    SubscriptionCancelRequest(
                        subscriptionId = it.id,
                        cancelledBy = "infoheroherokamniiih",
                        cancelledByRole = CancelledByRole.MODERATOR,
                        atPeriodEnd = false,
                        refundMethod = RefundMethod.REFUND_IF_NOT_PAID_OUT,
                        currency = currency,
                    ),
                )
                println(it.id)
            }
        println(
            "https://console.cloud.google.com/firestore/databases/-default-/data/panel" +
                "/prod-users/${creator.id}?project=heroheroco",
        )
    }
}
