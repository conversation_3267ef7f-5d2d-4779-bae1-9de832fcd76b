package hero.api.subscriber.scripts

import com.stripe.param.SubscriptionSearchParams
import com.stripe.param.SubscriptionUpdateParams
import hero.api.payment.scripts.initializeStripeScript
import hero.baseutils.SystemEnv
import hero.gcloud.PubSub
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Currency
import hero.model.User
import hero.model.topics.FeesUpdateRequested
import hero.stripe.service.stripeRetry
import java.math.BigDecimal
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

fun main() {
    val (_, _, _, _, clients) = initializeStripeScript(true)
    val currency = Currency.EUR
    val production = true
    val environment = "prod"

    val creatorId = "mikeoganesjandnqgmopj"
    val targetAccountId = "acct_1QUBSYB15QTMwBSa"
    val fee = BigDecimal(10)

    val executor = Executors.newFixedThreadPool(20)
    val pubSub = PubSub(environment, SystemEnv.cloudProject)
    val firestore = firestore(SystemEnv.cloudProject, production)
    val usersCollection = firestore.typedCollectionOf(User)

    clients[currency].subscriptions().search(
        SubscriptionSearchParams.builder()
            .setLimit(100)
            .setQuery(
                "metadata[\"creatorId\"]:\"$creatorId\" AND status:\"active\"",
            ).build(),
    )
        .autoPagingIterable()
        .forEach {
            if (it.transferData.destination == targetAccountId) {
                println(
                    "https://dashboard.stripe.com/subscriptions/${it.id} " +
                        "already migrated to $targetAccountId",
                )
            } else {
                executor.submit {
                    println(
                        "https://dashboard.stripe.com/subscriptions/${it.id} " +
                            "${it.transferData.destination} -> $targetAccountId",
                    )
                    stripeRetry {
                        // WARN: be warned this will nullify all the fee transfers! Need to regenerate fees below…
                        it.update(
                            SubscriptionUpdateParams.builder().setTransferData(
                                SubscriptionUpdateParams.TransferData.builder().setDestination(targetAccountId).build(),
                            ).build(),
                        )
                    }
                }
            }
        }

    executor.shutdown()
    executor.awaitTermination(Long.MAX_VALUE, TimeUnit.HOURS)

    // WARN: regenerate all the fees for concerned users
    val user = usersCollection[creatorId].get()
    pubSub.publish(
        FeesUpdateRequested(
            creatorId = user.id,
            vatId = user.company!!.vatId,
            tierId = user.creator.tierId,
            country = user.company!!.country!!,
            forced = true,
        ),
    )
}
