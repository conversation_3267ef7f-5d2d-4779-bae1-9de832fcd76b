package hero.stripe.service

import hero.baseutils.instantOf
import hero.baseutils.systemEnv
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class StripeFeeServiceKtTest {
    private val now = instantOf("2024-10-10T00:00:00Z")
    private val vatMapping = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD")).countryToVatMapping()
    private val testFee = BigDecimal("10.00")

    @Test
    fun `non-EU creators always have no VAT`() {
        val (transferCents, feeCents, feeVatCents) = computeFee(testFee, "NON-EU-VAT", "US", now, vatMapping)
        // non-EU have 100% transfers (defined by null here) and non-zero application fee
        assertEquals(null, transferCents)
        assertEquals(BigDecimal("10.00"), feeCents)
        assertEquals(BigDecimal("0.00"), feeVatCents)
    }

    @Test
    fun `CZ creators with VAT always have VAT`() {
        val (transferCents, feeCents, feeVatCents) = computeFee(testFee, "CZ123456789", "CZ", now, vatMapping)
        assertEquals(BigDecimal("87.90"), transferCents)
        assertEquals(BigDecimal("10.00"), feeCents)
        assertEquals(BigDecimal("2.10"), feeVatCents)
    }

    @Test
    fun `CZ creators without VAT always have VAT`() {
        val (transferCents, feeCents, feeVatCents) = computeFee(testFee, null, "CZ", now, vatMapping)
        assertEquals(BigDecimal("87.90"), transferCents)
        assertEquals(BigDecimal("10.00"), feeCents)
        assertEquals(BigDecimal("2.10"), feeVatCents)
    }

    @Test
    fun `SK creators without VAT always have VAT`() {
        val (transferCents, feeCents, feeVatCents) = computeFee(testFee, null, "SK", now, vatMapping)
        assertEquals(BigDecimal("88.00"), transferCents)
        assertEquals(BigDecimal("10.00"), feeCents)
        assertEquals(BigDecimal("2.00"), feeVatCents)
    }

    @Test
    fun `SK creators with VAT have no VAT`() {
        val (transferCents, feeCents, feeVatCents) = computeFee(testFee, "SK1234567", "SK", now, vatMapping)
        assertEquals(BigDecimal("90.00"), transferCents)
        assertEquals(BigDecimal("10.00"), feeCents)
        assertEquals(BigDecimal("0.00"), feeVatCents)
    }
}
