package hero.http4k.controller

import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Status
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class SwaggerUiControllerTest {
    val handler = swaggerUi("/api/docs")

    @Test
    fun swaggerUiIsAvailable() {
        val response = handler(Request(Method.GET, "/docs/index.html"))
        assertEquals(Status.OK, response.status)
    }
}
