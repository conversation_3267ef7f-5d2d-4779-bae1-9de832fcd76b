package hero.api.messages.service

import hero.api.post.service.PostService
import hero.sql.jooq.Tables
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant

class MessageCommandServiceIT : IntegrationTest() {
    @Test
    fun `should send a message to a thread`() {
        val underTest = MessageCommandService(
            TestCollections.messageThreadsCollection,
            PostService(
                postsCollection = TestCollections.postsCollection,
                postRepository = TestRepositories.postRepository,
                gjirafaService = mockk(),
                gjirafaLivestreamService = mockk(),
                pubSub = pubSubMock,
            ),
        )

        testHelper.createUser("filip")
        testHelper.createMessageThread("filip", listOf("pablo"), id = "message-thread-id")

        val message = underTest.execute(SendMessage("filip", "message-thread-id", "text"))

        val storedMessage = TestRepositories.postRepository.getById(message.id)

        assertThat(storedMessage).isEqualTo(message)
        assertThat(storedMessage.text).isEqualTo("text")
        assertThat(storedMessage.userId).isEqualTo("filip")
        assertThat(storedMessage.messageThreadId).isEqualTo("message-thread-id")
        assertThat(storedMessage.published).isBetween(Instant.now().minusSeconds(5), Instant.now())
        assertThat(storedMessage.assets).isEmpty()
        assertThat(storedMessage.parentId).isNull()
        assertThat(storedMessage.siblingId).isNull()
        assertThat(storedMessage.parentPostId).isNull()
        assertThat(storedMessage.parentUserId).isNull()

        val messageRecord = testContext.selectFrom(Tables.POST).where(Tables.POST.ID.eq(message.id)).fetchSingle()
        assertThat(messageRecord.type).isEqualTo("MESSAGE")
    }
}
