package hero.test.logging

import hero.core.logging.Logger
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger as ApacheLogger

val log4j: ApacheLogger = LogManager.getLogger()

object TestLogger : Logger {
    override fun warn(
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        log4j.warn(message, cause)
    }

    override fun notice(
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        log4j.trace(message, cause)
    }

    override fun debug(
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        log4j.debug(message, cause)
    }

    override fun info(
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        log4j.info(message, cause)
    }

    override fun error(
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        log4j.error(message, cause)
    }

    override fun fatal(
        message: String?,
        properties: Map<String, Any?>,
        cause: Throwable?,
    ) {
        log4j.fatal(message, cause)
    }
}
