package hero.api.post.service

import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.entry
import hero.gcloud.increment
import hero.gcloud.root
import hero.model.Poll
import hero.model.PollOption
import hero.model.Post
import hero.repository.subscription.JooqSubscriptionHelper
import hero.sql.jooq.Tables.POLL
import hero.sql.jooq.Tables.POLL_OPTION
import hero.sql.jooq.Tables.POLL_OPTION_VOTE
import hero.sql.jooq.Tables.SUBSCRIPTION
import hero.sql.jooq.tables.records.PollOptionVoteRecord
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant

class PollCommandService(
    lazyContext: Lazy<DSLContext>,
    private val postsCollection: TypedCollectionReference<Post>,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context by lazyContext

    fun execute(command: CastVotesCommand) {
        val post = postsCollection[command.pollId].get()

        val poll = post.poll
        if (poll == null) {
            throw BadRequestException("Poll is not set on post ${post.id}")
        }

        val hasValidSubscription = if (command.userId != post.userId) {
            context.selectFrom(SUBSCRIPTION)
                .where(SUBSCRIPTION.USER_ID.eq(command.userId).and(SUBSCRIPTION.CREATOR_ID.eq(post.userId)))
                .and(JooqSubscriptionHelper.activeSubscription)
                .fetch()
                .isNotEmpty
        } else {
            true
        }

        if (!hasValidSubscription) {
            throw ForbiddenException("User ${command.userId} does not subscribe ${post.userId}")
        }

        if (poll.deadline.isBefore(Instant.now()) == true) {
            throw BadRequestException("Poll ${poll.id} is closed")
        }

        val voteOptionIds = command.votes.map { it.optionId }
        val pollOptionIds = poll.options.keys

        if (!pollOptionIds.containsAll(voteOptionIds)) {
            throw BadRequestException("Invalid poll option ids were passed ${voteOptionIds - pollOptionIds}")
        }

        val previousVoteOptionIds = context
            .select(POLL_OPTION_VOTE.POLL_OPTION_ID)
            .from(POLL_OPTION)
            .join(POLL_OPTION_VOTE).on(POLL_OPTION_VOTE.POLL_OPTION_ID.eq(POLL_OPTION.ID))
            .where(POLL_OPTION.POLL_ID.eq(poll.id))
            .and(POLL_OPTION_VOTE.USER_ID.eq(command.userId))
            .fetch()
            .map { it.value1() }

        // there is no change in votes
        if (voteOptionIds == previousVoteOptionIds) {
            return
        }

        val newVoteOptionIds = voteOptionIds - previousVoteOptionIds
        val removedVoteOptionIds = previousVoteOptionIds - voteOptionIds

        newVoteOptionIds.forEach {
            val voteCountFieldPath = root(Post::poll).path(Poll::options).entry(it).path(PollOption::voteCount)
            postsCollection[post.id].field(voteCountFieldPath).increment(1)
        }

        removedVoteOptionIds.forEach {
            val voteCountFieldPath = root(Post::poll).path(Poll::options).entry(it).path(PollOption::voteCount)
            postsCollection[post.id].field(voteCountFieldPath).increment(-1)
        }

        context
            .deleteFrom(POLL_OPTION_VOTE)
            .where(POLL_OPTION_VOTE.POLL_OPTION_ID.`in`(pollOptionIds))
            .and(POLL_OPTION_VOTE.USER_ID.eq(command.userId))
            .execute()

        val voteRecords = newVoteOptionIds.map {
            PollOptionVoteRecord().apply {
                pollOptionId = it
                userId = command.userId
            }
        }

        context
            .insertInto(POLL_OPTION_VOTE)
            .set(voteRecords)
            .execute()
    }

    fun execute(command: EndPoll) {
        val post = postsCollection[command.pollId].get()

        val poll = post.poll
        if (poll == null) {
            throw BadRequestException("Poll is not set on post ${post.id}")
        }

        if (post.userId != command.userId) {
            throw ForbiddenException("User ${command.userId} is not the creator of the post ${post.id}")
        }

        val now = Instant.now(clock)
        if (poll.deadline.isBefore(now)) {
            throw BadRequestException("Poll ${poll.id} is already closed")
        }

        postsCollection[command.pollId].field(root(Post::poll).path(Poll::deadline)).update(now)
        context
            .update(POLL)
            .set(POLL.DEADLINE, now)
            .where(POLL.ID.eq(poll.id))
            .execute()
    }
}

data class CastVotesCommand(val userId: String, val pollId: String, val votes: List<Vote>)

data class Vote(val optionId: String)

data class EndPoll(val userId: String, val pollId: String)
