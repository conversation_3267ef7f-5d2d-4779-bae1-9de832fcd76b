package hero.api.invoice.scripts

import com.github.kittinunf.fuel.httpGet
import hero.baseutils.SystemEnv
import hero.baseutils.fetch
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Currency
import hero.model.Invoice
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneOffset

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)
    val collection = firestore.typedCollectionOf(Invoice)
    val currencyMap = hashMapOf<LocalDate, Map<Currency, BigDecimal>>()

    fun getRate(date: LocalDate): Long =
        currencyMap
            .getOrPut(date) { fetchConversionRates(date) }
            .get(Currency.CZK)!!
            .movePointRight(2).toLong()

    collection.fetchAll()
        .asSequence()
        .forEach {
            val rate = if (it.currencyInvoice == Currency.EUR)
                getRate(it.timestamp.atZone(ZoneOffset.UTC).toLocalDate())
            else
                null
            println(it.id + " " + rate)
            collection[it.id].field(Invoice::eurConversionRateCents).update(rate)
        }
}

fun fetchConversionRates(date: LocalDate) =
    "https://europe-west1-heroheroco.cloudfunctions.net/prod-conversion-rates/day/$date"
        .httpGet()
        .fetch<ConversionRatesResponse>()
        .rates

data class ConversionRatesResponse(val rates: Map<Currency, BigDecimal>)
