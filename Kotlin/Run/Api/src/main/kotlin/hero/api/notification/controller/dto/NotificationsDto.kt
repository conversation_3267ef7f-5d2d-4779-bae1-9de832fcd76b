package hero.api.notification.controller.dto

import hero.api.user.controller.dto.UserResponse
import hero.core.data.SimplePageResponse
import hero.model.NotificationType
import hero.model.StorageEntityType
import java.time.Instant

data class NotificationResponse(
    val id: String,
    val type: NotificationType,
    val createdAt: Instant,
    val checkedAt: Instant?,
    val seenAt: Instant?,
    val actorCount: Int,
    val lastActor: UserResponse?,
    val relationships: NotificationRelationships,
)

data class NotificationRelationships(
    val objectId: String?,
    val objectType: StorageEntityType,
    val lastActorId: String,
)

data class UpdateNotificationInput(val checkedAt: Instant?, val seenAt: Instant?)

data class PagedNotificationResponse(
    override val content: List<NotificationResponse>,
    override val hasNext: Boolean,
    override val afterCursor: String? = null,
    override val beforeCursor: String? = null,
) : SimplePageResponse<NotificationResponse>

data class NotificationSettingsResponse(
    val emailNewPost: <PERSON>olean,
    val emailNewDm: <PERSON>olean,
    val pushNewPost: <PERSON>olean,
    val pushNewComment: Boolean,
    val newsletter: Boolean,
    val termsChanged: Boolean,
)

data class NotificationSettingsUpdateRequest(
    val emailNewPost: Boolean,
    val emailNewDm: Boolean,
    val pushNewPost: Boolean,
    val pushNewComment: Boolean,
    val newsletter: Boolean,
    val termsChanged: Boolean,
)
