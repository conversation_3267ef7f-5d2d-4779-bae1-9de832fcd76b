package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.nullIfEmpty
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gjirafa.GjirafaLivestreamsService
import hero.model.GjirafaLivestreamMeta
import hero.model.User

@Suppress("unused")
class GjirafaLivestreamEnabler(
    private val isProduction: Boolean = SystemEnv.isProduction,
    private val hostnameServices: String = SystemEnv.hostnameServices,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, isProduction),
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val gjirafa: GjirafaLivestreamsService = GjirafaLivestreamsService(
        projectId = SystemEnv.gjirafaProject,
        apiKey = SystemEnv.gjirafaApiKey,
    ),
) : FirestoreEventSubcriber() {
    override fun consume(event: FirestoreEvent) {
        exportUser(event.documentId)
    }

    private fun exportUser(userId: String) {
        val user = usersCollection[userId].fetch()
            ?: error("User $userId was not found.")
        if (user.hasLivestreams && user.gjirafaLivestream == null) {
            log.info("Creating channel for user $userId.", mapOf("userId" to userId))
            val image = user.image?.id
                ?: "https://storage.googleapis.com/heroheroco-assets/static/og/default-thumbnail.png"
            val channel = gjirafa.postChannel(userId, user.name, image)
            val gjirafaMeta = GjirafaLivestreamMeta(
                publicId = channel.publicId,
                streamUrl = channel.streamServer,
                streamKey = channel.streamKey,
                playbackUrl = channel.playbackUrl.nullIfEmpty(),
            )
            usersCollection[userId].field(User::gjirafaLivestream).update(gjirafaMeta)
        }
    }
}
