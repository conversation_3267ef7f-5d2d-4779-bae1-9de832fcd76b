package hero.api.messages.service

import com.google.cloud.firestore.Query
import hero.baseutils.fromBase64
import hero.baseutils.toBase64
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.util.paginate
import hero.gcloud.where
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.MessageThread
import hero.model.Post
import hero.model.PostPayment
import hero.model.topics.PostState
import java.time.Instant

class MessageQueryService(
    private val messageThreadsCollection: TypedCollectionReference<MessageThread>,
    private val messagesCollection: TypedCollectionReference<Post>,
    private val messagePaymentsCollection: TypedCollectionReference<PostPayment>,
) {
    fun execute(query: GetMessage): MessageWithPayment {
        val message = messagesCollection[query.messageId].get()
        val threadId = message.messageThreadId ?: throw BadRequestException("${query.messageId} is not a message")

        val messageThread = messageThreadsCollection[threadId].get()
        if (query.userId !in messageThread.userIds) {
            throw ForbiddenException()
        }

        val messagePayment = if ((message.price ?: 0) > 0) {
            messagePaymentsCollection
                .where(PostPayment::postId).isEqualTo(message.id)
                .and(PostPayment::userId).isEqualTo(query.userId)
                .fetchSingle()
        } else {
            null
        }

        return MessageWithPayment(message, messagePayment)
    }

    fun execute(query: GetMessagesFromThread): Page<MessageWithPayment> {
        val messageThread = messageThreadsCollection[query.messageThreadId].get()
        if (query.userId !in messageThread.userIds) {
            throw ForbiddenException()
        }

        val deletedAt = messageThread.deletes[query.userId]

        val cursor = query.pageable.afterCursor?.fromBase64()?.fromJson<GetMessagesFromThreadCursor>()?.lastPublishedAt
        val (messages, hasNext) = messagesCollection
            .where(Post::messageThreadId).isEqualTo(query.messageThreadId)
            .let {
                if (deletedAt != null) {
                    it.and(Post::published).isGreaterThan(deletedAt)
                } else {
                    it
                }
            }
            .and(Post::state).isEqualTo(PostState.PUBLISHED)
            .orderBy(Post::published, Query.Direction.DESCENDING)
            .startAfterIfNotNull(cursor)
            .paginate(query.pageable.pageSize)

        // we don't need to find payments for my messages, since there will always be none
        val otherUsersMessagesIds = messages.filter { it.userId != query.userId }.map { it.id }
        val messagePayments = messagePaymentsCollection
            .where(PostPayment::postId).isIn(otherUsersMessagesIds)
            .fetchAll()
            .associateBy { paymentKey(it.userId, it.postId) }

        val messagesWithPayments = messages
            .map { MessageWithPayment(it, messagePayments[paymentKey(query.userId, it.id)]) }

        return Page(
            messagesWithPayments,
            nextPageable(messages, query.pageable),
            hasNext,
        )
    }

    private fun paymentKey(
        userId: String?,
        postId: String?,
    ) = "$userId-$postId"

    private fun nextPageable(
        messages: List<Post>,
        pageable: Pageable,
    ): Pageable {
        val afterCursor = messages
            .lastOrNull()
            ?.let {
                GetMessagesFromThreadCursor(it.id, it.published).toJson().toBase64()
            }

        return PageRequest(-1, pageable.pageSize, afterCursor = afterCursor)
    }
}

private data class GetMessagesFromThreadCursor(val messageId: String, val lastPublishedAt: Instant)

data class GetMessagesFromThread(val userId: String, val messageThreadId: String, val pageable: Pageable)

data class GetMessage(val userId: String, val messageId: String)

data class MessageWithPayment(val message: Post, val payment: PostPayment? = null)
