package hero.api.user.scripts

import hero.baseutils.SystemEnv
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Subscriber
import hero.model.SubscriberStatus

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)
    val collection = firestore.typedCollectionOf(Subscriber)
    val subscribers = collection
        .where(Subscriber::creatorId).isEqualTo("davidvanicekaauyustx")
        .fetchAll()

    val active = subscribers.filter { it.status == SubscriberStatus.ACTIVE }
    val pastDue = subscribers.filter { it.status == SubscriberStatus.PAST_DUE }
    val free = subscribers.filter { it.tierId == null }
    println("${active.size} ${pastDue.size} ${free.size}")
}
