package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.HttpStatusException
import hero.exceptions.http.NotFoundException
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.fetchSingle
import hero.gcloud.firestore
import hero.gcloud.get
import hero.gcloud.typedCollectionOf
import hero.gcloud.union
import hero.gcloud.where
import hero.http4k.extensions.get
import hero.http4k.extensions.userAgent
import hero.http4k.serverless.CloudHttpFunction
import hero.jwt.heroPublicKey
import hero.jwt.parseJwt
import hero.model.RssFeed
import hero.model.SpotifyCredentials
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.User
import io.jsonwebtoken.Claims
import org.http4k.contract.ContractRoute
import org.http4k.core.ContentType
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Query
import org.http4k.lens.contentType
import org.http4k.serverless.GoogleCloudHttpFunction
import java.security.PublicKey

class RssFeedProvider(
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, SystemEnv.isProduction),
    private val environment: String = SystemEnv.environment,
    private val envPrefix: String = "svc-" + environment.replace("local", "devel"),
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val rssFeedsCollections: TypedCollectionReference<RssFeed> = firestore.typedCollectionOf(RssFeed),
    private val subscribersCollection: TypedCollectionReference<Subscriber> = firestore.typedCollectionOf(Subscriber),
    private val credentials: SpotifyCredentials =
        firestore.firestore["constants"]["oauth-spotify"].fetchSingle<SpotifyCredentials>()!!,
    private val rssFeedGenerator: RssFeedGenerator = RssFeedGenerator(),
    systemEnvs: EnvironmentVariables = SystemEnv,
) : CloudHttpFunction(systemEnvs) {
    @Suppress("Unused")
    class EntryClass(rssFeedProvider: RssFeedProvider = RssFeedProvider()) :
        GoogleCloudHttpFunction(rssFeedProvider)

    // Note that when user stop subscribing creators, they usually don't remove these feeds.
    // That unfortunately leads to clients "DDOSing" these endpoints which always returns 401.
    // For that reasone, we need to cache these 401 responses to avoid excessive DB access.
    // See Cloudflare cache rules which set these.
    private val routeRootGet: ContractRoute =
        "/".get(
            summary = "Generate rss feed",
            tag = "RssFeedGenerator",
            parameters = object {
                val token = Query.required("token")
            },
            responses = listOf(Status.OK to Unit),
            handler = { request, parameters ->
                val token = parameters.token(request)
                val userAgent = request.userAgent?.takeIf { it.isNotBlank() }
                    ?: throw BadRequestException("User agent must be present.")

                val (creator, isSpotify) = validateRssToken(token = token, userAgent = userAgent)

                Response(Status.OK)
                    .contentType(ContentType.TEXT_XML)
                    .body(getRssFeed(creator, token, isSpotify))
            },
        )

    private fun getRssFeed(
        creator: User,
        token: String,
        isSpotify: Boolean,
    ): String {
        val generatedRssFeeds = rssFeedGenerator.execute(FindRssFeed(creator.id))
            ?: rssFeedGenerator.execute(GenerateAndStoreRssFeed(creator, envPrefix, credentials))

        val feed = if (isSpotify) {
            generatedRssFeeds.spotify
        } else {
            generatedRssFeeds.basic
        }

        // this should be safe to do, since we have validated that token is our token in validation process
        return feed.replace(generatedRssFeeds.placeholderToken, token)
    }

    private fun validateRssToken(
        token: String,
        userAgent: String,
    ): Pair<User, Boolean> {
        val jwt = parseToken(token)
            ?: throw ForbiddenException("Given token was invalid.")

        val userId = jwt["u"] as String
        val creatorId = jwt["c"] as String
        val iat = jwt["t"].toString().toLong()

        val creator = usersCollection[creatorId].fetch()
            ?: throw NotFoundException("Creator $creatorId was not found.", mapOf("creatorId" to creatorId))

        val isSpotify = jwt["s"]?.toString()?.toBooleanStrictOrNull() ?: false &&
            creator.hasSpotifyExport

        if (isSpotify) {
            if (userId != creatorId) {
                throw ForbiddenException(
                    "Illegal access to Spotify feed: $userId/$creatorId.",
                    mapOf("userId" to userId, "creatorId" to creatorId),
                )
            }

            if (("Spotify/" !in userAgent)) {
                // Note that Cloudflare ignores the `Vary` header so we cannot distinguish caching by
                // User-Agent differences: https://developers.cloudflare.com/cache/concepts/cache-control/#other
                //
                // As we cache HTTP 400-403 headers on RSS feed to be cached indefinitely, we must
                // send some other header (HTTP 412) so that the feed is not cached when creators
                // are trying to open their Spotify feed.
                throw HttpStatusException(
                    status = 412,
                    message = "Illegal access to Spotify feed, do not open in browser.",
                    labels = mapOf("userId" to userId, "creatorId" to creatorId, "userAgent" to userAgent),
                    body = null,
                )
            }
        } else {
            if (!creator.hasRssFeed) {
                throw ForbiddenException(
                    "Creator $creatorId does not have RSS allowed.",
                    mapOf("userId" to userId, "creatorId" to creatorId),
                )
            }

            if (userId != creatorId && subscribersCollection.fetchActiveSubscription(userId, creatorId) == null) {
                throw ForbiddenException(
                    "User $userId does not subscribe $creatorId",
                    mapOf("userId" to userId, "creatorId" to creatorId),
                )
            }

            val rssFeed = rssFeedsCollections["$creatorId-$userId"].fetch()
                ?: throw ForbiddenException(
                    "User $userId does not have does not have generated rss feed access.",
                    mapOf("userId" to userId, "creatorId" to creatorId),
                )

            if (rssFeed.iat != iat) {
                throw ForbiddenException(
                    "User $userId has generated a new rss feed token",
                    mapOf("userId" to userId, "creatorId" to creatorId),
                )
            }

            if (userAgent !in rssFeed.clients!!) {
                // we log clients using this feed for additional security
                rssFeedsCollections["$creatorId-$userId"].field(RssFeed::clients).union(userAgent)
            }
        }

        return Pair(creator, isSpotify)
    }

    override fun contractRoutes() = listOf(routeRootGet)
}

fun parseToken(token: String): Claims? = parseKeyOrNull(token, heroPublicKey)

fun parseKeyOrNull(
    token: String,
    publicKey: PublicKey,
): Claims? =
    try {
        token.parseJwt(publicKey = publicKey)
    } catch (e: Exception) {
        null
    }

fun TypedCollectionReference<Subscriber>.fetchActiveSubscription(
    userId: String,
    creatorId: String,
) = this
    .where(Subscriber::userId).isEqualTo(userId)
    .and(Subscriber::creatorId).isEqualTo(creatorId)
    .and(Subscriber::status).isIn(SubscriberStatus.activeStatuses)
    .fetchSingle()
