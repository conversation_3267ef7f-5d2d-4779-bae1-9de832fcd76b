package hero.api.messages.controller

import hero.api.messages.controller.dto.CreateMessageRequest
import hero.api.messages.controller.dto.exampleCreateMessageRequest
import hero.api.messages.controller.dto.exampleMessageThreadDetailsResponse
import hero.api.messages.controller.dto.examplePaginatedMessageThreadResponse
import hero.api.messages.controller.dto.toResponse
import hero.api.messages.service.GetMessage
import hero.api.messages.service.GetMessageThread
import hero.api.messages.service.GetMessagesFromThread
import hero.api.messages.service.GetUsersActiveMessageThreads
import hero.api.messages.service.MessageCommandService
import hero.api.messages.service.MessageQueryService
import hero.api.messages.service.MessageThreadQueryService
import hero.api.messages.service.SendMessage
import hero.api.post.controller.dto.examplePaginatedPostResponse
import hero.api.post.controller.dto.examplePostResponse
import hero.api.post.controller.dto.toResponse
import hero.core.data.PageRequest
import hero.core.data.toResponse
import hero.http4k.auth.getJwtUser
import hero.http4k.controller.QueryUtils
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.model.PostRenderMeta
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path

class MessageThreadsController(
    private val messageThreadQueryService: MessageThreadQueryService,
    private val messageQueryService: MessageQueryService,
    private val messageCommandService: MessageCommandService,
) {
    @Suppress("Unused")
    val routeGetMessageThreads: ContractRoute =
        "/v2/message-threads".get(
            summary = "Lists message threads for currently authorized user.",
            tag = "Message threads",
            parameters = object {
                val pageSize = QueryUtils.pageSize()
                val afterCursor = QueryUtils.afterCursor()
            },
            responses = listOf(Status.OK example examplePaginatedMessageThreadResponse),
            handler = { request, parameters ->
                val jwtUser = request.getJwtUser()
                val pageSize = parameters.pageSize(request)
                val afterCursor = parameters.afterCursor(request)

                val pageable = PageRequest(pageSize = pageSize, afterCursor = afterCursor)
                val result = messageThreadQueryService.execute(GetUsersActiveMessageThreads(jwtUser.id, pageable))

                val response = result.toResponse { it.toResponse(jwtUser.id) }

                Response(Status.OK).body(response)
            },
        )

    @Suppress("Unused")
    val routeGetSingleMessageThread: ContractRoute =
        ("/v2/message-threads" / Path.of("messageThreadId")).get(
            summary = "Get message thread details such as `canPost`, `relation` and `commonCreators`",
            tag = "Message threads",
            parameters = object {},
            responses = listOf(Status.OK example exampleMessageThreadDetailsResponse),
            handler = { request, _, messageThreadId ->
                val jwtUser = request.getJwtUser()
                val result = messageThreadQueryService.execute(GetMessageThread(jwtUser.id, messageThreadId))

                Response(Status.OK).body(result.toResponse(jwtUser.id))
            },
        )

    @Suppress("Unused")
    val routeGetMessages: ContractRoute =
        ("/v2/message-threads" / Path.of("messageThreadId") / "messages").get(
            summary = "Get messages from the thread",
            tag = "Message threads",
            parameters = object {
                val pageSize = QueryUtils.pageSize()
                val afterCursor = QueryUtils.afterCursor()
            },
            responses = listOf(Status.OK example examplePaginatedPostResponse),
            handler = { request, parameters, messageThreadId, _ ->
                val jwtUser = request.getJwtUser()
                val pageSize = parameters.pageSize(request)
                val afterCursor = parameters.afterCursor(request)

                val pageable = PageRequest(pageSize = pageSize, afterCursor = afterCursor)
                val result = messageQueryService.execute(GetMessagesFromThread(jwtUser.id, messageThreadId, pageable))

                val response = result.toResponse { it.toResponse(jwtUser.id) }

                Response(Status.OK).body(response)
            },
        )

    @Suppress("Unused")
    val routePostMessages: ContractRoute =
        ("/v2/message-threads" / Path.of("messageThreadId") / "messages").post(
            summary = "Get messages from the thread",
            tag = "Message threads",
            parameters = object {
                val pageSize = QueryUtils.pageSize()
                val afterCursor = QueryUtils.afterCursor()
            },
            responses = listOf(Status.OK example examplePostResponse),
            receiving = exampleCreateMessageRequest,
            handler = { request, parameters, messageThreadId, _ ->
                val jwtUser = request.getJwtUser()
                val body = lens<CreateMessageRequest>(request)

                val result = messageCommandService.execute(
                    SendMessage(
                        userId = jwtUser.id,
                        messageThreadId = messageThreadId,
                        text = body.text,
                    ),
                )

                Response(Status.OK).body(result.toResponse(PostRenderMeta(fullResponse = true)))
            },
        )

    @Suppress("Unused")
    val routeGetMessage: ContractRoute =
        ("/v1/messages" / Path.of("messageId")).get(
            summary = "Get messages from the thread",
            tag = "Message threads",
            parameters = object {
                val pageSize = QueryUtils.pageSize()
                val afterCursor = QueryUtils.afterCursor()
            },
            responses = listOf(Status.OK example examplePostResponse),
            handler = { request, _, messageId ->
                val jwtUser = request.getJwtUser()

                val result = messageQueryService.execute(GetMessage(jwtUser.id, messageId))

                Response(Status.OK).body(result.toResponse(jwtUser.id))
            },
        )
}
