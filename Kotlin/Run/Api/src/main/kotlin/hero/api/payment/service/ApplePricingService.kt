package hero.api.payment.service

import hero.model.Currency
import hero.model.Tier
import java.math.BigDecimal
import java.math.RoundingMode

class ApplePricingService {
    // precise apple 15 % + VAT
    private val subscriptionCoef = (1.20).toBigDecimal()

    fun computeApplePriceCents(
        currency: Currency,
        tier: Tier,
    ): Int {
        val conversionCoef = when (currency) {
            Currency.CZK -> 25
            else -> 1
        }

        val priceInTargetCurrency = tier.priceCents.toBigDecimal()
            // cents to regular price
            .movePointLeft(2)
            .times(subscriptionCoef)
            .times(conversionCoef.toBigDecimal())

        return when (currency) {
            // for CZK, we round to nearest upper 9, eg. 143 -> 149
            Currency.CZK ->
                priceInTargetCurrency
                    .setScale(0, RoundingMode.CEILING).minus(BigDecimal(1))
            // for EUR, we subtract 1 cent
            Currency.EUR ->
                priceInTargetCurrency
                    .setScale(0, RoundingMode.CEILING).minus(BigDecimal("0.01"))
            else -> error("Unsupported currency: $currency")
        }.movePointRight(2).toInt()
    }
}
