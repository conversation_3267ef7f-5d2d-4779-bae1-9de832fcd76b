package hero.functions

import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

internal class MailgunPublisherTest {
    private val mailgunPublisher = MailgunPublisher()

    @Test
    fun `title is matched and extracted`() {
        val result = mailgunPublisher.subjectMatcher.find("<html><head><title>foobar</title>…………</html>")
        assertNotNull(result)
        assertEquals("foobar", result.groups[1]?.value)
    }

    @Test
    fun `title cannot be matched`() {
        val result = mailgunPublisher.subjectMatcher.find("<html><head><titl>foobar</titl>…………</html")
        assertNull(result)
    }

    @Test
    fun `cs subject is extracted`() {
        assertEquals(
            "Tvůj dárkový poukaz na Herohero – <PERSON>",
            mailgunPublisher.subject("coupon-paid", "devel-cs", mapOf("creator-name" to "<PERSON>")),
        )
    }

    @Test
    fun `en subject is extracted`() {
        assertEquals(
            "Your Herohero gift voucher – <PERSON><PERSON><PERSON>",
            mailgunPublisher.subject("coupon-paid", "devel-en", mapOf("creator-name" to "Voita Otevrel")),
        )
    }

    @Test
    fun `utm is correctly appended`() {
        val variables = listOf(
            "param" to "not url",
            "url hero1" to "https://devel.herohero.co/blah",
            "url hero2" to "https://staging.herohero.co/blahblah",
            "url hero3" to "https://herohero.co/blahblah",
            "url not hero" to "https://stripe.com/not",
        )
        val processedVariables = mailgunPublisher.mapUtmParams(variables, "new-sign-up")
        val utm = "utm_source=mailgun&utm_medium=email&utm_campaign=new-sign-up"
        assertEquals(
            mapOf(
                "param" to "not url",
                "url hero1" to "https://devel.herohero.co/blah?$utm",
                "url hero2" to "https://staging.herohero.co/blahblah?$utm",
                "url hero3" to "https://herohero.co/blahblah?$utm",
                "url not hero" to "https://stripe.com/not",
                "utm" to utm,
            ),
            processedVariables,
        )
    }
}
