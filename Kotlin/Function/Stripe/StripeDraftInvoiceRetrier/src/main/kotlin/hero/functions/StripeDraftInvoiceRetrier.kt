package hero.functions

import com.stripe.model.Invoice
import com.stripe.param.InvoiceListParams
import com.stripe.param.InvoiceUpdateParams
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.get
import hero.model.Currency
import hero.model.topics.Daily
import hero.stripe.model.StripeKeys
import hero.stripe.service.StripeClients
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExecutorCoroutineDispatcher
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import java.time.Instant
import java.util.concurrent.Executors

@Suppress("unused")
class StripeDraftInvoiceRetrier(
    production: Boolean = SystemEnv.isProduction,
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
) : PubSubSubscriber<Daily>() {
    private val stripeKeysRepository: TypedCollectionReference<StripeKeys> =
        TypedCollectionReference<StripeKeys>(firestore.firestore["constants"])

    private val keysEu: StripeKeys = stripeKeysRepository["${production.envPrefix}-stripe-eu"].get()
    private val keysUs: StripeKeys = stripeKeysRepository["${production.envPrefix}-stripe-us"].get()
    private val stripeClients: StripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val invalidCustomers = mutableSetOf<String>()
    private val invalidAccounts = mutableSetOf<String>()

    override fun consume(payload: Daily) {
        val executor = Executors.newFixedThreadPool(16).asCoroutineDispatcher()
        executor.use {
            runBlocking(it) {
                handleDraftInvoices(Currency.EUR, executor)
                // TODO uncomment once we have US Stripe account fully active
                // handleDraftInvoices(Currency.USD, executor)
            }
        }
        log.info("Finished processing draft invoices.")
    }

    private suspend fun handleDraftInvoices(
        currency: Currency,
        executor: ExecutorCoroutineDispatcher,
    ) {
        val invoices = stripeClients[currency].invoices()
        val scope = CoroutineScope(executor)

        invoices
            .list(
                InvoiceListParams.builder()
                    .setStatus(InvoiceListParams.Status.DRAFT)
                    .setCreated(
                        // we ignore old draft invoices so that customers are not too surprised
                        InvoiceListParams.Created.builder().setGte(Instant.now().minusDays(21).epochSecond).build(),
                    )
                    .addExpand("data.customer")
                    .addExpand("data.subscription")
                    .build(),
            )
            .autoPagingIterable()
            .asSequence()
            .filter { !it.autoAdvance }
            .filter { it.subscriptionObject.status == "active" }
            .map { invoice ->
                scope.async {
                    processDraftInvoice(invoice)
                }
            }
            .toList()
            .awaitAll()
    }

    private fun processDraftInvoice(invoice: Invoice) {
        val accountId = invoice.transferData.destination
        if (accountId in invalidAccounts) {
            // skipping
            return
        }
        val account = accountId?.let { stripeClients[invoice.currency].accounts().retrieve(it) }
        when {
            invoice.customer in invalidCustomers -> {
                // already processed, skipping
            }
            invoice.customerObject.deleted == true -> {
                // customer deleted, skipping
                invalidCustomers += invoice.customer
            }
            invoice.subscriptionObject.status != "active" -> {
                // subscription ended, skipping
            }
            account == null -> {
                log.fatal("Invoice ${invoice.id} does not have destination account.")
            }
            account.deleted == true -> {
                // account deleted
                invalidAccounts += accountId
            }
            !account.requirements.disabledReason.isNullOrBlank() -> {
                // account disabled for some reason
                invalidAccounts += accountId
            }
            else -> {
                log.info(
                    "Re-enabling Automatic collection on ${invoice.id} of ${invoice.subscription}/$accountId.",
                )
                invoice.update(InvoiceUpdateParams.builder().setAutoAdvance(true).build())
            }
        }
    }
}
