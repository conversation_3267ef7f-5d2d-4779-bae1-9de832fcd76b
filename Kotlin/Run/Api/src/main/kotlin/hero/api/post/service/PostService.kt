package hero.api.post.service

import hero.api.post.service.dto.ImageAssetInput
import hero.api.post.service.dto.PostAssetInput
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.md5nice
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gjirafa.GjirafaLivestreamsService
import hero.gjirafa.GjirafaUploadsService
import hero.gjirafa.dto.toDomainStatus
import hero.model.Chapter
import hero.model.GjirafaLiveAsset
import hero.model.GjirafaStatus.COMPLETE
import hero.model.GjirafaStatus.PARTIALLY_COMPLETED
import hero.model.ImageAsset
import hero.model.Post
import hero.model.PostAsset
import hero.model.topics.PostState.DELETED
import hero.model.topics.PostState.PROCESSING
import hero.model.topics.PostState.PUBLISHED
import hero.model.topics.PostState.REVISION
import hero.model.topics.PostState.SCHEDULED
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.repository.post.PostRepository
import hero.sql.jooq.Tables
import java.time.Instant

class PostService(
    private val postsCollection: TypedCollectionReference<Post>,
    private val postRepository: PostRepository,
    private val gjirafaService: GjirafaUploadsService,
    private val gjirafaLivestreamService: GjirafaLivestreamsService,
    private val pubSub: PubSub,
) {
    /**
     * This factory method creates generic Post entity. This function should be called only from use cases, such as
     * [CreateComment], etc. This method does only basic validations (such as either text or assets are set),
     * specific validations should be done for given use case.
     */
    fun execute(command: CreatePost): Post {
        validate(command)
        val (assets, assetsReady) = processAssets(command.assets, command.userId)
        val state = when {
            !assetsReady -> PROCESSING
            command.publishedAt.isAfter(Instant.now()) -> SCHEDULED
            else -> PUBLISHED
        }

        val id = generateId(
            messageThreadId = command.messageThreadId,
            parentId = command.parentId,
            text = command.text,
            userId = command.userId,
        )

        if (assets.any { it.isEmpty() }) {
            log.fatal("One of assets was empty for post $id: $assets", mapOf("userId" to command.userId))
        }

        val now = Instant.now()
        val post = Post(
            id = id,
            userId = command.userId,
            parentId = command.parentId,
            siblingId = command.siblingId,
            messageThreadId = command.messageThreadId,
            parentUserId = command.parentUserId,
            parentPostId = command.parentPostId,
            published = command.publishedAt,
            pinnedAt = command.pinnedAt,
            updated = now,
            isSponsored = command.isSponsored,
            isAgeRestricted = command.isAgeRestricted,
            created = now,
            state = state,
            text = command.text.trim(),
            textHtml = command.textHtml?.let { htmlSanitizer.sanitize(it).trim() },
            textDelta = command.textDelta?.trim(),
            excludeFromRss = command.excludeFromRss,
            assets = assets,
            assetIds = assets.mapNotNull { it.gjirafa?.id ?: it.gjirafaLive?.id },
            assetStates = assets.mapNotNull { it.gjirafa?.status }.distinct(),
            price = command.price,
            categories = command.categories.toList(),
            participingUserIds = command.participatingUserIds,
            chapters = command.chapters,
        ).also {
            postsCollection[it.id].set(it)
            postRepository.save(it)
        }

        if (post.state in publishableStates) {
            pubSub.publish(PostStateChanged(PostStateChange.PUBLISHED, post))
        }

        return post
    }

    fun execute(command: UpdatePost): Post {
        val post = postsCollection[command.postId].get().apply {
            validate(command, this)
            command.postValidator(this)
            validateBeforeUpdate(this, command.userId)
        }

        val publishedAt = command.publishedAt ?: post.published
        val (assets, assetsReady) = processAssets(command.assets, command.userId)
        val state = when {
            // we currently never unpublish posts, might want to change in the future
            post.state == PUBLISHED -> PUBLISHED
            !assetsReady -> PROCESSING
            publishedAt.isAfter(Instant.now()) && post.state == PUBLISHED && assetsReady -> SCHEDULED
            else -> post.state
        }

        val updatedPost = post.copy(
            updated = Instant.now(),
            text = htmlCleaner.sanitize(command.text).trim(),
            textHtml = htmlSanitizer.sanitize(command.textHtml).trim(),
            textDelta = command.textDelta?.trim(),
            published = publishedAt,
            state = state,
            price = command.price,
            pinnedAt = command.pinnedAt,
            categories = command.categories,
            excludeFromRss = command.excludeFromRss,
            assets = assets,
            assetIds = assets.mapNotNull { it.gjirafa?.id ?: it.gjirafaLive?.id },
            assetStates = assets.mapNotNull { it.gjirafa?.status }.distinct(),
            chapters = command.chapters,
            isSponsored = command.isSponsored,
            isAgeRestricted = command.isAgeRestricted,
        )

        postsCollection[post.id].set(updatedPost)
        postRepository.save(updatedPost)
        if (post.state in publishableStates) {
            pubSub.publish(PostStateChanged(PostStateChange.PATCHED, updatedPost))
        }

        return updatedPost
    }

    private fun processAssets(
        assets: List<PostAssetInput>,
        creatorId: String,
    ): Pair<List<PostAsset>, Boolean> {
        val mappedAssets = assets.map { processAsset(it, creatorId) }
        val readyStates = setOf(COMPLETE, PARTIALLY_COMPLETED)
        val gjirafaReady = mappedAssets
            .mapNotNull { it.gjirafa }
            .all { it.status in readyStates }

        return mappedAssets to gjirafaReady
    }

    private fun validateBeforeUpdate(
        post: Post,
        userId: String,
    ) {
        val labels = mapOf("userId" to userId, "postId" to post.id)
        if (post.state == DELETED || post.state == REVISION) {
            throw BadRequestException("Cannot patch deleted posts", labels)
        }

        if (post.userId != userId) {
            throw ForbiddenException("Only post owner can update his posts", labels)
        }
    }

    private fun validate(command: CreatePost) {
        validate(command.text, command.assets, command.userId)
    }

    private fun validate(
        command: UpdatePost,
        post: Post,
    ) {
        validate(command.text, command.assets, command.userId)
        // we allow pinning only three pinned posts
        if (!SystemEnv.isProduction && command.pinnedAt != null && post.pinnedAt == null) {
            val pinnedPosts = postRepository.find {
                this
                    .where(Tables.POST.USER_ID.eq(post.userId))
                    .and(Tables.POST.PINNED_AT.isNotNull)
            }

            if (pinnedPosts.size >= 3) {
                throw BadRequestException("Only three pinned posts are allowed.")
            }
        }
    }

    private fun validate(
        text: String,
        assets: List<PostAssetInput>,
        userId: String,
    ) {
        val labels = mapOf("userId" to userId)
        if (text.isBlank() && assets.isEmpty()) {
            throw BadRequestException("Either text or some assets must be given to a post", labels)
        }
    }

    private fun processAsset(
        assetDto: PostAssetInput,
        creatorId: String,
    ): PostAsset {
        return PostAsset(
            image = assetDto.image?.toImageAsset(),
            gjirafa = assetDto.gjirafa?.let {
                gjirafaService.getAsset(userId = creatorId, assetId = it.id)
            },
            thumbnail = assetDto.thumbnail,
            thumbnailImage = assetDto.thumbnailImage?.toImageAsset(),
            document = assetDto.document,
            gjirafaLive = assetDto.gjirafaLivestream?.let {
                val response = gjirafaLivestreamService.getLiveVideo(it.id)

                GjirafaLiveAsset(
                    id = it.id,
                    playbackUrl = response.playbackUrl,
                    channelPublicId = response.channelPublicId,
                    liveStatus = response.liveStatus.toDomainStatus(),
                )
            },
        )
    }

    private fun ImageAssetInput.toImageAsset() = ImageAsset.of(id = url, width = width, height = height)

    private fun generateId(
        messageThreadId: String?,
        parentId: String?,
        text: String,
        userId: String,
    ): String {
        val currentTimeMillis = System.currentTimeMillis()
        return if (messageThreadId != null) {
            "$messageThreadId-$currentTimeMillis-${(userId + text).md5nice()}"
        } else if (parentId != null) {
            "$parentId-${(userId + currentTimeMillis).md5nice().take(HASH_LENGTH)}"
        } else {
            userId + (text + userId + currentTimeMillis).md5nice()
        }
    }
}

data class CreatePost(
    val userId: String,
    val parentId: String? = null,
    val siblingId: String? = null,
    val parentUserId: String? = null,
    val parentPostId: String? = null,
    val messageThreadId: String? = null,
    val publishedAt: Instant,
    val pinnedAt: Instant? = null,
    val text: String,
    val textHtml: String?,
    val textDelta: String?,
    val isSponsored: Boolean,
    val isAgeRestricted: Boolean,
    val assets: List<PostAssetInput>,
    val price: Long? = null,
    val excludeFromRss: Boolean = false,
    val categories: Set<String> = setOf(),
    val participatingUserIds: List<String> = emptyList(),
    val chapters: List<Chapter> = emptyList(),
)

data class UpdatePost(
    val postId: String,
    val userId: String,
    val publishedAt: Instant? = null,
    val pinnedAt: Instant? = null,
    val text: String,
    val textHtml: String?,
    val textDelta: String?,
    val isSponsored: Boolean,
    val isAgeRestricted: Boolean,
    val assets: List<PostAssetInput>,
    val price: Long? = null,
    val excludeFromRss: Boolean = false,
    val categories: List<String> = emptyList(),
    val chapters: List<Chapter> = emptyList(),
    val postValidator: (Post) -> Unit = {},
)

private const val HASH_LENGTH = 8

private val publishableStates = setOf(PUBLISHED)
