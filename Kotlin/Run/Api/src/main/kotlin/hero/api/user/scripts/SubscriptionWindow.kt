package hero.api.user.scripts

import hero.baseutils.SystemEnv
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Subscriber
import hero.model.User
import java.time.Duration
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)
    val formatter = DateTimeFormatter.ofPattern("uuuu-MM-dd")
    val zoneId = ZoneId.of("Europe/Prague")

    fun Instant.format() = formatter.format(this.atZone(zoneId))

    val userCollection = firestore.typedCollectionOf(User)

    fun fetchCreated(creatorId: String): Instant = userCollection[creatorId].get().created

    firestore.typedCollectionOf(Subscriber)
        .fetchAll()
        .map { it.creatorId to it }
        .groupBy({ it.first }, { it.second })
        .entries
        .map { (creatorId, subscriptions) ->
            CreatorStat(
                creatorId = creatorId,
                createdAt = fetchCreated(creatorId),
                subscribedAt = subscriptions.minOf { it.subscribed },
                lastSubscribedAt = subscriptions.maxOf { it.subscribed },
                totalSubscribers = subscriptions.size,
                activeSubscribers = subscriptions.filter { it.status.isActive }.size,
            )
        }
        .sortedBy { it.diff }
        .map {
            println(
                "${it.creatorId};${it.diff};${it.createdAt.format()};${it.subscribedAt.format()};" +
                    "${it.lastSubscribedAt.format()};${it.totalSubscribers};${it.activeSubscribers}",
            )
        }
}

data class CreatorStat(
    val creatorId: String,
    val createdAt: Instant,
    val subscribedAt: Instant,
    val lastSubscribedAt: Instant,
    val totalSubscribers: Int,
    val activeSubscribers: Int,
) {
    val diff: Long
        get() = Duration.between(createdAt, subscribedAt).abs().toDays()
}
