package hero.api.category.repository

import hero.baseutils.md5nice
import hero.baseutils.webalize
import hero.core.data.Sort
import hero.exceptions.http.ConflictException
import hero.exceptions.http.NotFoundException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.remove
import hero.gcloud.where
import hero.jwt.JwtUser
import hero.model.Category
import hero.model.CategoryDto
import hero.model.CategoryDtoAttributes
import hero.model.CategoryDtoRelationship
import hero.model.CategoryDtoRelationships
import hero.model.Post
import hero.model.UserDtoRelationship
import java.time.Instant
import kotlin.concurrent.thread

class CategoriesRepository(
    private val collection: TypedCollectionReference<Category>,
    private val postsCollection: TypedCollectionReference<Post>,
) {
    fun list(userId: String): List<CategoryDto> =
        collection
            .where(Category::userId).isEqualTo(userId)
            .orderBy(Category::index, Sort.Direction.ASC)
            .fetchAll()
            .map { toDto(it) }

    fun get(id: String): CategoryDto? =
        collection[id].fetch()
            ?.let(::toDto)

    fun post(categoryDto: CategoryDto): CategoryDto {
        val category = Category(
            id = categoryDto.relationships.user.id + "-" + categoryDto.attributes.name.md5nice(),
            name = categoryDto.attributes.name,
            createdAt = Instant.now(),
            userId = categoryDto.relationships.user.id,
            slug = categoryDto.attributes.name.webalize(),
        )
        collection[category.id].set(category)
        return categoryDto.copy(
            id = category.id,
            attributes = categoryDto.attributes.copy(
                slug = category.slug,
                createdAt = category.createdAt,
                postCount = category.postCount,
            ),
        )
    }

    fun patch(categoryDto: CategoryDto): CategoryDto {
        val slug = categoryDto.attributes.name.webalize()
        collection[categoryDto.id!!].field(Category::name).update(categoryDto.attributes.name)
        collection[categoryDto.id!!].field(Category::slug).update(slug)
        return categoryDto.copy(
            attributes = categoryDto.attributes.copy(slug = categoryDto.attributes.name.webalize()),
        )
    }

    fun delete(categoryId: String) {
        collection[categoryId].delete()
        thread {
            postsCollection.where(Post::categories).contains(categoryId)
                .fetchAll()
                .forEach {
                    postsCollection[it.id].field(Post::categories).remove(categoryId)
                }
        }
    }

    private fun toDto(category: Category): CategoryDto =
        CategoryDto(
            id = category.id,
            attributes = CategoryDtoAttributes(
                name = category.name,
                slug = category.slug,
                createdAt = category.createdAt,
                postCount = category.postCount,
            ),
            relationships = CategoryDtoRelationships(
                user = UserDtoRelationship(category.userId),
            ),
        )

    fun order(
        jwtUser: JwtUser,
        categories: List<CategoryDtoRelationship>,
    ) {
        categories
            // iterate over users' categories and load them from repository
            .map { categoryDtoRelationship ->
                get(categoryDtoRelationship.id)
                    ?: throw NotFoundException("Category ${categoryDtoRelationship.id} was not found.")
            }
            // iterate over categories and check if they belong to user
            .onEach { categoryDto ->
                if (categoryDto.relationships.user.id != jwtUser.id) {
                    throw ConflictException("Category ${categoryDto.id} does not belong to ${jwtUser.id}.")
                }
            }
            // iterate over categories and update order
            .forEachIndexed { index, categoryDto ->
                collection[categoryDto.id!!].field(Category::index).update(index)
            }
    }
}
