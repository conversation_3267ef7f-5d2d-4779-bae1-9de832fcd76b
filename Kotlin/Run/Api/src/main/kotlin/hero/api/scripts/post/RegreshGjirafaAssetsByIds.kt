package hero.api.scripts.post

import hero.baseutils.SystemEnv
import hero.baseutils.systemEnv
import hero.gcloud.containsAny
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.gjirafa.GjirafaUploadsService
import hero.model.Post

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)
    val postsCollection = firestore.typedCollectionOf(Post)

    // WARN: these keys are relevant only for production, otherwise, remove the _PROD
    val gjirafaService = GjirafaUploadsService(
        projectId = systemEnv("GJIRAFA_PROJECT_PROD"),
        apiKey = systemEnv("GJIRAFA_API_KEY_PROD"),
        imageKey = systemEnv("GJIRAFA_IMAGE_KEY_PROD"),
    )

    val idsToCheck =
        """
        mikeoganesjandnqgmopjgplcgclkgthrrxea
        """
            .split("\\s+".toRegex())
            .filter { it.isNotBlank() }

    val postsByAssetId = postsCollection
        .where(Post::assetIds).containsAny(idsToCheck)
        .fetchAll()

    val postsById = postsCollection
        .where(Post::id).isIn(idsToCheck)
        .fetchAll()

    val postsByUser = if (idsToCheck.size == 1)
        postsCollection
            .where(Post::userId).isEqualTo(idsToCheck.first())
            .fetchAll()
    else
        emptyList()

    (postsByAssetId + postsById + postsByUser)
        .distinct()
        .map { post ->
            refreshGjirafaAssetsInPost(post, gjirafaService, postsCollection)
        }
}
