package hero.api.user.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.toYearMonth
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.jackson.toJson
import hero.model.Subscriber
import java.time.Instant

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)

    val creatorId = "davidvanicekaauyustx"

    firestore.typedCollectionOf(Subscriber)
        .where(Subscriber::creatorId).isEqualTo(creatorId)
        .fetchAll()
        .filter { it.expires == null || it.expires!! < Instant.now() || it.cancelAtPeriodEnd }
        .map {
            it.expires?.toYearMonth() to it.userId
        }
        .groupBy({ it.first ?: "never" }, { it.second })
        .let { println(it.toJson()) }
}
