package hero.api.watch.service

import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.model.PostAssetType
import hero.model.topics.PostState
import hero.repository.post.PostRepository
import hero.repository.post.PostType
import hero.repository.subscription.JooqSubscriptionHelper
import hero.sql.jooq.Tables.POST
import hero.sql.jooq.Tables.POST_ASSET
import hero.sql.jooq.Tables.SUBSCRIPTION
import hero.sql.jooq.Tables.WATCH_ACTIVITY
import org.jooq.DSLContext
import org.jooq.impl.DSL
import java.time.Clock
import java.time.Instant

class WatchActivityCommandService(
    lazyContext: Lazy<DSLContext>,
    private val postRepository: PostRepository,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context by lazyContext

    fun execute(command: RecordWatchActivity) {
        if (command.timestamp <= 0.0) {
            return
        }

        val post = if (command.postId != null) {
            val post = postRepository.getById(command.postId)
            if (command.assetId !in post.assets.mapNotNull { asset -> asset.gjirafa?.id }) {
                // creator can edit a post while user is watching, without this check
                // the bad request exception would be thrown.
                // this check will be easier when we have diffs on the post entity
                if (findPostByAssetId(command.assetId, PostState.REVISION) != null) {
                    return
                } else {
                    throw BadRequestException("Asset ${command.assetId} is not part of ${command.postId}")
                }
            }
            post
        } else {
            findPostByAssetId(command.assetId, PostState.PUBLISHED)
                ?: throw BadRequestException("No post with asset ${command.assetId} was found")
        }

        val userId = command.userId
        val creatorId = post.userId
        val subscription = context.selectFrom(SUBSCRIPTION)
            .where(SUBSCRIPTION.USER_ID.eq(userId).and(SUBSCRIPTION.CREATOR_ID.eq(creatorId)))
            .and(JooqSubscriptionHelper.activeSubscription)
            .fetch()

        if (userId != creatorId && subscription.isEmpty()) {
            throw ForbiddenException("User $userId does not subscribe $creatorId")
        }

        val asset = post.assets.mapNotNull { it.gjirafa }.first { it.id == command.assetId }

        // clients can send a timestamp that is a little lower than the asset duration, due to video player
        // or other reasons, we still should mark the asset as finished in that case
        val finished = (command.timestamp + TIMESTAMP_ROUNDING_EPSILON) >= asset.duration

        val now = Instant.now(clock)
        if (finished) {
            context
                .update(WATCH_ACTIVITY)
                .set(WATCH_ACTIVITY.FINISHED, true)
                .set(WATCH_ACTIVITY.TIMESTAMP, command.timestamp)
                .set(WATCH_ACTIVITY.WATCHED_AT, now)
                .where(WATCH_ACTIVITY.USER_ID.eq(userId))
                .and(WATCH_ACTIVITY.POST_ID.eq(post.id))
                .and(WATCH_ACTIVITY.ASSET_ID.eq(command.assetId))
                .execute()
        } else {
            context
                .insertInto(WATCH_ACTIVITY)
                .set(WATCH_ACTIVITY.USER_ID, userId)
                .set(WATCH_ACTIVITY.POST_ID, post.id)
                .set(WATCH_ACTIVITY.CREATOR_ID, creatorId)
                .set(WATCH_ACTIVITY.SUBSCRIPTION_ACTIVE, true)
                .set(WATCH_ACTIVITY.ASSET_ID, command.assetId)
                .set(WATCH_ACTIVITY.SESSION_ID, command.sessionId)
                .set(WATCH_ACTIVITY.TIMESTAMP, command.timestamp)
                .set(WATCH_ACTIVITY.WATCHED_AT, now)
                .set(WATCH_ACTIVITY.CREATED_AT, now)
                .set(WATCH_ACTIVITY.FINISHED, false)
                // one gjirafa video on multiple posts is kinda unwieldy
                .onConflict(WATCH_ACTIVITY.USER_ID, WATCH_ACTIVITY.POST_ID, WATCH_ACTIVITY.ASSET_ID)
                .where(WATCH_ACTIVITY.FINISHED.isFalse.and(WATCH_ACTIVITY.DELETED_AT.isNull))
                .doUpdate()
                .set(WATCH_ACTIVITY.SESSION_ID, command.sessionId)
                .set(WATCH_ACTIVITY.SUBSCRIPTION_ACTIVE, true)
                .set(WATCH_ACTIVITY.TIMESTAMP, command.timestamp)
                .set(WATCH_ACTIVITY.WATCHED_AT, now)
                .execute()
        }
    }

    fun execute(command: RemoveWatchActivity) {
        context
            .update(WATCH_ACTIVITY)
            .set(WATCH_ACTIVITY.DELETED_AT, Instant.now(clock))
            .where(WATCH_ACTIVITY.USER_ID.eq(command.userId))
            .and(WATCH_ACTIVITY.POST_ID.eq(command.postId))
            .and(WATCH_ACTIVITY.ASSET_ID.eq(command.assetId))
            .and(WATCH_ACTIVITY.FINISHED.isFalse)
            .execute()

        return
    }

    private fun findPostByAssetId(
        assetId: String,
        state: PostState,
    ) = postRepository.findSingle {
        this
            .join(POST_ASSET).on(POST_ASSET.POST_ID.eq(POST.ID))
            .and(POST_ASSET.ASSET_TYPE.eq(PostAssetType.GJIRAFA.name))
            .where(POST.TYPE.eq(PostType.CONTENT_POST.name))
            .and(POST.STATE.eq(state.name))
            .and(DSL.jsonbGetAttributeAsText(POST_ASSET.METADATA, "id").eq(assetId))
            .limit(1)
    }
}

data class RemoveWatchActivity(
    val userId: String,
    val assetId: String,
    val postId: String,
)

data class RecordWatchActivity(
    val userId: String,
    val sessionId: String,
    val assetId: String,
    val postId: String?,
    val timestamp: Double,
)

private const val TIMESTAMP_ROUNDING_EPSILON = 2
