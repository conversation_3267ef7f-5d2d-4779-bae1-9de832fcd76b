package hero.functions

import com.google.cloud.functions.CloudEventsFunction
import com.google.cloud.logging.Synchronicity
import com.google.events.cloud.firestore.v1.DocumentEventData
import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.retryOn
import io.cloudevents.CloudEvent
import io.sentry.Sentry
import java.io.IOException
import java.time.Duration
import java.time.Instant
import java.util.concurrent.ExecutionException

// https://cloud.google.com/functions/docs/calling/cloud-firestore
abstract class FirestoreEventSubcriber(
    environmentVariables: EnvironmentVariables = SystemEnv,
    private val retryable: Boolean = false,
) : CloudEventsFunction {
    init {
        // https://github.com/googleapis/java-logging/issues/432
        if (environmentVariables.environment != "test") {
            log.synchronicity = Synchronicity.SYNC
            log.notice("Consumer of firestore events is being started.")
        }
    }

    override fun accept(event: CloudEvent) {
        if (event.data == null) {
            log.fatal("Received empty cloud event")
            return
        }
        val eventData = DocumentEventData.parseFrom(event.data?.toBytes())
        val id = eventData.value.fieldsMap["id"]?.stringValue
        val oldId = eventData.oldValue.fieldsMap["id"]?.stringValue

        if (id == null && oldId == null) {
            log.fatal("Id was null for entity result $eventData")
            return
        }

        val now = Instant.now()
        val seconds = Duration.between(event.time?.toInstant() ?: now, now).seconds
        if (retryable && seconds > 3600) {
            log.fatal("Not retrying event $id anymore, has been failing for $seconds seconds")
            Sentry.configureScope {
                it.setContexts("Now timestamp", now)
                Sentry.captureMessage("Failed to consume $id")
            }

            return
        }

        try {
            retryOn(IOException::class, ExecutionException::class) {
                val documentId = id ?: oldId ?: error("Both ids were null, should never happen")
                consume(FirestoreEvent(documentId, id == null))
            }
        } catch (e: Throwable) {
            if (retryable) {
                log.fatal(
                    "Failed to consume firestore event for $id: ${e.message}, but will retry",
                    cause = e,
                )
                throw e
            } else {
                log.fatal("Failed to consume firestore event for $id: ${e.message}", cause = e)
            }
        }
    }

    abstract fun consume(event: FirestoreEvent)
}

data class FirestoreEvent(val documentId: String, val wasDeleted: Boolean = false)
