package hero.auth.controller

import com.github.kittinunf.fuel.httpPost
import dev.forkhandles.result4k.get
import hero.auth.model.AccessTokenDetails
import hero.auth.model.DummyOAuthTokenPersistence
import hero.baseutils.escapeUrl
import hero.baseutils.fetch
import hero.baseutils.isUrlValid
import hero.baseutils.log
import hero.baseutils.nullIfEmpty
import hero.baseutils.retryOn
import hero.baseutils.serviceCall
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.HttpStatusException
import hero.exceptions.http.UnauthorizedException
import hero.http4k.auth.parseJwtUser
import hero.http4k.auth.tokenExpirations
import hero.http4k.extensions.ErrorResponse
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.post
import hero.jackson.map
import hero.jackson.to
import hero.jwt.JwtUser
import hero.jwt.parseJwt
import hero.jwt.toJwt
import hero.model.ExtractedUser
import hero.model.HEROHERO_DOMAIN_PATTERN
import hero.model.SealedOAuthCredentials
import hero.model.UserIdResponse
import hero.model.UserStateChange
import io.jsonwebtoken.Claims
import io.jsonwebtoken.JwtException
import org.http4k.client.Fuel
import org.http4k.contract.ContractRoute
import org.http4k.core.Credentials
import org.http4k.core.HttpHandler
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.core.Uri
import org.http4k.core.cookie.cookie
import org.http4k.lens.Query
import org.http4k.lens.string
import org.http4k.security.AccessTokenFetcher
import org.http4k.security.OAuthCallbackError.CouldNotFetchAccessToken
import org.http4k.security.OAuthPersistence
import org.http4k.security.OAuthProvider
import java.io.IOException
import java.time.Duration
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible

abstract class OAuthController(
    protected val hostnameServices: String,
    protected val hostname: String,
    protected val servicePath: String,
    private val oAuthCredentials: SealedOAuthCredentials,
    protected val service: String,
    protected val permissions: String? = null,
    protected val domainPattern: String = HEROHERO_DOMAIN_PATTERN,
) {
    abstract fun provide(
        client: HttpHandler,
        credentials: Credentials,
        tokenPersistence: OAuthPersistence,
        callbackUri: Uri,
    ): OAuthProvider

    /** returns extracted user and redirectUrl for callback */
    protected open fun handleAccessTokenInternal(
        tokensInJwt: String,
        request: Request,
    ): Pair<ExtractedUser, String?> {
        val tokenDetailsMap = tokensInJwt.parseJwt()
        // https://github.com/http4k/http4k/issues/787
        return handleAccessToken(
            tokenDetailsMap.to<AccessTokenDetails>(),
            extractSecondaryInfoInFinish(tokenDetailsMap),
            request,
        )
    }

    /** returns extracted user and redirectUrl for callback */
    abstract fun handleAccessToken(
        tokenDetails: AccessTokenDetails,
        tokenDetailsMap: Map<String, Any?>,
        request: Request,
    ): Pair<ExtractedUser, String?>

    // note that none of the links below contain the path of /auth - `service` here is google|facebook|...
    protected val initUrl: String = "/$service/v1/init"
    private val callbackUrl: String = "/$service/v1/code"
    private val tokensUrl: String = "/$service/v1/tokens"

    protected val connectUrl: String = "/$service/v1/connection-links"
    private val routeFinishAuthParameters = object {
        val tokenDetails = Query.string().optional("tokens", "OAuth tokens")
    }

    protected val provider: OAuthProvider
        get() = provide(
            httpClient,
            Credentials(oAuthCredentials.appId, oAuthCredentials.appSecret),
            DummyOAuthTokenPersistence(),
            Uri.of("$hostnameServices$servicePath$callbackUrl"),
        )

    protected open fun extractSecondaryInfoInCode(request: Request): Map<String, Any?> =
        request
            .query("state")
            ?.nullIfEmpty()
            ?.parseJwt()
            ?.let {
                mapOf(
                    "userId" to it["userId"],
                    "redirectUrl" to it["redirectUrl"],
                )
            }
            ?: emptyMap()

    protected open fun extractSecondaryInfoInFinish(payload: Claims): Map<String, Any?> =
        mapOf(
            "redirectUrl" to payload["redirectUrl"],
            "userId" to payload["userId"],
        )

    protected open fun notifyProvider(response: UserIdResponse) {}

    @Suppress("unused")
    open val routeInitAuth: ContractRoute
        get() = initUrl.get(
            summary = "Initiates the $service/OAuth dance and redirects to the provider's gateway.",
            tag = service.capitalize(),
            parameters = object {
                val redirectUrl = Query.string().optional("redirectUrl", "Url to redirect after OAuth dance finish.")
            },
            responses = listOf(Status.FOUND to ""),
            handler = { request, parameters ->
                val redirectUrl = parameters.redirectUrl(request)
                if (redirectUrl?.isUrlValid(domainPattern) == false) {
                    throw BadRequestException("Redirect URL $redirectUrl is invalid.")
                }
                val jwt = redirectUrl?.let { mapOf("redirectUrl" to redirectUrl).toJwt() }
                // I tried to implement some referer dancing with authInitUri to make it work
                // on subdomains like `preview.herohero.co`, but then it gets broken in the
                // code->accessToken part anyway. This doesn't lead anywhere, and we must test it
                // only on regular devel/staging/prod domains.
                Response(Status.FOUND).header(
                    "Location",
                    authInitUri(callbackUrl = provider.extractCallbackUrl(), state = jwt),
                )
            },
        )

    @Suppress("unused")
    open val routeConnectAuth: ContractRoute
        get() = connectUrl.post(
            summary = "Initiates the $service/OAuth dance and returns a link for the user to be redirected. " +
                "In comparison with /init endpoint, this requires authorization and " +
                "returns an object instead of redirect.",
            tag = service.capitalize(),
            parameters = object {
                val redirectUrl = Query.string().optional("redirectUrl", "Url to redirect after OAuth dance finish.")
            },
            responses = listOf(Status.OK to ConnectionResponse(url = "https://url-to-redirect")),
            receiving = null,
            handler = { request, parameters ->
                val redirectUrl = parameters.redirectUrl(request)
                if (redirectUrl?.isUrlValid(domainPattern) == false) {
                    throw BadRequestException("Redirect URL $redirectUrl is invalid.")
                }
                val userId = request.parseJwtUser()?.id ?: throw UnauthorizedException()
                val jwt = mapOf("userId" to userId, "redirectUrl" to redirectUrl).toJwt()
                Response(Status.OK).body(
                    ConnectionResponse(
                        url = authInitUri(
                            callbackUrl = provider.extractCallbackUrl(),
                            state = jwt,
                        ),
                    ),
                )
            },
        )

    @Suppress("unused")
    open val routeReturnCallback: ContractRoute
        get() = callbackUrl.get(
            summary = "Handles responding $service/OAuth code.",
            tag = service.capitalize(),
            parameters = object {
                val oAuthCode = oAuthCodeParameter
            },
            responses = listOf(
                Status.BAD_REQUEST to ErrorResponse("Either code or accessToken must be given."),
                Status.FOUND to "",
            ),
            handler = { request, parameters ->
                val tokenDetails = parameters
                    .oAuthCode(request)
                    ?.let {
                        retryOn(IOException::class) {
                            accessTokenFetcher.fetch(it).get()
                        }
                    }

                val queryString = when (tokenDetails) {
                    null ->
                        "?error=true"

                    is CouldNotFetchAccessToken -> {
                        log.error("Cannot fetch accessToken: ${tokenDetails.reason}")
                        "?error=true"
                    }

                    else -> {
                        val tokens = tokenDetails
                            .map()
                            .plus(extractSecondaryInfoInCode(request))
                            .toJwt()
                            .escapeUrl()
                        "?tokens=$tokens"
                    }
                }
                Response(Status.FOUND).header("Location", "$servicePath$tokensUrl$queryString")
            },
        )

    @Suppress("unused")
    val routeTokens: ContractRoute
        get() = tokensUrl.get(
            summary = "Called on successful $service/OAuth authorization and posts to " +
                "/api/v1/users with signed extracted user details.",
            tag = service.capitalize(),
            parameters = routeFinishAuthParameters,
            responses = listOf(
                Status.FOUND example Unit,
            ),
            handler = { request, parameters ->
                fun respond(
                    status: Status,
                    userId: String? = null,
                    userState: UserStateChange? = null,
                ): Response =
                    Response(Status.FOUND)
                        .header(
                            "Location",
                            "$hostname/oauth-callback" +
                                "?status=${status.code}" +
                                "&service=$service" +
                                "&message=${status.description.escapeUrl()}" +
                                (if (userId != null) "&userId=$userId" else "") +
                                (if (userState != null) "&state=$userState" else ""),
                        )
                try {
                    val tokensInJwt = parameters.tokenDetails(request)
                        ?: throw UnauthorizedException("Access token could not be extracted, probably unauthorized.")
                    val (user, redirectUrl) = handleAccessTokenInternal(tokensInJwt, request)
                    if (redirectUrl?.isUrlValid(domainPattern) == false) {
                        throw BadRequestException("Redirect URL $redirectUrl is invalid.")
                    }
                    user.affiliateSource = request.cookie("affiliateSource")?.value
                    user.language = request.header("accept-language")?.take(2)
                    val response = userFactory(user)
                    notifyProvider(response)

                    val (accessTokenExpiry, refreshTokenExpiry) = tokenExpirations()
                    val redirectJwt = JwtUser(response.userId, accessTokenExpiry, response.role.ordinal).toJwt()
                    log.info(
                        "User is being logged in.",
                        user.map() + mapOf(
                            "userId" to user.id,
                            "jwt" to redirectJwt.take(MAX_TOKEN_CHARS) + "***",
                        ),
                    )

                    respond(
                        status = Status.OK,
                        userId = response.userId,
                        userState = response.state,
                    )
                } catch (e: HttpStatusException) {
                    log.info("User cannot log in: ${e.message}")
                    respond(Status(e.status, e.message))
                } catch (e: JwtException) {
                    log.info("User cannot log in: ${e.message}")
                    respond(Status.FORBIDDEN)
                } catch (e: Exception) {
                    log.fatal("Could not process login: ${e.message}", cause = e)
                    respond(Status.INTERNAL_SERVER_ERROR)
                }
            },
        )

    internal fun userFactory(user: ExtractedUser): UserIdResponse =
        serviceCall("api", "/v1/users")
            .httpPost()
            .header("Content-Type", "application/json")
            .body(user.map().toJwt())
            .fetch<UserIdResponse>()

    private val accessTokenFetcher: AccessTokenFetcher by lazy { provider.extractAccessTokenFetcher() }

    private fun OAuthProvider.extractAccessTokenFetcher(): AccessTokenFetcher {
        val property = OAuthProvider::class.memberProperties.find { it.name == "accessTokenFetcher" }!!
        property.isAccessible = true
        return property.get(this) as AccessTokenFetcher
    }

    private fun OAuthProvider.extractCallbackUrl(): String {
        val property = OAuthProvider::class.memberProperties.find { it.name == "callbackUri" }!!
        property.isAccessible = true
        return (property.get(this) as Uri).toString()
    }

    private fun authInitUri(
        callbackUrl: String,
        state: String?,
    ): String =
        "${provider.providerConfig.authUri}?" +
            "client_id=${provider.providerConfig.credentials.user}&" +
            "response_type=code&" +
            "redirect_uri=${callbackUrl.escapeUrl()}&" +
            "scope=${provider.scopes.joinToString("%20")}&" +
            // used to pass info about current users
            // https://stackoverflow.com/questions/58858066/pass-a-string-through-discord-oauth
            state?.let { "state=$it&" } +
            permissions?.let { "permissions=$permissions&" }
}

private const val MAX_TOKEN_CHARS = 10

data class AuthResponse(
    val status: Int,
    val userId: String?,
    val role: Int?,
    val isNew: Boolean?,
    val userState: UserStateChange?,
    val sessionId: String?,
    val accessToken: String? = null,
    val refreshToken: String? = null,
)

internal data class ConnectionResponse(
    val url: String,
)

private val oAuthCodeParameter = Query.string().optional("code", "OAuth code")
val httpClient = Fuel(timeout = Duration.ofSeconds(10))
