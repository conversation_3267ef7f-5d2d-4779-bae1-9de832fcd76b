package hero.api.payment.scripts.usmigration

import com.stripe.param.CustomerSearchParams
import hero.api.payment.scripts.initializeStripeScript
import hero.model.Currency

fun main() {
    val (_, _, _, _, clients) = initializeStripeScript(migrationProduction)
    clients[Currency.EUR]
        .customers()
        .search(CustomerSearchParams.builder().setQuery("name ~ 'USD'").build())
        .autoPagingIterable()
        .filter { it.name.endsWith(" / USD") }
        .forEach {
            println("${it.id} ${it.name}")
        }
}
