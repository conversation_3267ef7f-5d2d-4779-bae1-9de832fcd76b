package hero.api.device.service

import hero.model.DeviceType
import hero.sql.jooq.Tables.DEVICE
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant

class DeviceCommandService(
    lazyContext: Lazy<DSLContext>,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context by lazyContext

    fun execute(command: RegisterDevice) {
        val now = Instant.now(clock)
        context.insertInto(DEVICE)
            .set(DEVICE.DEVICE_ID, command.deviceId)
            .set(DEVICE.USER_ID, command.userId)
            .set(DEVICE.APP_VERSION, command.appVersion)
            .set(DEVICE.FIREBASE_REGISTRATION_TOKEN, command.registrationToken)
            .set(DEVICE.DEVICE_TYPE, command.deviceType.name)
            .set(DEVICE.CREATED_AT, now)
            .set(DEVICE.UPDATED_AT, now)
            .setNull(DEVICE.DISABLED_AT)
            // the device type is not updatable
            .onDuplicateKeyUpdate()
            .set(DEVICE.APP_VERSION, command.appVersion)
            .set(DEVICE.FIREBASE_REGISTRATION_TOKEN, command.registrationToken)
            .set(DEVICE.UPDATED_AT, now)
            .setNull(DEVICE.DISABLED_AT)
            .execute()
    }

    fun execute(command: DisableDevice) {
        val now = Instant.now(clock)
        context.update(DEVICE)
            .set(DEVICE.DISABLED_AT, now)
            .where(DEVICE.USER_ID.eq(command.userId))
            .and(DEVICE.DEVICE_ID.eq(command.deviceId))
            .execute()
    }
}

data class DisableDevice(
    val deviceId: String,
    val userId: String,
)

data class RegisterDevice(
    val userId: String,
    val deviceId: String,
    val appVersion: String,
    val registrationToken: String,
    val deviceType: DeviceType,
)
