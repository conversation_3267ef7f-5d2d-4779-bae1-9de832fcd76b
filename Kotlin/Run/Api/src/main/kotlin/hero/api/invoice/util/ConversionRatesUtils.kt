package hero.api.invoice.util

import com.github.kittinunf.fuel.httpGet
import hero.baseutils.fetch
import hero.model.Currency
import java.math.BigDecimal
import java.time.LocalDate

private val conversionRatesCache: MutableMap<LocalDate, Map<Currency, BigDecimal>> = mutableMapOf()

@Deprecated("Non-precise, try to avoid. Maybe use https://herohero.flexibee.eu/c/herohero/kurz.json")
fun fetchConversionRates(date: LocalDate): Map<Currency, BigDecimal> =
    conversionRatesCache.getOrPut(date) {
        try {
            // We intentionally go always against production service not perform unnecessary request via devel.
            // This service should also be more stable when running in integration tests.
            "https://europe-west1-heroheroco.cloudfunctions.net/prod-conversion-rates/day/$date"
                .httpGet()
                .fetch<ConversionRatesResponse>()
                .rates
        } catch (e: Exception) {
            error("Couldn't fetch conversion rates for $date: ${e.message}")
        }
    }

private data class ConversionRatesResponse(
    val rates: Map<Currency, BigDecimal>,
)
