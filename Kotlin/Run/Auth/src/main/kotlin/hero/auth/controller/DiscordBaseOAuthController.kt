package hero.auth.controller

import com.github.kittinunf.fuel.httpGet
import dev.forkhandles.result4k.mapFailure
import dev.forkhandles.result4k.resultFrom
import hero.auth.model.AccessTokenDetails
import hero.auth.model.DiscordUser
import hero.baseutils.FuelException
import hero.baseutils.fetch
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.UnauthorizedException
import hero.gcloud.PubSub
import hero.jackson.fromJson
import hero.model.DiscordCredentials
import hero.model.ExtractedUser
import hero.model.OAuthProvider.DISCORD
import hero.model.UserIdResponse
import hero.model.topics.DiscordConnectionChanged
import hero.model.topics.SubscriberStatusChange
import io.jsonwebtoken.Claims
import org.http4k.core.Credentials
import org.http4k.core.HttpHandler
import org.http4k.core.Request
import org.http4k.core.Status
import org.http4k.core.Uri
import org.http4k.security.AccessTokenResponse
import org.http4k.security.OAuthCallbackError
import org.http4k.security.OAuthPersistence
import org.http4k.security.OAuthProvider
import org.http4k.security.OAuthProviderConfig
import org.http4k.security.openid.IdToken
import java.time.Instant
import org.http4k.security.AccessTokenDetails as AccessTokenDetailsHttp4k

abstract class DiscordBaseOAuthController(
    hostnameServices: String,
    hostname: String,
    servicePath: String,
    credentials: DiscordCredentials,
    discordType: String,
    private val scopes: List<String>,
    private val pubSub: PubSub,
) : OAuthController(
        hostnameServices,
        hostname,
        servicePath,
        credentials,
        "discord-$discordType",
        // https://discord.com/developers/docs/topics/permissions
        permissions = "268435459",
    ) {
    // https://discord.com/developers/applications/970573270548111380/information
    override fun provide(
        client: HttpHandler,
        credentials: Credentials,
        tokenPersistence: OAuthPersistence,
        callbackUri: Uri,
    ): OAuthProvider =
        OAuthProvider(
            OAuthProviderConfig(
                Uri.of("https://discord.com"),
                "/api/oauth2/authorize",
                "/api/oauth2/token",
                credentials,
                Uri.of("https://discord.com"),
            ),
            client,
            callbackUri,
            scopes,
            tokenPersistence,
            accessTokenExtractor = { msg ->
                resultFrom {
                    val result = msg.bodyString().fromJson<AccessTokenResponse>()
                    val id = result.id_token?.let { IdToken(it) }
                    AccessTokenDetailsHttp4k(
                        result.toAccessToken(),
                        idToken = id,
                    )
                }.mapFailure {
                    OAuthCallbackError.CouldNotFetchAccessToken(msg.status, msg.bodyString())
                }
            },
        )

    override fun handleAccessToken(
        tokenDetails: AccessTokenDetails,
        tokenDetailsMap: Map<String, Any?>,
        request: Request,
    ): Pair<ExtractedUser, String?> {
        val oAuthUser = try {
            "https://discord.com/api/users/@me".httpGet()
                .header("Authorization", "Bearer ${tokenDetails.accessToken.value}")
                .fetch<DiscordUser>()
        } catch (e: FuelException) {
            when (e.status) {
                Status.UNAUTHORIZED.code -> throw UnauthorizedException(e.message)
                Status.FORBIDDEN.code -> throw ForbiddenException(e.message)
                else -> throw e
            }
        }

        val extractedUser = ExtractedUser(
            id = oAuthUser.id,
            secondaryId = tokenDetailsMap["guildId"] as String?,
            userId = tokenDetailsMap["userId"] as String,
            provider = DISCORD,
            name = oAuthUser.userName,
            email = null,
            // `oAuthUser.avatar` not working properly, see https://gitlab.com/heroheroco/general/-/issues/857
            imageUrl = null,
            accessToken = tokenDetails.accessToken.value,
            refreshToken = tokenDetails.accessToken.refreshToken?.value,
            tokenExpiresAt = tokenDetails.accessToken.expiresIn?.let { Instant.now().plusSeconds(it) },
        )

        return extractedUser to (tokenDetailsMap["redirectUrl"] as String?)
    }

    override fun extractSecondaryInfoInCode(request: Request): Map<String, Any?> =
        mapOf("guildId" to request.query("guild_id")) + super.extractSecondaryInfoInCode(request)

    override fun extractSecondaryInfoInFinish(payload: Claims): Map<String, Any?> =
        mapOf("guildId" to payload["guildId"]) + super.extractSecondaryInfoInFinish(payload)

    override fun notifyProvider(response: UserIdResponse) {
        // notify PubSub consumers of successful Discord connection to attach subscriptions
        pubSub.publish(
            DiscordConnectionChanged(
                userId = response.userId,
                statusChange = SubscriberStatusChange.SUBSCRIBED,
            ),
        )
    }
}
