package hero.stripe.service

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonProperty
import com.github.kittinunf.fuel.core.extensions.authentication
import com.github.kittinunf.fuel.httpGet
import hero.baseutils.fetch
import hero.model.euCountries
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

class VatMappingProvider(private val flexibeePassword: String) {
    private val countryToVatMapping by lazy {
        (
            "https://herohero.flexibee.eu/c/herohero/sazba-dph/" +
                "(typSzbDphK='typSzbDph.dphZakl').json?limit=0"
        )
            .httpGet()
            .authentication().basic("herohero-api", flexibeePassword)
            .fetch<WinstromWithSazbaDph>()
            .winstrom
            .sazbaDph
            .groupBy { it.stat.replace("code:", "") }
            // export contains non-EU countries, like Switzerland, we don't care about these
            .filter { it.key.uppercase() in euCountries }
            .let { VatMapping(it.toMap()) }
    }

    fun countryToVatMapping(): VatMapping = countryToVatMapping
}

class VatMapping(private val rawCountryMap: Map<String, List<SazbaDph>>) {
    private fun getRaw(
        country: String,
        instant: Instant,
    ): Int? {
        val vatInLocalDates = rawCountryMap[country] ?: return null
        val localDate = instant.atZone(ZoneOffset.UTC).toLocalDate()
        val vat = vatInLocalDates.find { it.platiOdData <= localDate && localDate <= (it.platiDoData ?: LocalDate.MAX) }
            ?.szbDph
            ?: return null
        // check that there are no decimal values, eg. 21.5%
        if (vat.remainder(BigDecimal.ONE).signum() != 0) {
            error(
                "Country $country has non-zero decimal part: " +
                    "$vat % and our code base is not ready for such.",
            )
        }
        return vat.toInt()
    }

    operator fun get(
        country: String,
        instant: Instant,
    ): Int =
        getRaw(country.uppercase(), instant)
            ?: error("Cloud not find VAT value for $country in $instant")
}

class WinstromWithSazbaDph(
    val winstrom: WinstromDataWithSazbaDph,
)

data class WinstromDataWithSazbaDph(
    @JsonProperty("@version") val version: String,
    @JsonProperty("sazba-dph") val sazbaDph: List<SazbaDph>,
)

data class SazbaDph(
    val id: String,
    val lastUpdate: String,
    val stat: String,
    @JsonProperty("stat@ref")
    val statRef: String,
    @JsonProperty("stat@showAs")
    val statShowAs: String,
    val typSzbDphK: String,
    @JsonProperty("typSzbDphK@showAs") val typSzbDphKShowAs: String,
    val szbDph: BigDecimal,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-ddXXX")
    @JsonProperty("platiOdData")
    val platiOdData: LocalDate,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-ddXXX")
    @JsonProperty("platiDoData")
    val platiDoData: LocalDate?,
)
