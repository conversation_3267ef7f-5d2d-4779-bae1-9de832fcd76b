package hero.api.invoice.scripts

import hero.baseutils.SystemEnv
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Invoice
import hero.model.User

fun main() {
    val production = false
    val firestore = firestore(SystemEnv.cloudProject, production)

    val usersCollection = firestore.typedCollectionOf(User)
    val invoicesCollection = firestore.typedCollectionOf(Invoice)

    val invoices = invoicesCollection
        .where(Invoice::userId).isEqualTo("userId")
        .fetchAll()

    println("Found ${invoices.size} invoices")

    invoices.map { invoice ->
        println("[${Thread.currentThread().name}] Processing invoice ${invoice.id}")
        val user = usersCollection[invoice.userId].fetch()
        if (user?.company == null) {
            error("Script can be only run for existing users with correctly filled company object: ${user?.id}")
        }
        val userCompany = user.company!!.copy(namePublic = user.name)
        invoicesCollection[invoice.id].field(Invoice::invoicedCompany).update(userCompany)
    }
}
