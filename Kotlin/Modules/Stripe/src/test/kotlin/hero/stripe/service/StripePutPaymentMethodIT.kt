package hero.stripe.service

import com.stripe.model.PaymentMethod
import com.stripe.param.CustomerCreateParams
import com.stripe.param.PaymentMethodCreateParams
import hero.baseutils.SystemEnv
import hero.model.Currency.EUR
import hero.model.topics.CardCreateType.CARD
import hero.stripe.model.SetupIntentStatus
import hero.stripe.model.StripeDeclineCode
import hero.stripe.model.StripeErrorCode
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class StripePutPaymentMethodIT {
    private val stripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val stripeService = StripeService(stripeClients, null)
    private val stripePaymentMethods = StripePaymentMethodsService(stripeClients, stripeService, null)

    private val customer = stripeClients[EUR].customers().create(
        CustomerCreateParams
            .builder()
            .setEmail("<EMAIL>")
            .setName("Herohero testing account")
            .build(),
    )

    @Test
    fun `put payment method with no validation`() {
        val paymentMethod = paymentMethod("tok_visa")
        val response = stripePaymentMethods.putPaymentMethodViaSetupIntent(
            paymentMethod.id,
            customer.id,
            EUR,
            true,
            CARD,
            true,
        )
        assertEquals(paymentMethod.id, response.paymentMethodId)
        assertEquals(customer.id, response.customerId)
        assertEquals(SetupIntentStatus.SUCCEEDED, response.status)
        assertNull(response.nextAction)
    }

    @Test
    fun `put payment method with 3DS`() {
        val paymentMethod = paymentMethod("tok_threeDSecure2Required")
        val response = stripePaymentMethods.putPaymentMethodViaSetupIntent(
            paymentMethod.id,
            customer.id,
            EUR,
            true,
            CARD,
            true,
        )
        assertEquals(paymentMethod.id, response.paymentMethodId)
        assertEquals(customer.id, response.customerId)
        assertEquals(SetupIntentStatus.REQUIRES_ACTION, response.status)
        assertEquals("use_stripe_sdk", response.nextAction?.type)
        assertNotNull(response.nextAction?.useStripeSdk)
    }

    @Test
    fun `generic decline`() {
        val paymentMethod = paymentMethod("tok_visa_chargeDeclined")
        val response = stripePaymentMethods.putPaymentMethodViaSetupIntent(
            paymentMethod.id,
            customer.id,
            EUR,
            true,
            CARD,
            true,
        )
        assertEquals(paymentMethod.id, response.paymentMethodId)
        assertEquals(customer.id, response.customerId)
        assertEquals(SetupIntentStatus.CANCELLED, response.status)
        assertEquals(StripeDeclineCode.GENERIC_DECLINE, response.declineCode)
        assertEquals(StripeErrorCode.CARD_DECLINED, response.declineError)
        assertNull(response.nextAction)
    }

    @Test
    fun `insufficient funds`() {
        val paymentMethod = paymentMethod("tok_visa_chargeDeclinedInsufficientFunds")
        val response = stripePaymentMethods.putPaymentMethodViaSetupIntent(
            paymentMethod.id,
            customer.id,
            EUR,
            true,
            CARD,
            true,
        )
        assertEquals(paymentMethod.id, response.paymentMethodId)
        assertEquals(customer.id, response.customerId)
        assertEquals(SetupIntentStatus.CANCELLED, response.status)
        assertEquals(StripeDeclineCode.INSUFFICIENT_FUNDS, response.declineCode)
        assertEquals(StripeErrorCode.CARD_DECLINED, response.declineError)
        assertNull(response.nextAction)
    }

    @Test
    fun `incorrect cvc`() {
        val paymentMethod = paymentMethod("tok_chargeDeclinedIncorrectCvc")
        val response = stripePaymentMethods.putPaymentMethodViaSetupIntent(
            paymentMethod.id,
            customer.id,
            EUR,
            true,
            CARD,
            true,
        )
        assertEquals(paymentMethod.id, response.paymentMethodId)
        assertEquals(customer.id, response.customerId)
        assertEquals(SetupIntentStatus.CANCELLED, response.status)
        assertEquals(StripeDeclineCode.INCORRECT_CVC, response.declineCode)
        assertEquals(StripeErrorCode.INCORRECT_CVC, response.declineError)
        assertNull(response.nextAction)
    }

    // Testing tokens can be found here: https://stripe.com/docs/testing
    private fun paymentMethod(token: String): PaymentMethod =
        stripeClients[EUR].paymentMethods().create(
            PaymentMethodCreateParams
                .builder()
                .setType(PaymentMethodCreateParams.Type.CARD)
                .setCard(
                    PaymentMethodCreateParams.Token.builder()
                        .setToken(token)
                        .build(),
                )
                .build(),
        )
}
