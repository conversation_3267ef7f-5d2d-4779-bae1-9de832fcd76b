package hero.api.user.repository

import com.github.kittinunf.fuel.httpGet
import hero.baseutils.envPrefix
import hero.baseutils.fetch
import hero.model.ImageAsset

class ImageRepository(
    production: Boolean,
) {
    private val imageCloudFunction =
        "https://europe-west1-heroheroco.cloudfunctions.net/${production.envPrefix}-image-size"

    fun imageMeta(imageUrl: String): ImageAsset =
        dimensions(imageUrl).let { ImageAsset.of(imageUrl, width = it.width, height = it.height) }

    fun dimensions(imageUrl: String): ImageSize =
        imageCloudFunction
            .httpGet(listOf("url" to imageUrl))
            .fetch<ImageSize>()
}

data class ImageSize(
    val width: Int,
    val height: Int,
)
