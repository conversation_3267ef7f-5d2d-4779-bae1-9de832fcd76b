package hero.http4k.auth

import hero.baseutils.plusDays
import hero.exceptions.http.ForbiddenException
import hero.jwt.IMPERSONATION_TOKEN
import hero.jwt.JwtUser
import hero.jwt.toJwt
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.cookie.Cookie
import org.http4k.core.cookie.cookie
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.Instant
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

internal class AuthUtilsTest {
    @Test
    fun `should parse jwt from cookie`() {
        val expectedUser = JwtUser("anna-marie", Instant.now().plusSeconds(180).epochSecond, 0)
        val request = Request(Method.GET, "")
            .withAccessTokenCookie(expectedUser.toJwt())

        val parsedUser = request.parseJwtUser()

        assertNotNull(parsedUser)
        assertEquals(expectedUser, parsedUser)
    }

    @Test
    fun `should throw if token is expired`() {
        val jwtToken = JwtUser(
            "2aa5080a-70ac-418d-a23a-0358e9f478af",
            Instant.now().minusSeconds(180).epochSecond,
            0,
        ).toJwt()

        assertNull(
            Request(Method.GET, "")
                .withAccessTokenCookie(jwtToken)
                .parseJwtUser(),
        )
    }

    @Test
    fun `using both impersonate and access token should prioritize impersonate`() {
        val accessTokenUser = JwtUser("access", Instant.now().epochSecond, 1)
        val impersonateTokenUser = JwtUser("impersonate-marie", Instant.now().plusDays(1).epochSecond, 0)

        val request = Request(Method.GET, "")
            .withAccessTokenCookie(accessTokenUser.toJwt())
            .cookie(Cookie(IMPERSONATION_TOKEN, impersonateTokenUser.toJwt()))

        val parsedUser = request.parseJwtUser(allowImpersonation = true)

        assertNotNull(parsedUser)
        assertEquals(impersonateTokenUser, parsedUser)
    }

    @Test
    fun `impersonation may be disallowed for certain endpoints`() {
        val accessTokenUser = JwtUser("access", Instant.now().epochSecond, 1)
        val impersonateTokenUser = JwtUser("impersonate-marie", Instant.now().plusDays(1).epochSecond, 0)

        val request = Request(Method.GET, "")
            .withAccessTokenCookie(accessTokenUser.toJwt())
            .cookie(Cookie(IMPERSONATION_TOKEN, impersonateTokenUser.toJwt()))

        assertThrows<ForbiddenException> {
            request.parseJwtUser(allowImpersonation = false)
        }
    }
}
