package hero.auth.controller

import hero.auth.constant.IMPERSONATION_TOKEN_DURATION
import hero.auth.service.RefreshTokenCommandService
import hero.auth.service.RotateRefreshToken
import hero.baseutils.systemEnv
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.UnauthorizedException
import hero.http4k.auth.tokenExpirations
import hero.http4k.auth.withAccessTokenCookie
import hero.http4k.auth.withImpersonateTokenCookie
import hero.http4k.auth.withRefreshTokenCookie
import hero.http4k.extensions.deviceId
import hero.http4k.extensions.example
import hero.http4k.extensions.post
import hero.http4k.extensions.remoteAddress
import hero.http4k.extensions.userAgent
import hero.jwt.IMPERSONATION_TOKEN
import hero.jwt.JwtUser
import hero.jwt.REFRESH_TOKEN
import hero.jwt.jwtParser
import hero.jwt.toJwt
import org.http4k.contract.ContractRoute
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.core.cookie.cookie
import java.time.Instant

class RefreshController(
    private val refreshTokenCommandService: RefreshTokenCommandService,
) {
    @Suppress("unused")
    val routeRefreshToken: ContractRoute =
        REFRESH_PATH.post(
            summary = "Refreshes user's access token.",
            tag = "Authentication",
            parameters = object {},
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            receiving = null,
            handler = { request, _ ->
                val refreshCookie = request.cookie(REFRESH_TOKEN) ?: throw UnauthorizedException()
                val (accessTokenExpiry, refreshTokenExpiry) = tokenExpirations()

                val userAgent = request.userAgent ?: throw BadRequestException("Missing user agent")
                val (newRefreshToken, jwtUser) = refreshTokenCommandService.execute(
                    RotateRefreshToken(
                        refreshToken = refreshCookie.value,
                        refreshTokenExpiresAt = refreshTokenExpiry,
                        accessTokenExpiresAt = accessTokenExpiry,
                        userAgent = userAgent,
                        deviceId = request.deviceId,
                        ipAddress = request.remoteAddress,
                    ),
                )

                Response(Status.NO_CONTENT)
                    .withAccessTokenCookie(jwtUser.toJwt())
                    .withRefreshTokenCookie(newRefreshToken.token, refreshFullPath)
                    .handleImpersonationToken(request)
            },
        )

    private fun Response.handleImpersonationToken(request: Request): Response =
        request.cookie(IMPERSONATION_TOKEN)?.value
            ?.let {
                val claims = jwtParser().parseSignedClaims(it).payload
                return this.withImpersonateTokenCookie(
                    JwtUser(
                        claims.subject,
                        Instant.now().plus(IMPERSONATION_TOKEN_DURATION).epochSecond,
                        0,
                        null,
                    ).toJwt(),
                )
            }
            ?: this

    companion object {
        private const val REFRESH_PATH: String = "/v1/oauth/refresh"
        val refreshFullPath: String = "/${systemEnv("SERVICE_NAME")}$REFRESH_PATH"
    }
}
