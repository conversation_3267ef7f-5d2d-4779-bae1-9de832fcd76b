package hero.stripe.service

import com.stripe.model.Coupon
import com.stripe.model.Subscription
import com.stripe.param.SubscriptionCreateParams
import hero.baseutils.mockNow
import hero.exceptions.http.ConflictException
import hero.model.CouponMethod
import hero.model.CouponMethod.TRIAL
import hero.model.CouponMethod.VOUCHER
import hero.model.Currency.EUR
import hero.model.Tier
import hero.model.topics.CardCreateType
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.unmockkAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import java.time.Instant
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class StripeSubscriptionServiceTest {
    private val service = spyk(StripeSubscriptionService(mockk(), mockk(), false, mockk(), mockk()))

    @BeforeEach
    fun before() {
        mockNow("2020-01-01T00:00:00Z")
    }

    @AfterEach
    fun clear() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun `coupon with both days and months is denied`() {
        val coupon = createTestCoupon(VOUCHER, 3, 3, null, 100)
        assertThrows<ConflictException> {
            createTestSubscription(coupon)
        }
    }

    @Test
    fun `gift with months off is applied`() {
        val coupon = createTestCoupon(VOUCHER, null, 3, null, 100)
        val params = createTestSubscription(coupon)
        val metadata = params.metadata as Map<String, String?>

        mapOf(
            "couponAppliedForDays" to null,
            "couponAppliedForMonths" to "3",
            "couponPercentOff" to null,
            "couponExpiresAt" to "2020-04-01T00:00:00Z",
            "couponMethod" to VOUCHER.name,
        ).forEach {
            assertTrue(it in metadata.entries, "$it !in $metadata")
        }

        assertEquals(null, params.trialPeriodDays)
    }

    @Test
    fun `gift with days off is denied`() {
        val coupon = createTestCoupon(VOUCHER, 3, null, null, 100)

        assertThrows<ConflictException> {
            createTestSubscription(coupon)
        }
    }

    @Test
    fun `invite with days off is applied`() {
        val coupon = createTestCoupon(TRIAL, 3, null, "0.01".toBigDecimal(), null)
        val params = createTestSubscription(coupon)
        val metadata = params.metadata as Map<String, String?>

        mapOf(
            "couponAppliedForDays" to "3",
            "couponAppliedForMonths" to null,
            "couponPercentOff" to "100",
            "couponExpiresAt" to "2020-01-04T00:00:00Z",
            "couponMethod" to TRIAL.name,
        ).forEach {
            assertTrue(it in metadata.entries, "$it !in $metadata")
        }

        assertEquals(3, params.trialPeriodDays)
    }

    @Test
    fun `invite with months off is applied`() {
        val coupon = createTestCoupon(TRIAL, null, 3, "100".toBigDecimal(), null)
        val params = createTestSubscription(coupon)
        val metadata = params.metadata as Map<String, String?>

        mapOf(
            "couponAppliedForDays" to null,
            "couponAppliedForMonths" to "3",
            "couponPercentOff" to "100",
            "couponExpiresAt" to "2020-04-01T00:00:00Z",
            "couponMethod" to TRIAL.name,
        ).forEach {
            assertTrue(it in metadata.entries, "$it !in $metadata")
        }

        assertEquals(null, params.trialPeriodDays)
    }

    private fun createTestCoupon(
        couponMethod: CouponMethod,
        couponDays: Long?,
        couponMonths: Long?,
        percentOff: BigDecimal?,
        amountOf: Long?,
    ): Coupon =
        Coupon().also {
            it.id = "11111"
            it.metadata = mapOf(
                "days" to couponDays?.toString(),
                "months" to couponMonths?.toString(),
                "couponMethod" to couponMethod.name,
            )
            it.durationInMonths = couponMonths
            it.percentOff = percentOff
            it.amountOff = amountOf
        }

    private fun createTestSubscription(coupon: Coupon): SubscriptionCreateParams {
        every { service.getCoupon(EUR, coupon.id) } returns coupon
        val createParamsSlot = slot<SubscriptionCreateParams>()
        every { service.createSubscription(any(), capture(createParamsSlot)) } returns Subscription()

        service.createSubscription(
            customerId = "customer",
            paymentMethodId = null,
            couponId = coupon.id,
            tier = Tier.ofId("EUR05"),
            priceId = "price",
            creatorId = "creator",
            userId = "user",
            creatorStripeAccountId = "accountId",
            onBehalfOf = null,
            creatorCountry = "DE",
            creatorVatId = "DE123456",
            subscribed = Instant.now(),
            isResubscription = false,
            cardCreateType = CardCreateType.CARD,
            currency = EUR,
        )

        return createParamsSlot.captured
    }
}
