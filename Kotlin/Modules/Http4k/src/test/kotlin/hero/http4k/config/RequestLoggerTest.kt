package hero.http4k.config

import hero.exceptions.http.NotFoundException
import hero.jackson.fromJson
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.then
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class RequestLoggerTest {
    @Test
    fun testExceptionResponseWithBody() {
        val path = "/path/to/hell"
        val remoteHost = "666.666.666.666"
        val userAgent = "Usually a complete mess"
        val request = Request(Method.GET, path)
            .header("X-Forwarded-For", remoteHost)
            .header("User-Agent", userAgent)

        val handler = onRequest(production = true, isLocalHost = false).then {
            throw NotFoundException("Not found.", body = mapOf("additional" to "body", "in" to "response"))
        }

        val response = handler(request)

        assertEquals(
            mapOf("additional" to "body", "in" to "response"),
            response.bodyString().from<PERSON>son(),
        )
    }
}
