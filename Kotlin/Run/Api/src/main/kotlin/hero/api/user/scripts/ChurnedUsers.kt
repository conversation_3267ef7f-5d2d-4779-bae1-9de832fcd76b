package hero.api.user.scripts

import hero.baseutils.SystemEnv
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Subscriber

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)

    val collection = firestore.typedCollectionOf(Subscriber)
    collection
        .fetchAll()
        .groupBy { it.creatorId }
        .map { (creatorId, list) ->
            CreatorStats(
                creatorId = creatorId,
                total = list.size,
                active = list.filter { it.status.isActive }.size,
                inactive = list.filter { !it.status.isActive }.size,
            )
        }
        .filter { it.total > 0 }
        .sortedByDescending { it.total }
        .forEach { println("https://herohero.co/${it.creatorId};${it.total};${it.active};${it.inactive}") }
}

data class CreatorStats(
    val creatorId: String,
    val active: Int,
    val inactive: Int,
    val total: Int,
)
