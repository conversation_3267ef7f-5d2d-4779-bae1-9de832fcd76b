package hero.api.user.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.FirestoreFulltextService
import hero.gcloud.FulltextIndex
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.User
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

suspend fun main() {
    val production = false
    val firestore = firestore(SystemEnv.cloudProject, production)
    val firestoreFulltext = FirestoreFulltextService(firestore.typedCollectionOf(FulltextIndex))

    val users = firestore.typedCollectionOf(User).fetchAll()

    users
        .map { user ->
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    log.info(
                        "Indexing ${user.id} for fulltext search.",
                        mapOf("userId" to user.id, "userName" to user.name),
                    )
                    firestoreFulltext.index(
                        id = user.id,
                        texts = listOfNotNull(user.name, if (user.path == user.id) null else user.path),
                        explicit = user.explicit,
                        subscribers = user.counts.supporters.toInt(),
                    )
                } catch (e: Exception) {
                    log.fatal("Cannot index ${user.id}: ${e.message}")
                }
            }
        }
        .forEach {
            it.join()
        }
}
