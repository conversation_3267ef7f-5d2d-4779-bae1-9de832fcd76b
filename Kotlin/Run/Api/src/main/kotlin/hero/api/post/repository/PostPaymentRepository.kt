package hero.api.post.repository

import hero.gcloud.Document
import hero.gcloud.TypedCollectionReference
import hero.gcloud.where
import hero.model.PostPayment

operator fun TypedCollectionReference<PostPayment>.get(
    postId: String,
    userId: String,
): Document<PostPayment> = this["pp-$postId-$userId"]

fun TypedCollectionReference<PostPayment>.getPaymentsForPost(postId: String): List<PostPayment> =
    where(PostPayment::postId).isEqualTo(postId)
        .fetchAll()
