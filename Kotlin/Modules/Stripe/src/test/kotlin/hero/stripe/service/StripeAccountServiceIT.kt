package hero.stripe.service

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.model.CompanyType
import hero.model.Creator
import hero.model.User
import hero.model.UserCompany
import hero.model.euCountries
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class StripeAccountServiceIT {
    private val isProduction = false
    private val stripeClients = StripeClients(
        keysEu = SystemEnv.stripeKeyEu,
        keysUs = SystemEnv.stripeKeyUs,
        production = isProduction,
    )
    private val service = StripeAccountService(
        clients = stripeClients,
        hostname = "https://test.herohero.co",
        hostnameServices = "https://svc-test.herohero.co",
    )

    @ParameterizedTest
    // AU does not work correctly because of Stripe Bug, see:
    // https://dashboard.stripe.com/support/sco_Rsi6eqUfNcnQwe
    @ValueSource(strings = ["CZ", "DE", "ES", "SK", "US"]) // , "AU"
    fun `successfully create a business account`(country: String) {
        val companyId = when (country) {
            "CZ" -> "********"
            "DE" -> "HRB********"
            "ES" -> "********"
            "SK" -> "********"
            "US" -> "12-1234567"
            "AU" -> "********"
            else -> error("Unknown country: $country")
        }
        val phone = when (country) {
            "CZ" -> "+*********** 006"
            "DE" -> "+49 176 ********"
            "ES" -> "+34 ***********"
            "SK" -> "+*********** 006"
            "US" -> "+****************"
            "AU" -> "+61 7 4567 9812"
            else -> error("Unknown country: $country")
        }
        val postalCode = when (country) {
            "CZ" -> "10000"
            "DE" -> "10117"
            "ES" -> "28001"
            "SK" -> "81101"
            "US" -> "90210"
            "AU" -> "2150"
            else -> error("Unknown country: $country")
        }
        val businessUser = User(
            id = System.currentTimeMillis().toString(),
            company = UserCompany(
                country = country,
                companyType = CompanyType.LEGAL_ENTITY,
                name = "Testing account $country",
                // https://docs.stripe.com/connect/testing
                address = "address_full_match",
                city = "New city",
                postalCode = postalCode,
                id = companyId,
                // https://docs.stripe.com/connect/testing
                vatId = "*********",
                phone = phone,
                firstName = "Jane",
                lastName = "Doe $country",
                birthDate = "1985-05-05",
            ),
            email = "<EMAIL>",
            path = "test-$country",
            name = "test-$country",
            creator = Creator(tierId = "${if (country in euCountries) "EUR" else "USD"}05"),
        )

        val account = service.createAccount(businessUser)
        log.debug("Created a business Stripe account: ${account.id}")
        assertEquals(if (country in euCountries) "eur" else "usd", account.defaultCurrency)
        assertNotNull(account)
    }

    @ParameterizedTest
    // AU does not work correctly because of Stripe Bug, see:
    // https://dashboard.stripe.com/support/sco_Rsi6eqUfNcnQwe
    @ValueSource(strings = ["CZ", "DE", "ES", "SK", "US"]) // , "AU"
    fun `successfully create an individual account`(country: String) {
        val businessUser = User(
            id = System.currentTimeMillis().toString(),
            company = UserCompany(
                country = country,
                companyType = CompanyType.NO_COMPANY,
                firstName = "Person",
                lastName = "without company $country",
                birthDate = "2000-01-01",
            ),
            email = "<EMAIL>",
            path = "test",
            name = "test",
            creator = Creator(tierId = "${if (country in euCountries) "EUR" else "USD"}05"),
        )

        val account = service.createAccount(businessUser)
        log.debug("Created an individual Stripe account: ${account.id}")
        assertEquals(if (country in euCountries) "eur" else "usd", account.defaultCurrency)
        assertNotNull(account)
    }

    @Test
    fun `fail business if details are not provided`() {
        val businessUser = User(
            id = System.currentTimeMillis().toString(),
            company = UserCompany(
                country = "CZ",
                companyType = CompanyType.LEGAL_ENTITY,
                firstName = "Company name for individual",
                id = "2000-01-01",
            ),
            email = "<EMAIL>",
            path = "test",
            name = "test",
            creator = Creator(tierId = "EUR05"),
        )

        assertThrows<BadRequestException> {
            service.createAccount(businessUser)
        }
    }

    @Test
    fun `fail individual if details are not provided`() {
        val individualUser = User(
            id = System.currentTimeMillis().toString(),
            company = UserCompany(
                country = "CZ",
                companyType = CompanyType.NO_COMPANY,
                name = "Person without company",
                birthDate = "2000-01-01",
            ),
            email = "<EMAIL>",
            path = "test",
            name = "test",
            creator = Creator(tierId = "EUR05"),
        )

        assertThrows<BadRequestException> {
            service.createAccount(individualUser)
        }
    }
}
