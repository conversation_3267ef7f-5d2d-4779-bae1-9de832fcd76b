package hero.api.payment.scripts

import com.stripe.StripeClient
import com.stripe.net.RequestOptions
import com.stripe.param.RefundCreateParams
import com.stripe.param.TransferListParams
import com.stripe.param.TransferReversalCollectionCreateParams
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Currency
import hero.model.User
import java.time.Instant

fun main() {
    val (firestore, production, _, _, clients) = initializeStripeScript(true)
    val currency = Currency.EUR

    /*
    val users = firestore["${production.envPrefix}-users"]
        .whereEqualTo(User::status.name, UserStatus.DELETED)
        .fetchAll<User>()
        .filter { it.creator.stripeRequirements?.disabledReason != null }
        .filter { it.id!!.compareTo("epyclomainpyvh") >= 0 }
     */

    val userIds = """
        dummibabyslxvbcjm
        vivienmalarlezoqgi
        citronacikatkaromanvsypgwvc
        jannieveselayxttnrbq
        barcakissggillmli
        lukaswitschelpvcnhuxk
        czkilleryvwzxmfy
        lexnballinjuwpmjqh
        ferovesazkyqzhfqfjm
        anetamackovatzvolplx
        romanakovacovaedtjexqc
    """.trimIndent()
        .split("\\s+".toRegex())

    val users = userIds.flatMap {
        firestore.typedCollectionOf(User)
            .where(User::id).isEqualTo(it)
            .fetchAll()
    }

    for (user in users) {
        through(clients[currency], user)
    }
}

fun through(
    client: StripeClient,
    user: User,
) {
    println("Handling ${user.id}/${user.creator.stripeAccountId}")
    try {
        client
            .transfers()
            .list(
                TransferListParams.builder()
                    .addAllExpand(listOf("data.source_transaction"))
                    .setDestination(user.creator.stripeAccountId)
                    .build(),
            )
            .autoPagingIterable()
            .filter { !it.reversed }
            .map {
                try {
                    val balances = client.balance().retrieve(
                        RequestOptions.builder().setStripeAccount(it.destination).build(),
                    )
                    /*
                    val available = balances.available.firstOrNull { balance -> "eur" == balance.currency }?.amount ?: 0
                    val amount = if (it.currency == "eur") it.amount else it.amount
                     */
                    val available = balances.available.firstOrNull { balance -> "czk" == balance.currency }?.amount ?: 0
                    val amount = if (it.currency == "eur") it.amount * 23 else it.amount

                    if (amount > available) {
                        println("${it.destination}: Not enough funds to refund $amount, available: $available.")
                        return@through
                    }
                    println("")
                    val timestamp = Instant.ofEpochSecond(it.sourceTransactionObject.created)
                    println(
                        "https://herohero.co/${user.id} - ${it.destination} - " +
                            "Refunding $amount, available: $available $timestamp.",
                    )
                    it.reversals.create(TransferReversalCollectionCreateParams.builder().setAmount(it.amount).build())
                    if (timestamp > Instant.now().minusDays(20)) {
                        client.refunds().create(RefundCreateParams.builder().setCharge(it.sourceTransaction).build())
                    }
                } catch (e: Exception) {
                    log.error("${user.id}/${it.id}: ${e.message}")
                }
            }
    } catch (e: Exception) {
        log.error("${user.id}: ${e.message}", cause = e)
    }
}
