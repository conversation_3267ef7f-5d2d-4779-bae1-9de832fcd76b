package hero.api.category.controller

import hero.api.category.repository.CategoriesRepository
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.exceptions.http.UnauthorizedException
import hero.http4k.auth.parseJwtUser
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.delete
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.patch
import hero.http4k.extensions.post
import hero.model.CategoryDto
import hero.model.CategoryDtoAttributes
import hero.model.CategoryDtoRelationship
import hero.model.CategoryDtoRelationships
import hero.model.UserDtoRelationship
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import java.time.Instant

class CategoriesController(
    repository: CategoriesRepository,
) {
    data class CategoryResponse(
        val categories: List<CategoryDto>,
        val included: CategoryResponseIncluded,
    )

    class CategoryResponseIncluded

    data class CategoryOrdering(
        val categories: List<CategoryDtoRelationship>,
    )

    private val categoryOrderingExample = CategoryOrdering(
        categories = listOf(
            CategoryDtoRelationship("aunwbdhland"),
            CategoryDtoRelationship("audkdkfjnvd"),
            CategoryDtoRelationship("adjdjjksncc"),
        ),
    )

    @Suppress("unused")
    val routeGetCategories: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "categories").get(
            summary = "Lists categories for requested user.",
            tag = "Categories",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(
                Status.OK example CategoryResponse(
                    listOf(categoryDtoExample),
                    CategoryResponseIncluded(),
                ),
            ),
            handler = { request, _, userId, _ ->
                val categories = repository.list(userId)

                Response(Status.OK)
                    .body(
                        CategoryResponse(
                            categories = categories,
                            included = CategoryResponseIncluded(),
                        ),
                    )
            },
        )

    @Suppress("unused")
    val routePostCategories: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "categories").post(
            summary = "Creates a new category.",
            tag = "Categories",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = categoryDtoExample,
            responses = listOf(Status.OK example categoryDtoExample),
            handler = { request, _, userId, _ ->
                val jwtUser = request.parseJwtUser()
                    ?: throw UnauthorizedException()
                if (jwtUser.id != userId) {
                    throw ForbiddenException("Cannot post categories of different user: ${jwtUser.id} != $userId.")
                }
                val requestDto = lens<CategoryDto>(request)
                if (jwtUser.id != requestDto.relationships.user.id) {
                    throw ForbiddenException(
                        "User ${jwtUser.id} does not correspond to entity user ${requestDto.relationships.user.id}.",
                    )
                }
                val responseDto = repository.post(requestDto)
                Response(Status.OK).body(responseDto)
            },
        )

    @Suppress("unused")
    val routeDeleteCategory: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "categories" / Path.of("categoryId")).delete(
            summary = "Deletes existing category.",
            tag = "Categories",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.NO_CONTENT example Unit),
            handler = { request, _, userId, _, categoryId ->
                val jwtUser = request.parseJwtUser()
                    ?: throw UnauthorizedException()
                if (jwtUser.id != userId) {
                    throw ForbiddenException("Cannot post categories of different user: ${jwtUser.id} != $userId.")
                }
                val originalDto = repository.get(categoryId)
                    ?: throw NotFoundException("Category $categoryId was not found.")

                if (originalDto.relationships.user.id != jwtUser.id) {
                    throw ForbiddenException(
                        "Cannot modify someone else's category: ${originalDto.relationships.user.id} != ${jwtUser.id}",
                    )
                }
                repository.delete(categoryId)
                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("unused")
    val routePatchCategory: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "categories" / Path.of("categoryId")).patch(
            summary = "Patches existing category.",
            tag = "Categories",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = categoryDtoExample,
            responses = listOf(Status.OK example categoryDtoExample),
            handler = { request, _, userId, _, categoryId ->
                val jwtUser = request.parseJwtUser()
                    ?: throw UnauthorizedException()
                if (jwtUser.id != userId) {
                    throw ForbiddenException("Cannot patch categories of different user: ${jwtUser.id} != $userId.")
                }
                val requestDto = lens<CategoryDto>(request)
                if (requestDto.id != categoryId) {
                    throw BadRequestException("Inconsistent id in body and path: ${requestDto.id} != $categoryId.")
                }
                val user = requestDto.relationships.user
                if (jwtUser.id != user.id) {
                    throw ConflictException(
                        "User ${jwtUser.id} does not correspond to category owning user ${user.id}.",
                    )
                }
                val originalDto = repository.get(categoryId)
                    ?: throw NotFoundException("Category $categoryId was not found.")

                if (originalDto.relationships.user.id != user.id) {
                    throw ForbiddenException(
                        "Cannot modify someone else's category: ${originalDto.relationships.user.id} != ${user.id}",
                    )
                }
                val responseDto = repository.patch(requestDto)
                Response(Status.OK).body(responseDto)
            },
        )

    @Suppress("unused")
    val routePostCategoryOrder: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "categories-order").post(
            summary = "Patches existing category.",
            tag = "Categories",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = categoryOrderingExample,
            responses = listOf(Status.OK example categoryOrderingExample),
            handler = { request, _, userId, _ ->
                val jwtUser = request.parseJwtUser()
                    ?: throw UnauthorizedException()
                if (jwtUser.id != userId) {
                    throw ForbiddenException("Cannot patch categories of different user: ${jwtUser.id} != $userId.")
                }
                val requestDto = lens<CategoryOrdering>(request)
                repository.order(jwtUser, requestDto.categories)
                Response(Status.OK).body(requestDto)
            },
        )
}

val categoryDtoExample = CategoryDto(
    id = "aunwbdhland",
    attributes = CategoryDtoAttributes(
        name = "Self-awareness",
        slug = "self-awareness",
        createdAt = Instant.now(),
        postCount = 123,
    ),
    relationships = CategoryDtoRelationships(
        user = UserDtoRelationship("jane-doe"),
    ),
)
