package hero.functions

import com.github.doyaaaaaken.kotlincsv.client.CsvReader
import com.github.kittinunf.fuel.core.extensions.authentication
import com.github.kittinunf.fuel.httpGet
import hero.baseutils.SystemEnv
import hero.baseutils.fetch
import hero.baseutils.log
import hero.baseutils.systemEnv
import hero.gcloud.CellHorizontalAlignment.LEFT
import hero.gcloud.CellHorizontalAlignment.RIGHT
import hero.gcloud.Color
import hero.gcloud.CurrencyFormat
import hero.gcloud.Format
import hero.gcloud.SHEETS_DEFAULT_COLUMN_COUNT
import hero.gcloud.SheetsCurrency
import hero.gcloud.SheetsRow
import hero.gcloud.SpreadsheetRef
import hero.gcloud.addSheet
import hero.gcloud.appendFormatted
import hero.gcloud.batched
import hero.gcloud.currency
import hero.gcloud.date
import hero.gcloud.emptyCell
import hero.gcloud.emptyRow
import hero.gcloud.formula
import hero.gcloud.insertColumns
import hero.gcloud.resizeColumns
import hero.gcloud.string
import hero.gcloud.writeHeader
import hero.model.Currency
import java.math.BigDecimal
import java.time.LocalDate
import java.time.Year
import java.time.YearMonth
import java.time.ZoneOffset

internal class FinancialsExporter(
    private val currency: SheetsCurrency,
    private val since: Year,
    private val hostname: String = SystemEnv.hostnameNoAuthorizationServices,
) : DataExporter {
    override fun invoke(spreadsheetRef: SpreadsheetRef) {
        val sheetId = try {
            spreadsheetRef.addSheet("Financials")
        } catch (e: Exception) {
            log.fatal("Failed to create new sheet", cause = e)
            throw e
        }

        val yearMonthsRange = yearMonthsRange(YearMonth.of(since.value, 1))
        val rows = yearRange(since)
            .map { year -> year to fetchCsv(year.value) }
            .flatMap { (year, csv) -> toFinancialRow(year.value, csv) }
            .groupBy { it.code }
            .map { mergeFinancialRows(it.value, yearMonthsRange) }
            .let { toSheetsRows(it, yearMonthsRange) }

        spreadsheetRef
            .batched()
            .insertColumns(
                SHEETS_DEFAULT_COLUMN_COUNT + 1,
                50,
                sheetId = sheetId,
            )
            .writeHeader(tableHeader(yearMonthsRange), sheetId = sheetId)
            .resizeColumns(1, 12, 200, sheetId = sheetId)
            .resizeColumns(2, 12, 80, sheetId = sheetId)
            .resizeColumns(3, 12, 110, sheetId = sheetId)
            .resizeColumns(15, 1, 130, sheetId = sheetId)
            .appendFormatted(rows, sheetId = sheetId)
            .execute()
    }

    private fun toFinancialRow(
        year: Int,
        csvResponse: String,
    ): List<FinancialsRow> {
        val parsedCsv = CsvReader().readAllWithHeader(csvResponse)
        val currencyConverter = currencyConverter(currency)

        return parsedCsv.map { csvRow ->
            val monthlyData = monthsRange.map { index ->
                val monthIndex = index.toString().padStart(2, '0')
                MonthlyFinances(
                    YearMonth.of(year, index),
                    revenue = currencyConverter(csvRow.getValue("obratDal$monthIndex").toDouble()),
                    expenses = currencyConverter(csvRow.getValue("obratMd$monthIndex").toDouble()),
                )
            }
            FinancialsRow(
                code = csvRow.getValue("ucet").removePrefix("code:"),
                accountName = csvRow.getValue("nazevUctu"),
                finalAmount = currencyConverter(csvRow.getValue("zustatekMD").toDouble()),
                monthlyFinances = monthlyData,
            )
        }.filter {
            it.code !in setOf(
                // Ostatní provozní výnosy, obratMd01 is negative for reason
                "648001",
            )
        }
    }

    private fun mergeFinancialRows(
        financialRows: List<FinancialsRow>,
        yearMonthsRange: List<YearMonth>,
    ): FinancialsRow {
        val first = financialRows.first()
        val monthlyFinancesMap = financialRows.flatMap { it.monthlyFinances }.associateBy { it.yearMonth }

        val monthlyFinances = yearMonthsRange
            .sortedBy { it }
            .map {
                monthlyFinancesMap[it] ?: MonthlyFinances(it)
            }

        return FinancialsRow(
            code = first.code,
            accountName = first.accountName,
            finalAmount = financialRows.sumOf { it.finalAmount },
            monthlyFinances = monthlyFinances,
        )
    }

    private fun toSheetsRows(
        financialRows: List<FinancialsRow>,
        yearMonthsRange: List<YearMonth>,
    ): List<SheetsRow> {
        val (expenses, revenues) = financialRows.sortedBy { it.code }.partition { it.isExpense }
        val rows = mutableListOf<SheetsRow>()

        rows.addAll(expenses.map { it.render(currency) })
        rows.add(emptyRow)

        rows.addAll(revenues.map { it.render(currency) })
        rows.add(emptyRow)

        rows.add(
            listOf(
                string("Result", Format(bold = true)),
                emptyCell(),
            ) +
                yearMonthsRange.map {
                    val accountCodesColumn = "INDIRECT(ADDRESS(2,2) & \":\" & ADDRESS(ROW()-1, 2))"
                    val currentColumn = "INDIRECT(ADDRESS(2, COLUMN()) & \":\" & ADDRESS(ROW()-1, COLUMN()))"
                    formula(
                        """
                        /* total value is sum of 6* accounts */
                        =SUMIF(
                            $accountCodesColumn,
                            "6*",
                            $currentColumn
                         )
                         /* and subtraction of sum of all 5* accounts */
                         -SUMIF(
                            $accountCodesColumn,
                            "5*",
                            $currentColumn
                        )
                    """.formatFormula(),
                        Format(numberFormat = CurrencyFormat(currency)),
                    )
                },
        )

        return rows
    }

    private fun FinancialsRow.render(currency: SheetsCurrency): SheetsRow =
        listOf(
            string(accountName, Format(bold = true)),
            string(code, Format(horizontalAlignment = RIGHT, bold = true)),
        ) + monthlyFinances.map {
            cellValue(it.value(), currency, it.yearMonth).apply {
                note = "$hostname/accounts-flexibee-reporter/?account=$code&month=${it.yearMonth}&fullTable=false"
            }
        } + listOf(currency(finalAmount, currency))
}

private fun fetchCsv(year: Int): String =
    "$FLEXIBEE_HOST/c/$FLEXIBEE_COMPANY_ID/vysledovka-po-uctech.csv"
        .httpGet(
            listOf(
                "report-name" to "vysledovkaPoUctechStavy",
                "ucetniObdobi" to year,
                "mena" to "code:CZK",
            ),
        )
        .authentication().basic(FLEXIBEE_USER, systemEnv("FLEXIBEE_PASSWORD"))
        .response()
        .second
        .body()
        .toByteArray()
        .toString(Charsets.UTF_8)

private fun currencyConverter(currency: SheetsCurrency): (Double) -> Double {
    if (currency == SheetsCurrency.CZK) {
        return { it }
    } else {
        require(currency == SheetsCurrency.EUR) { "Only CZK and EUR currencies are supported atm." }

        data class ConversionRatesResponse(val rates: Map<Currency, BigDecimal>)

        val rates = "https://europe-west1-heroheroco.cloudfunctions.net/prod-conversion-rates/day/${LocalDate.now()}"
            .httpGet()
            .fetch<ConversionRatesResponse>()
            .rates

        val czkRate = rates.getValue(Currency.CZK)

        return {
            (BigDecimal(it) / czkRate).toDouble()
        }
    }
}

private fun tableHeader(yearMonthsRange: List<YearMonth>) =
    listOf(
        string(
            "Account",
            format = Format(horizontalAlignment = LEFT, bold = true),
        ),
        string(
            "Code",
            format = Format(horizontalAlignment = RIGHT, bold = true),
        ),
    ) + yearMonthsRange.map {
        date(
            it.atDay(1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant(),
            Format(horizontalAlignment = RIGHT, bold = true),
            "yyyy/MM",
        )
    } + listOf(
        string("Final bal.", format = Format(horizontalAlignment = RIGHT, bold = true)),
    )

private data class FinancialsRow(
    val code: String,
    val accountName: String,
    val finalAmount: Double,
    val monthlyFinances: List<MonthlyFinances>,
) {
    val isExpense: Boolean = code.startsWith("5")
}

private data class MonthlyFinances(val yearMonth: YearMonth, val expenses: Double = 0.0, val revenue: Double = 0.0)

private fun cellValue(
    value: Double,
    currency: SheetsCurrency,
    yearMonth: YearMonth,
) = if (yearMonth.isPredictionMonth) {
    val firstDataColumn = 3
    formula(
        """
        /* https://support.google.com/docs/answer/3094000 */
        =FORECAST(
            /* column to be forecasted */
            INDIRECT(ADDRESS(1, COLUMN())),
            /* linking values of known (preceding) data in current ROW() up to previous COLUMN() */
            INDIRECT(ADDRESS(ROW(), $firstDataColumn) & ":" & ADDRESS(ROW(), COLUMN()-1)),
            /* linking months of (preceding) known data in first row */
            INDIRECT(ADDRESS(1, $firstDataColumn) & ":" & ADDRESS(1, COLUMN()-1))
        )
        """.formatFormula(),
        cellFormat(yearMonth).copy(numberFormat = CurrencyFormat(currency)),
    )
} else {
    currency(value, currency, cellFormat(yearMonth))
}

private fun String.formatFormula() = replace("/[*].*?[*]/".toRegex(), "").trimIndent()

private fun cellFormat(yearMonth: YearMonth) =
    if (yearMonth.isPredictionMonth) {
        Format(textColor = Color(0.6, 0.6, 0.6))
    } else {
        Format()
    }

// accounting is usually closed ~2 weeks into the new month, so we have to subtract 1+1/2 months ~ 45 days
private val YearMonth.isPredictionMonth: Boolean
    get() = this.atDay(1) > LocalDate.now().minusDays(45)

private fun MonthlyFinances.value() = if (expenses != 0.0) expenses else revenue

private const val FLEXIBEE_HOST: String = "https://herohero.flexibee.eu"
private const val FLEXIBEE_COMPANY_ID: String = "herohero"
private const val FLEXIBEE_USER: String = "herohero-api"
