package hero.stripe.service

import com.stripe.StripeClient
import com.stripe.exception.InvalidRequestException
import com.stripe.model.Subscription
import com.stripe.param.CustomerCreateParams
import com.stripe.param.PaymentMethodCreateParams
import com.stripe.param.PriceCreateParams
import com.stripe.param.PriceCreateParams.Recurring.Interval
import com.stripe.param.SetupIntentCreateParams
import com.stripe.param.SetupIntentCreateParams.AutomaticPaymentMethods
import com.stripe.param.SubscriptionCreateParams
import hero.baseutils.SystemEnv
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * This test demonstrates that <PERSON><PERSON> cannot attach multi-currency payment methods ot a single customer:
 *
 * > You cannot combine currencies on a single customer. This customer has an active subscription,
 * > subscription schedule, discount, quote, or invoice item with currency eur.
 *
 * Should this test starts to fail, it will mean <PERSON><PERSON> fixed this issue and we can simplify our codebase
 * as now we have to store separate Stripe customer for every single currency.
 *
 * See: https://gitlab.com/heroheroco/general/-/issues/1345
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class StripeSingleCurrencyForCustomerIT {
    private val isProduction = false
    private val stripeClient = StripeClient(SystemEnv.stripeKeyEu)

    @Test
    fun `stripe allows only single currency for a customer`() {
        val customer = stripeClient.customers().create(
            CustomerCreateParams
                .builder()
                .setEmail("<EMAIL>")
                .setName("Herohero testing account")
                .build(),
        )

        val priceEur = stripeClient.prices().create(
            PriceCreateParams
                .builder()
                .setCurrency("EUR")
                .setProductData(PriceCreateParams.ProductData.builder().setName("Testing product").build())
                .setUnitAmount(100L)
                .setRecurring(recurringPeriod(production = isProduction))
                .build(),
        )

        val priceUsd = stripeClient.prices().create(
            PriceCreateParams
                .builder()
                .setCurrency("USD")
                .setProductData(PriceCreateParams.ProductData.builder().setName("Testing product").build())
                .setUnitAmount(100L)
                .setRecurring(recurringPeriod(production = isProduction))
                .build(),
        )

        val paymentMethod = stripeClient.paymentMethods().create(
            PaymentMethodCreateParams
                .builder()
                .setType(PaymentMethodCreateParams.Type.CARD)
                .setCard(
                    PaymentMethodCreateParams.Token.builder()
                        .setToken("tok_visa")
                        .build(),
                )
                .build(),
        )

        // attach the payment method to the customer
        val setupIntent = stripeClient.setupIntents().create(
            SetupIntentCreateParams.builder()
                .setCustomer(customer.id)
                .setPaymentMethod(paymentMethod.id)
                .setUsage(SetupIntentCreateParams.Usage.ON_SESSION)
                // A `return_url` must be specified because this Setup Intent is configured to automatically accept the payment
                // methods enabled in the Dashboard, some of which may require a full page redirect to succeed. If you do not
                // want to accept redirect-based payment methods, set `automatic_payment_methods[enabled]` to `true` and
                // `automatic_payment_methods[allow_redirects]` to `never` when creating Setup Intents and Payment Intents.
                .setAutomaticPaymentMethods(
                    AutomaticPaymentMethods.builder()
                        .setEnabled(true)
                        .setAllowRedirects(AutomaticPaymentMethods.AllowRedirects.NEVER)
                        .build(),
                )
                .build(),
        )
        setupIntent.confirm()
        assertNull(setupIntent.nextAction)

        // we create a subscription with EUR payment method
        createSubscription(
            customerId = customer.id,
            priceId = priceEur.id,
            paymentMethodId = paymentMethod.id,
        )

        // WARN: should this test start to fail, see comment above on the test itself
        val exception = assertThrows<InvalidRequestException> {
            createSubscription(
                customerId = customer.id,
                priceId = priceUsd.id,
                paymentMethodId = paymentMethod.id,
            )
        }

        @Suppress("ktlint:standard:max-line-length")
        assertTrue(
            "You cannot combine currencies on a single customer. This customer has an active subscription, subscription schedule," +
                " discount, quote, or invoice item with currency eur."
                in exception.message!!,
        )
    }

    private fun createSubscription(
        customerId: String,
        paymentMethodId: String,
        priceId: String,
    ): Subscription {
        val subCreateParams = SubscriptionCreateParams
            .builder()
            .setDefaultPaymentMethod(paymentMethodId)
            .addItem(SubscriptionCreateParams.Item.builder().setPrice(priceId).build())
            .setCustomer(customerId)
            .build()

        return stripeClient.subscriptions().create(subCreateParams)
    }

    private fun recurringPeriod(production: Boolean): PriceCreateParams.Recurring =
        PriceCreateParams.Recurring.builder()
            .setInterval(if (production) Interval.MONTH else Interval.DAY)
            .setUsageType(PriceCreateParams.Recurring.UsageType.LICENSED)
            .build()
}
