package hero.api.subscriber.scripts

import hero.baseutils.SystemEnv
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Subscriber
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneOffset

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)
    val subCollection = firestore.typedCollectionOf(Subscriber)

    val start = LocalDate.of(2023, 9, 17).atStartOfDay().toInstant(ZoneOffset.UTC)
    val end = LocalDate.of(2023, 9, 30).atTime(LocalTime.MAX).toInstant(ZoneOffset.UTC)
    val creatorId = "themagenbdyhvw"
    val count = subCollection
        .where(Subscriber::creatorId).isEqualTo(creatorId)
        .and(Subscriber::subscribed).isGreaterThan(start)
        .and(Subscriber::subscribed).isLessThan(end)
        .count()
    println("Between $start and $end, $creatorId has $count subs")
}
