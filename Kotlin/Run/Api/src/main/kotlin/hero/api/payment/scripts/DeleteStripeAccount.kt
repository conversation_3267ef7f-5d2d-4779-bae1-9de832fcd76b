package hero.api.payment.scripts

import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.union
import hero.gcloud.where
import hero.model.Creator
import hero.model.Currency
import hero.model.User

fun main() {
    val (firestore, _, _, _, clients) = initializeStripeScript(true)
    val users = firestore.typedCollectionOf(User)
    val withConfirmation = true
    val accountsToDelete = listOf(
        "accountId",
    )
    val currency = Currency.EUR

    accountsToDelete.forEach {
        val accountToDelete = clients[currency].accounts().retrieve(it)

        if (withConfirmation) {
            println("Are you sure you want to ${accountToDelete.email}?")
            if (readln() == "no") {
                println("Skipping deletion of ${accountToDelete.email}")
                return@forEach
            }
        }

        println("Deleting account ${accountToDelete.email}")
        try {
            val deletedAccount = clients[currency].accounts().delete(accountToDelete.id)
            println("Deleted account ${accountToDelete.id} with result ${deletedAccount.deleted}")
        } catch (e: Exception) {
            println("Failed to delete account ${accountToDelete.id}, $e")
        } finally {
            val user = users
                .where(root(User::creator).path(Creator::stripeAccountId)).isEqualTo(it)
                .fetchSingle()

            if (user != null) {
                val userId = user.id
                require(userId != null)
                println("Do you want to set user's [$userId] stripe account id to null, and move it to legacy field?")
                if (readln() != "no") {
                    users[userId].field(root(User::creator).path(Creator::stripeAccountId)).update(null)
                    users[userId].field(root(User::creator).path(Creator::stripeAccountLegacyIds)).union(it)
                    users[userId].field(root(User::creator).path(Creator::stripeAccountOnboarded)).update(false)
                    users[userId].field(root(User::creator).path(Creator::active)).update(false)
                }
            }
        }
    }
}
