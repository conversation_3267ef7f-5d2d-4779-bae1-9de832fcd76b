package hero.repository.message

import hero.model.MessageThread
import hero.sql.jooq.Tables
import hero.sql.jooq.tables.records.MessageThreadParticipantRecord
import hero.sql.jooq.tables.records.MessageThreadRecord
import org.jooq.DSLContext
import java.time.Instant

class MessageThreadRepository(
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DS<PERSON>ontext by lazyContext

    constructor(context: DSLContext) : this(lazy { context })

    fun save(messageThread: MessageThread) {
        val now = Instant.now()

        val messageThreadRecord = MessageThreadRecord()
            .apply {
                this.id = messageThread.id
                this.createdAt = messageThread.createdAt
                this.updatedAt = now
                this.lastMessageAt = messageThread.lastMessageAt
                this.lastMessageBy = messageThread.lastMessageBy
                this.lastMessageId = messageThread.lastMessageId
                this.postsCount = messageThread.posts
                this.emailNotified = messageThread.emailNotified
            }

        val messageThreadParticipantRecords = messageThread.userIds.map {
            MessageThreadParticipantRecord().apply {
                this.threadId = messageThread.id
                this.userId = it
                this.seenAt = messageThread.seens[it]
                this.checkedAt = messageThread.checks[it]
                this.archivedAt = if (it in messageThread.archivedFor) now else null
                this.deletedAt = messageThread.deletes[it]
                this.isDeleted = it in messageThread.deletedFor
            }
        }

        context.transaction { tx ->
            tx
                .dsl()
                .insertInto(Tables.MESSAGE_THREAD)
                .set(messageThreadRecord)
                .onDuplicateKeyUpdate()
                .set(messageThreadRecord)
                .execute()

            tx
                .dsl()
                .delete(Tables.MESSAGE_THREAD_PARTICIPANT)
                .where(Tables.MESSAGE_THREAD_PARTICIPANT.THREAD_ID.eq(messageThread.id))
                .execute()

            tx
                .dsl()
                .insertInto(Tables.MESSAGE_THREAD_PARTICIPANT)
                .set(messageThreadParticipantRecords)
                .execute()
        }
    }
}
