package hero.api.notification.controller

import hero.api.notification.controller.dto.UpdateNotificationInput
import hero.api.notification.controller.dto.exampleNotificationResponse
import hero.api.notification.controller.dto.examplePagedNotificationResponse
import hero.api.notification.controller.dto.exampleUpdateNotificationInput
import hero.api.notification.controller.dto.toResponse
import hero.api.notification.service.GetNotifications
import hero.api.notification.service.MarkNotificationsAsSeen
import hero.api.notification.service.NotificationCommandService
import hero.api.notification.service.NotificationQueryService
import hero.api.notification.service.NotificationTypeCategory
import hero.api.notification.service.UpdateNotification
import hero.core.data.PageRequest
import hero.core.data.toResponse
import hero.exceptions.http.ForbiddenException
import hero.http4k.auth.getJwtUser
import hero.http4k.controller.QueryUtils
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.enum
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.http4k.extensions.put
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.Query

class NotificationsController(
    private val notificationCommandService: NotificationCommandService,
    private val notificationQueryService: NotificationQueryService,
) {
    @Suppress("unused")
    val routeUpdateNotification: ContractRoute =
        ("/v1/notifications" / Path.of("notificationId")).put(
            summary = "Patches existing notification.",
            tag = "Notifications",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = exampleUpdateNotificationInput,
            responses = listOf(Status.OK example exampleNotificationResponse),
            handler = { request, _, notificationId ->
                val user = request.getJwtUser(allowImpersonation = false)
                val input = lens<UpdateNotificationInput>(request)
                val updatedNotification = notificationCommandService.execute(
                    UpdateNotification(notificationId, user.id, input.checkedAt, input.seenAt),
                )

                Response(Status.OK).body(updatedNotification.toResponse())
            },
        )

    @Suppress("unused")
    val routeGetNotifications: ContractRoute =
        ("/v1/notifications").get(
            summary = "Get user's notifications.",
            tag = "Notifications",
            parameters = object {
                val pageSize = QueryUtils.pageSize()
                val afterCursor = QueryUtils.afterCursor()
                val beforeCursor = QueryUtils.beforeCursor()
                val categories = Query.enum<NotificationTypeCategory>().multi.defaulted("categories", emptyList())
            },
            responses = listOf(Status.OK example examplePagedNotificationResponse),
            handler = { request, parameters ->
                val user = request.getJwtUser()
                val pageSize = parameters.pageSize(request)
                val afterCursor = parameters.afterCursor(request)
                val beforeCursor = parameters.beforeCursor(request)
                val categories = parameters.categories(request).toSet()

                val pageRequest = PageRequest(
                    pageSize = pageSize,
                    beforeCursor = beforeCursor,
                    afterCursor = afterCursor,
                )
                val result = notificationQueryService.execute(GetNotifications(user.id, pageRequest, categories))

                val response = result.toResponse { it.toResponse() }

                Response(Status.OK).body(response)
            },
        )

    @Suppress("unused")
    val routePostMarkSeen: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "mark-notifications-seen").post(
            summary = "Mark all user's notifications as seen.",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.NO_CONTENT example Unit),
            receiving = null,
            tag = "Notifications",
            handler = { request, _, userId, _ ->
                val user = request.getJwtUser(allowImpersonation = false)
                if (user.id != userId) {
                    throw ForbiddenException(
                        "Cannot mark notifications of different user as seen: ${user.id} != $userId.",
                    )
                }
                notificationCommandService.execute(MarkNotificationsAsSeen(user.id))
                Response(Status.NO_CONTENT)
            },
        )
}
