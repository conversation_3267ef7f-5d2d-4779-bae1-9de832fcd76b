package hero.baseutils

import io.mockk.every
import io.mockk.mockkStatic
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalUnit

/** Mocks both Instant.now() and ZonedDateTime.now() for given ISO8601-formatted string. */
fun mockNow(string: String) {
    val zonedDateTime = ZonedDateTime.parse(string)
    mockkStatic(Instant::class, ZonedDateTime::class)
    every { Instant.now() }.returns(zonedDateTime.toInstant())
    every { ZonedDateTime.now() }.returns(zonedDateTime)
    every { ZonedDateTime.now(any<ZoneId>()) }.answers { zonedDateTime.toInstant().atZone(firstArg()) }
}

fun Instant.truncated(unit: TemporalUnit = ChronoUnit.SECONDS) = this.truncatedTo(unit)
