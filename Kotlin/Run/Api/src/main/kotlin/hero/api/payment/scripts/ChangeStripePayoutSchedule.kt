package hero.api.payment.scripts

import com.stripe.param.AccountUpdateParams
import hero.baseutils.log
import hero.gcloud.fetchAll
import hero.gcloud.typedCollectionOf
import hero.model.Currency
import hero.model.User

fun main() {
    val (firestore, production, _, _, clients) = initializeStripeScript(true)
    val usersCollection = firestore.typedCollectionOf(User)
    val delayDays = 0L
    val currency = Currency.EUR

    usersCollection
        .fetchAll()
        .onEach { log.info("Processing ${it.id}: ${it.creator.stripeAccountId}.") }
        .mapNotNull { it.creator.stripeAccountId }
        .forEach { accountId ->
            val account = clients[currency].accounts().retrieve(accountId)
            log.info("Setting $accountId schedule from ${account.settings.payouts.schedule.delayDays} to $delayDays.")
            try {
                clients[currency].accounts()
                    .retrieve(accountId)
                    .update(
                        AccountUpdateParams.builder()
                            .setSettings(
                                AccountUpdateParams.Settings.builder()
                                    .setPayouts(
                                        AccountUpdateParams.Settings.Payouts.builder()
                                            .setSchedule(
                                                AccountUpdateParams.Settings.Payouts.Schedule.builder()
                                                    .setDelayDays(delayDays)
                                                    .build(),
                                            )
                                            .build(),
                                    )
                                    .build(),
                            )
                            .build(),
                    )
            } catch (e: Exception) {
                log.info(e.message)
            }
        }
}
