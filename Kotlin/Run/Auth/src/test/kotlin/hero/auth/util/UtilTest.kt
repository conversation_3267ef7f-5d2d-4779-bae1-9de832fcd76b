package hero.auth.util

import hero.exceptions.http.ForbiddenException
import hero.jwt.JwtUser
import hero.jwt.TokenType
import hero.jwt.toJwt
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.Test
import java.time.Instant

class UtilTest {
    @Test
    fun `should throw if delete token is not of type delete`() {
        val token = JwtUser("pepa", Instant.now().plusSeconds(100).epochSecond, 0).toJwt(type = TokenType.ACCESS)

        assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
            validateDeleteToken("pepa", token)
        }
    }

    @Test
    fun `should throw if requester is different from the subject in delete token`() {
        val token = deleteToken("pepa", Instant.now().plusSeconds(100).epochSecond)

        assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
            validateDeleteToken("not-pepa", token)
        }
    }

    @Test
    fun `should throw if delete token is expired`() {
        val token = deleteToken("pepa", Instant.now().minusSeconds(100).epochSecond)

        assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
            validateDeleteToken("pepa", token)
        }
    }

    @Test
    fun `should validate delete token without any issues`() {
        val token = deleteToken("pepa", Instant.now().plusSeconds(100).epochSecond)

        assertThatNoException().isThrownBy {
            validateDeleteToken("pepa", token)
        }
    }
}
