@file:Suppress("ktlint:standard:max-line-length")

package hero.http4k.extensions

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.HttpStatusException
import hero.http4k.extensions.CustomJackson.auto
import hero.jackson.heroheroConfiguration
import org.http4k.contract.ContractRoute
import org.http4k.contract.ContractRouteSpec1
import org.http4k.contract.ContractRouteSpec2
import org.http4k.contract.ContractRouteSpec3
import org.http4k.contract.ContractRouteSpec4
import org.http4k.contract.PreFlightExtraction
import org.http4k.contract.RouteMetaDsl
import org.http4k.contract.Tag
import org.http4k.contract.meta
import org.http4k.core.Body
import org.http4k.core.Filter
import org.http4k.core.HttpHandler
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.core.then
import org.http4k.core.with
import org.http4k.format.ConfigurableJackson
import org.http4k.format.asConfigurable
import org.http4k.format.withStandardMappings
import org.http4k.lens.BiDiBodyLens
import org.http4k.lens.BiDiLens
import org.http4k.lens.BiDiLensSpec
import org.http4k.lens.LensFailure
import org.http4k.lens.Query
import kotlin.reflect.KProperty1
import kotlin.reflect.full.memberProperties

inline fun <reified T : Enum<T>> Query.enum(): BiDiLensSpec<Request, T> =
    map({ key -> enumValueOf(key.uppercase()) }, { it.name.lowercase() })

private val customJackson: ObjectMapper
    get() = KotlinModule.Builder().build()
        .asConfigurable()
        .withStandardMappings()
        .done()
        .deactivateDefaultTyping()
        .heroheroConfiguration()

object CustomJackson : ConfigurableJackson(
    customJackson,
)

object CustomJacksonWithoutNulls : ConfigurableJackson(
    customJackson
        // we want to avoid inclusion of NULLs in documentation to avoid dealing with:
        // https://github.com/http4k/http4k/issues/458
        .setSerializationInclusion(JsonInclude.Include.NON_NULL),
)

// syntactic sweetening - note this .auto must be part of CustomJackson above
// see https://www.http4k.org/cookbook/custom_json_marshallers/
inline fun <reified T : Any> lens(): BiDiBodyLens<T> = Body.auto<T>().toLens()

inline fun <reified T : Any> lens(request: Request): T = Body.auto<T>().toLens()(request)

inline fun <reified T : Any> Response.body(body: T): Response = this.with(lens<T>() of body)

inline fun <reified T : Any> Response(
    status: Status,
    body: T,
): Response = Response(status).with(lens<T>() of body)

fun Response(exception: HttpStatusException): Response =
    Response(Status(exception.status, exception.message)).with(lens<Any>() of exception.serialize())

infix fun <T> Status.example(exampleResponse: T): Pair<Status, T> = Pair(this, exampleResponse)

inline fun <reified T : Any, reified U : Any> String.get(
    summary: String,
    parameters: U,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request) -> Response,
): ContractRoute = httpMethod(summary, parameters, null, responses, Method.GET, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any> String.get(
    summary: String,
    parameters: U,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U) -> Response,
): ContractRoute = httpMethod(summary, parameters, null, responses, Method.GET, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any> ContractRouteSpec1<A>.get(
    summary: String,
    parameters: U,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A) -> Response,
): ContractRoute = httpMethod(summary, parameters, null, responses, Method.GET, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any> ContractRouteSpec2<A, B>.get(
    summary: String,
    parameters: U,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B) -> Response,
): ContractRoute = httpMethod(summary, parameters, null, responses, Method.GET, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any, reified C : Any> ContractRouteSpec3<A, B, C>.get(
    summary: String,
    parameters: U,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B, C) -> Response,
): ContractRoute = httpMethod(summary, parameters, null, responses, Method.GET, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any, reified C : Any, reified D : Any> ContractRouteSpec4<A, B, C, D>.get(
    summary: String,
    parameters: U,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B, C, D) -> Response,
): ContractRoute = httpMethod(summary, parameters, null, responses, Method.GET, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any> String.post(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.POST, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any> ContractRouteSpec1<A>.post(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.POST, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any> ContractRouteSpec2<A, B>.post(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.POST, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any, reified C : Any> ContractRouteSpec3<A, B, C>.post(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B, C) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.POST, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any, reified C : Any, reified D : Any> ContractRouteSpec4<A, B, C, D>.post(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B, C, D) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.POST, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any> String.patch(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.PATCH, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any> ContractRouteSpec1<A>.patch(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.PATCH, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any> ContractRouteSpec2<A, B>.patch(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.PATCH, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any, reified C : Any> ContractRouteSpec3<A, B, C>.patch(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B, C) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.PATCH, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any> ContractRouteSpec1<A>.delete(
    summary: String,
    parameters: U,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A) -> Response,
): ContractRoute = httpMethod(summary, parameters, null, responses, Method.DELETE, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any> ContractRouteSpec2<A, B>.delete(
    summary: String,
    parameters: U,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B) -> Response,
): ContractRoute = httpMethod(summary, parameters, null, responses, Method.DELETE, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any, reified C : Any> ContractRouteSpec3<A, B, C>.delete(
    summary: String,
    parameters: U,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B, C) -> Response,
): ContractRoute = httpMethod(summary, parameters, null, responses, Method.DELETE, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any> String.delete(
    summary: String,
    parameters: U,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U) -> Response,
): ContractRoute = httpMethod(summary, parameters, null, responses, Method.DELETE, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any> String.put(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.PUT, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any> ContractRouteSpec1<A>.put(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.PUT, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any> ContractRouteSpec2<A, B>.put(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.PUT, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any, reified C : Any> ContractRouteSpec3<A, B, C>.put(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B, C) -> Response,
): ContractRoute =
    httpMethod(summary, parameters, receiving, responses, Method.PUT, tag, hideFromOpenApi, meta, handler)

inline fun <reified T : Any> received(body: T): Pair<BiDiBodyLens<T>, T> = lens<T>() to body

inline fun <reified T : Any, reified U : Any> RouteMetaDsl.routeMeta(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    tag: String,
    meta: (RouteMetaDsl) -> Unit = {},
    hideFromOpenApi: Boolean,
) {
    this.summary = summary
    this.described = !hideFromOpenApi
    @Suppress("unchecked_cast")
    this.queries += parameters::class.memberProperties
        .filter { BiDiLens::class == it.returnType.classifier }
        .map { it as KProperty1<Any, BiDiLens<Request, List<String>>> }
        .map {
            it.get(parameters)
        }
    this.tags += Tag(tag)
    // do not silently catch LensExceptions so that we can log Jackson errors
    preFlightExtraction = PreFlightExtraction.Companion.IgnoreBody

    if (receiving != null) {
        this.receiving(received(receiving), null)
    }

    meta(this)

    responses.forEach {
        if (it.second is Unit) {
            returning(it.first)
        } else {
            returning(it.first, lens<T>() to it.second)
        }
    }
}

class WrappedLensFailure(lensFailure: LensFailure) : BadRequestException(
    message = listOfNotNull(lensFailure.message, lensFailure.cause?.message).joinToString(", "),
    cause = lensFailure,
)

val lensFailureFilter = Filter { next: HttpHandler ->
    { request: Request ->
        try {
            next(request)
        } catch (e: LensFailure) {
            throw WrappedLensFailure(e)
        }
    }
}

inline fun <reified T : Any, reified U : Any> String.httpMethod(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    method: Method,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request) -> Response,
): ContractRoute =
    this meta { routeMeta(summary, parameters, receiving, responses, tag, meta, hideFromOpenApi) } bindContract
        method to lensFailureFilter.then { request -> handler(request) }

inline fun <reified T : Any, reified U : Any> String.httpMethod(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    method: Method,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U) -> Response,
): ContractRoute =
    this meta { routeMeta(summary, parameters, receiving, responses, tag, meta, hideFromOpenApi) } bindContract
        method to lensFailureFilter.then { request -> handler(request, parameters) }

inline fun <reified T : Any, reified U : Any, reified A : Any> ContractRouteSpec1<A>.httpMethod(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    method: Method,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A) -> Response,
): ContractRoute =
    this meta { routeMeta(summary, parameters, receiving, responses, tag, meta, hideFromOpenApi) } bindContract
        method to { path1 -> lensFailureFilter.then { request -> handler(request, parameters, path1) } }

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any> ContractRouteSpec2<A, B>.httpMethod(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    method: Method,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B) -> Response,
): ContractRoute =
    this meta {
        if (!hideFromOpenApi) routeMeta(summary, parameters, receiving, responses, tag, meta, hideFromOpenApi)
    } bindContract
        method to { path1, path2 -> lensFailureFilter.then { request -> handler(request, parameters, path1, path2) } }

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any, reified C : Any> ContractRouteSpec3<A, B, C>.httpMethod(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    method: Method,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B, C) -> Response,
): ContractRoute =
    this meta {
        if (!hideFromOpenApi) routeMeta(summary, parameters, receiving, responses, tag, meta, hideFromOpenApi)
    } bindContract
        method to { path1, path2, path3 ->
            lensFailureFilter.then { request ->
                handler(
                    request,
                    parameters,
                    path1,
                    path2,
                    path3,
                )
            }
        }

inline fun <reified T : Any, reified U : Any, reified A : Any, reified B : Any, reified C : Any, reified D : Any> ContractRouteSpec4<A, B, C, D>.httpMethod(
    summary: String,
    parameters: U,
    receiving: Any?,
    responses: List<Pair<Status, T>>,
    method: Method,
    tag: String,
    hideFromOpenApi: Boolean = false,
    crossinline meta: (RouteMetaDsl) -> (Unit) = {},
    noinline handler: (Request, U, A, B, C, D) -> Response,
): ContractRoute =
    this meta {
        if (!hideFromOpenApi) routeMeta(summary, parameters, receiving, responses, tag, meta, hideFromOpenApi)
    } bindContract
        method to { path1, path2, path3, path4 ->
            lensFailureFilter.then { request ->
                handler(
                    request,
                    parameters,
                    path1,
                    path2,
                    path3,
                    path4,
                )
            }
        }
