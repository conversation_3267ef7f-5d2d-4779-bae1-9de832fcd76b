package hero.auth.service

import hero.baseutils.log
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.exceptions.http.UnauthorizedException
import hero.gcloud.TypedCollectionReference
import hero.jwt.JwtUser
import hero.jwt.SESSION_ID
import hero.jwt.TOKEN_TYPE_CLAIM
import hero.jwt.TokenType.REFRESH
import hero.jwt.generateJwt
import hero.jwt.jwtParser
import hero.model.Session
import hero.model.SignInProvider
import hero.model.User
import hero.model.UserStatus
import hero.repository.session.SessionRepository
import hero.sql.jooq.Tables.SESSION
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant
import java.util.UUID

class RefreshTokenCommandService(
    private val sessionRepository: SessionRepository,
    private val usersCollection: TypedCollectionReference<User>,
    lazyContext: Lazy<DSLContext>,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context: DSLContext by lazyContext

    fun execute(command: GenerateRefreshToken): RefreshToken {
        val session = Session(
            id = UUID.randomUUID().toString(),
            userId = command.userId,
            userAgent = command.userAgent,
            createdAt = Instant.now(clock),
            refreshedAt = Instant.now(clock),
            deviceId = command.deviceId,
            signInProvider = command.signInProvider,
            ipAddress = command.ipAddress,
            signInLocation = command.signInLocation,
        )
        sessionRepository.save(session)

        // revoke all active sessions that were on the given device
        if (command.deviceId != null) {
            context
                .update(SESSION)
                .set(SESSION.REVOKED, true)
                .where(SESSION.DEVICE_ID.eq(command.deviceId))
                .and(SESSION.USER_ID.eq(command.userId))
                .and(SESSION.ID.notEqual(UUID.fromString(session.id)))
                .execute()
        }

        val token = generateJwt(command.userId, command.refreshTokenExpiresAt, REFRESH, mapOf(SESSION_ID to session.id))

        return RefreshToken(token = token, userId = command.userId, sessionId = session.id)
    }

    // TODO implement proper refresh token rotation
    // we can use sessions to store previous refresh token and newly created refresh token and use these to validate
    fun execute(command: RotateRefreshToken): Pair<RefreshToken, JwtUser> {
        val parsedRefreshToken = parseToken(command.refreshToken)
        val user = usersCollection[parsedRefreshToken.userId].fetch()
        if (user == null || user.status == UserStatus.DELETED) {
            throw NotFoundException("User was not found.", labels = mapOf("userId" to parsedRefreshToken.userId))
        }

        val session = sessionRepository.getById(UUID.fromString(parsedRefreshToken.sessionId))
        if (session.revoked) {
            throw UnauthorizedException("Session expired")
        }

        // update refreshedAt timestamp to current time and update device id, if it was not set
        session
            .copy(
                refreshedAt = Instant.now(clock),
                ipAddress = command.ipAddress,
            )
            .let {
                if (command.deviceId != null && session.deviceId == null)
                    it.copy(deviceId = command.deviceId)
                else
                    it
            }
            .run {
                sessionRepository.save(this)
            }

        val token = generateJwt(user.id, command.refreshTokenExpiresAt, REFRESH, mapOf(SESSION_ID to session.id))
        val refreshToken = RefreshToken(token = token, userId = user.id, sessionId = session.id)

        return refreshToken to JwtUser(
            user.id,
            command.accessTokenExpiresAt,
            user.role.ordinal,
            session.id,
        )
    }

    private fun parseToken(refreshToken: String): RefreshToken =
        try {
            val claims = jwtParser().parseSignedClaims(refreshToken).payload
            if (claims[TOKEN_TYPE_CLAIM] != REFRESH.name) {
                throw ForbiddenException("Invalid token type")
            }

            RefreshToken(
                token = refreshToken,
                userId = claims.subject,
                sessionId = claims[SESSION_ID]?.toString() ?: error("Missing session id in token $refreshToken"),
            )
        } catch (e: Exception) {
            log.error("Failed parsing refresh token", cause = e)
            throw ForbiddenException()
        }
}

data class RefreshToken(
    val token: String,
    val userId: String,
    val sessionId: String,
)

data class GenerateRefreshToken(
    val userId: String,
    val refreshTokenExpiresAt: Long,
    val userAgent: String,
    val ipAddress: String?,
    val signInLocation: String?,
    val signInProvider: SignInProvider,
    val deviceId: String? = null,
)

data class RotateRefreshToken(
    val refreshToken: String,
    val refreshTokenExpiresAt: Long,
    val accessTokenExpiresAt: Long,
    val userAgent: String,
    val ipAddress: String?,
    val deviceId: String? = null,
)
