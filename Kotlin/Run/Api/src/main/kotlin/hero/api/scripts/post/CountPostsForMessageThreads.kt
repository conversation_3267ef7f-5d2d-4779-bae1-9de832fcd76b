package hero.api.scripts.post

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.MessageThread
import hero.model.Post

fun main() {
    val production = false
    val firestore = firestore(SystemEnv.cloudProject, production)

    val messageThreadsCollection = firestore.typedCollectionOf(MessageThread)
    val postCollection = firestore.typedCollectionOf(Post)
    val threads = messageThreadsCollection
        .fetchAll()

    for (thread in threads) {
        val posts = postCollection
            .where(Post::messageThreadId).isEqualTo(thread.id)
            .fetchAll()
        if (posts.isNotEmpty()) {
            log.info("Setting MT.posts ${thread.id} to ${posts.size}.")
            messageThreadsCollection[thread.id].field(MessageThread::posts).update(posts.size.toLong())
        }
    }
}
