@file:Suppress("ktlint:standard:filename")

package hero.api.device.controller.dto

import hero.model.DeviceType

data class RegisterDeviceRequest(
    val appVersion: String,
    val registrationToken: String,
    val deviceType: DeviceType,
)

val exampleRegisterDeviceRequest = RegisterDeviceRequest(
    appVersion = "Herohero@1.0",
    registrationToken = "Firebase-registration-token",
    deviceType = DeviceType.IOS,
)
