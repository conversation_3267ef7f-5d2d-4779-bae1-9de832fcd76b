package hero.api.payment.scripts

import com.stripe.net.RequestOptions
import com.stripe.param.PayoutCreateParams
import hero.baseutils.log
import hero.model.Currency

fun main() {
    // ideally DO NOT DO ANY manual payouts, because it breaks accountancy, see:
    // https://linear.app/herohero/issue/HH-1507/payout-and-invoice-dont-match

    val (_, _, _, _, clients) = initializeStripeScript(true)

    // always include both decimal digits
    // 3100 -> 31.00
    val amount = 30_00L
    val currency = Currency.EUR
    val connectedAccountId = ""

    error("See comment above before running this script.")

    val payoutCreateParams = PayoutCreateParams.builder()
        .setAmount(amount)
        .setCurrency(currency.name.lowercase())
        .setDescription("STRIPE PAYOUT")
        .build()

    val requestOptions = RequestOptions.builder()
        .setStripeAccount(connectedAccountId)
        .build()

    log.info("Paying out $amount CZK to $connectedAccountId")
    clients[currency].payouts().create(payoutCreateParams, requestOptions)
}
