package hero.api.notification.controller

import hero.api.notification.controller.dto.NotificationSettingsUpdateRequest
import hero.api.notification.controller.dto.exampleNotificationSettingsResponse
import hero.api.notification.controller.dto.exampleNotificationSettingsUpdateRequest
import hero.api.notification.controller.dto.toResponse
import hero.api.notification.service.GetNotificationSettings
import hero.api.notification.service.NotificationSettingsCommandService
import hero.api.notification.service.NotificationSettingsQueryService
import hero.api.notification.service.UpdateNotificationSettings
import hero.http4k.auth.getJwtUser
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.put
import hero.model.NotificationsEnabled
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class NotificationSettingsController(
    private val notificationSettingsQueryService: NotificationSettingsQueryService,
    private val notificationSettingsCommandService: NotificationSettingsCommandService,
) {
    @Suppress("unused")
    val routeGetNotificationSettings: ContractRoute =
        ("/v1/notification-settings").get(
            summary = "Get user notification settings",
            tag = "Notifications",
            parameters = object {},
            responses = listOf(Status.OK example exampleNotificationSettingsResponse),
            handler = { request, _ ->
                val user = request.getJwtUser()

                val settings = notificationSettingsQueryService.execute(GetNotificationSettings(user.id))

                Response(Status.OK).body(settings.toResponse())
            },
        )

    @Suppress("unused")
    val routePutNotificationSettings: ContractRoute =
        ("/v1/notification-settings").put(
            summary = "Update user's notification settings",
            tag = "Notifications",
            parameters = object {},
            receiving = exampleNotificationSettingsUpdateRequest,
            responses = listOf(Status.NO_CONTENT to Unit),
            handler = { request, _ ->
                val user = request.getJwtUser()
                val body = lens<NotificationSettingsUpdateRequest>(request)

                notificationSettingsCommandService.execute(
                    UpdateNotificationSettings(
                        user.id,
                        NotificationsEnabled(
                            emailNewPost = body.emailNewPost,
                            emailNewDm = body.emailNewDm,
                            pushNewComment = body.pushNewComment,
                            pushNewPost = body.pushNewPost,
                            newsletter = body.newsletter,
                            termsChanged = body.termsChanged,
                        ),
                    ),
                )

                Response(Status.NO_CONTENT)
            },
        )
}
