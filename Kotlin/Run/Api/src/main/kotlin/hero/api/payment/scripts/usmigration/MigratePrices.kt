package hero.api.payment.scripts.usmigration

import com.stripe.param.PriceCreateParams
import com.stripe.param.PriceRetrieveParams
import hero.api.payment.scripts.initializeStripeScript
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.retryOn
import hero.baseutils.systemEnv
import hero.gcloud.PubSub
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Currency.EUR
import hero.model.Currency.USD
import hero.stripe.model.StripePrice
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.VatMappingProvider

fun main() {
    val (firestore, _, _, _, clients) = initializeStripeScript(migrationProduction)
    val pubSub = PubSub(migrationEnvironment, SystemEnv.cloudProject)
    val stripeService = StripeService(clients, pubSub)
    val paymentMethodsService = StripePaymentMethodsService(clients, stripeService, pubSub)
    val vatMappingProvider = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD"))
    val subscriptionService =
        StripeSubscriptionService(
            clients,
            paymentMethodsService,
            migrationProduction,
            vatMappingProvider.countryToVatMapping(),
        )
    val pricesCollection = firestore.typedCollectionOf(StripePrice)
    pricesCollection.where(StripePrice::tierId).isGreaterThan("USD")
        .fetchAll()
        .filter { it.euOldStripeId == null }
        .forEach {
            try {
                val price = clients[EUR].prices()
                    .retrieve(it.stripeId, PriceRetrieveParams.builder().addExpand("product").build())
                val product = PriceCreateParams.ProductData.builder()
                    .setName(price.productObject.name)
                    .build()

                val params = PriceCreateParams.builder()
                    .setCurrency(price.currency)
                    .setUnitAmount(price.unitAmount)
                    .setProductData(product)
                    .setRecurring(subscriptionService.createRecurringPeriod(PriceCreateParams.Recurring.Interval.MONTH))
                    .build()

                val newStripePriceId = retryOn(Exception::class) {
                    clients[USD].prices().create(params).id
                }
                println("${it.id} ${it.stripeId} -> $newStripePriceId")
                pricesCollection[it.id].field(StripePrice::euOldStripeId).update(it.stripeId)
                pricesCollection[it.id].field(StripePrice::stripeId).update(newStripePriceId)
            } catch (e: Exception) {
                log.error("Could not copy price ${it.id}: ${e.message}")
            }
        }
}
