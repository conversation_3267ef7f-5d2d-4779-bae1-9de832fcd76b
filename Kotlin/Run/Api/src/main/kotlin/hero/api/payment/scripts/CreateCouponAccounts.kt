package hero.stripe.service

import com.stripe.param.AccountCreateParams
import com.stripe.param.AccountCreateParams.BusinessType
import com.stripe.param.AccountCreateParams.Capabilities
import com.stripe.param.AccountCreateParams.Capabilities.Transfers
import com.stripe.param.AccountCreateParams.Settings.Payments
import com.stripe.param.AccountCreateParams.Settings.Payouts
import com.stripe.param.AccountCreateParams.Settings.Payouts.Schedule
import com.stripe.param.AccountCreateParams.Settings.Payouts.Schedule.Interval.MANUAL
import com.stripe.param.PersonCollectionCreateParams
import com.stripe.param.TokenCreateParams
import hero.api.payment.scripts.initializeStripeScript
import hero.model.Currency
import java.time.Instant

fun czUsdBankAccountToken(production: Boolean): TokenCreateParams.BankAccount =
    TokenCreateParams.BankAccount.builder()
        // https://stripe.com/docs/connect/testing#account-numbers
        .setAccountNumber(if (production) "************************" else "************************")
        .setCurrency("USD")
        .setCountry("CZ")
        .build()

fun czEurBankAccountToken(production: Boolean): TokenCreateParams.BankAccount =
    TokenCreateParams.BankAccount.builder()
        // https://stripe.com/docs/connect/testing#account-numbers
        .setAccountNumber(if (production) "************************" else "************************")
        .setCurrency("EUR")
        .setCountry("CZ")
        .build()

fun main() {
    val currency = Currency.EUR
    val (_, production, _, _, clients) = initializeStripeScript(false)
    val hostname = "https://${if (production) "herohero.co" else "devel.herohero.co"}"

    val accountParams = AccountCreateParams.builder()
        .setCountry("CZ")
        .setType(AccountCreateParams.Type.CUSTOM)
        .setBusinessType(BusinessType.COMPANY)
        .setEmail("admin+coupon-${currency.name.lowercase()}@herohero.co")
        .setCapabilities(
            Capabilities.builder()
                .setTransfers(Transfers.builder().setRequested(true).build())
                .build(),
        )
        .setSettings(
            AccountCreateParams.Settings.builder()
                .setPayments(
                    Payments.builder()
                        .setStatementDescriptor(bankStatementDescriptor("Coupons"))
                        .build(),
                )
                .setPayouts(
                    Payouts.builder()
                        .setSchedule(Schedule.builder().setInterval(MANUAL).build())
                        .build(),
                ).build(),
        )
        .setExternalAccount(
            clients[currency].tokens().create(
                TokenCreateParams.builder()
                    .setBankAccount(
                        when (currency) {
                            Currency.EUR -> czEurBankAccountToken(production)
                            Currency.USD -> czUsdBankAccountToken(production)
                            else -> error("Unknown testing currency: $currency")
                        },
                    )
                    .build(),
            ).id,
        )
        .setTosAcceptance(
            AccountCreateParams.TosAcceptance.builder()
                .setDate(Instant.now().epochSecond)
                .setIp("*************")
                .setServiceAgreement("full")
                .build(),
        )
        .setBusinessProfile(
            AccountCreateParams.BusinessProfile.builder()
                .setName("Herohero s.r.o. coupons account $currency")
                .setUrl("$hostname/coupons")
                .setSupportEmail("<EMAIL>")
                .setSupportPhone("+*********** 565")
                // industry type requirement (not mentioned in API documentation)
                .setMcc("5045")
                .build(),
        )
        .setCompany(
            AccountCreateParams.Company.builder()
                .setName("Herohero s.r.o.")
                .setAddress(
                    AccountCreateParams.Company.Address.builder().setLine1(
                        "Spálená 53",
                    ).setCity("Prague 1").setPostalCode("110 00").build(),
                )
                .setPhone("+*********** 565")
                .setTaxId("********")
                .setVatId("CZ********")
                .setOwnersProvided(true)
                .setDirectorsProvided(true)
                .setExecutivesProvided(true)
                .setVerification(
                    AccountCreateParams.Company.Verification.builder()
                        .setDocument(
                            AccountCreateParams.Company.Verification.Document.builder()
                                // https://stripe.com/docs/connect/testing#test-file-tokens
                                .also { if (!production) it.setFront("file_identity_document_success") }
                                .build(),
                        )
                        .build(),
                )
                .build(),
        )
        .build()

    val personCeo = PersonCollectionCreateParams.builder()
        .setFirstName("Vojtech")
        .setLastName("Otevrel")
        .setEmail("<EMAIL>")
        .setNationality("CZ")
        .also { if (!production) it.setIdNumber("*********") }
        .setDob(
            if (production)
                PersonCollectionCreateParams.Dob.builder().setDay(12).setMonth(4).setYear(1984).build()
            else
                PersonCollectionCreateParams.Dob.builder().setDay(1).setMonth(1).setYear(1901).build(),
        )
        .setRelationship(
            PersonCollectionCreateParams.Relationship.builder()
                .setDirector(true)
                .setRepresentative(true)
                .setExecutive(true)
                .setOwner(true)
                .setTitle("CEO")
                .build(),
        )
        .setAddress(
            PersonCollectionCreateParams.Address.builder()
                .setLine1(if (production) "třída Obránců míru 166" else "address_full_match")
                .setCity(if (production) "Žatec" else "address_full_match")
                .setPostalCode("438 01")
                .build(),
        )
        .setVerification(
            PersonCollectionCreateParams.Verification.builder()
                .setDocument(
                    PersonCollectionCreateParams.Verification.Document.builder()
                        // https://stripe.com/docs/connect/testing#test-file-tokens
                        .also { if (!production) it.setFront("file_identity_document_success") }
                        .build(),
                )
                .build(),
        )
        .build()

    val personCto = PersonCollectionCreateParams.builder()
        .setFirstName("Vojtech")
        .setLastName("Knyttl")
        .setEmail("<EMAIL>")
        .setNationality("CZ")
        .also { if (!production) it.setIdNumber("*********") }
        .setDob(
            if (production)
                PersonCollectionCreateParams.Dob.builder().setDay(24).setMonth(11).setYear(1985).build()
            else
                PersonCollectionCreateParams.Dob.builder().setDay(1).setMonth(1).setYear(1901).build(),
        )
        .setRelationship(
            PersonCollectionCreateParams.Relationship.builder()
                .setDirector(true)
                // account has only one representative
                .setRepresentative(false)
                .setExecutive(true)
                .setOwner(false)
                .setTitle("CTO")
                .build(),
        )
        .setAddress(
            PersonCollectionCreateParams.Address.builder()
                .setLine1(if (production) "Pod Kavalírkou 1322/28" else "address_full_match")
                .setCity(if (production) "Prague 5" else "address_full_match")
                .setPostalCode("150 00")
                .build(),
        )
        .setVerification(
            PersonCollectionCreateParams.Verification.builder()
                .setDocument(
                    PersonCollectionCreateParams.Verification.Document.builder()
                        // https://stripe.com/docs/connect/testing#test-file-tokens
                        .also { if (!production) it.setFront("file_identity_document_success") }
                        .build(),
                )
                .build(),
        )
        .build()

    val account = clients[currency].accounts().create(accountParams)
    account.persons().create(personCeo)
    account.persons().create(personCto)

    println("https://dashboard.stripe.com${if (production) "" else "/test"}/connect/accounts/${account.id}/activity")
}
