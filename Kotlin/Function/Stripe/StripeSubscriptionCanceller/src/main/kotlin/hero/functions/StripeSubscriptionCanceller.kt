package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.get
import hero.model.topics.SubscriptionCancelRequest
import hero.stripe.model.StripeKeys
import hero.stripe.service.CancelSubscriptionAtPeriodEnd
import hero.stripe.service.CancelSubscriptionCommandService
import hero.stripe.service.CancelSubscriptionImmediately
import hero.stripe.service.StripeClients

@Suppress("unused")
class StripeSubscriptionCanceller(
    production: Boolean = SystemEnv.isProduction,
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
) : PubSubSubscriber<SubscriptionCancelRequest>() {
    private val cancelSubscriptionCommandService: CancelSubscriptionCommandService

    init {
        val stripeKeysRepository = TypedCollectionReference<StripeKeys>(firestore.firestore["constants"])
        val keysEu: StripeKeys = stripeKeysRepository["${production.envPrefix}-stripe-eu"].get()
        val keysUs: StripeKeys = stripeKeysRepository["${production.envPrefix}-stripe-us"].get()
        val stripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
        cancelSubscriptionCommandService = CancelSubscriptionCommandService(stripeClients)
    }

    override fun consume(payload: SubscriptionCancelRequest) {
        if (payload.atPeriodEnd) {
            cancelSubscriptionCommandService.execute(
                CancelSubscriptionAtPeriodEnd(
                    subscriptionId = payload.subscriptionId,
                    cancelledBy = payload.cancelledBy,
                    cancelledByRole = payload.cancelledByRole,
                    refundMethod = payload.refundMethod,
                    currency = payload.currency,
                ),
            )
        } else {
            cancelSubscriptionCommandService.execute(
                CancelSubscriptionImmediately(
                    subscriptionId = payload.subscriptionId,
                    cancelledBy = payload.cancelledBy,
                    cancelledByRole = payload.cancelledByRole,
                    refundMethod = payload.refundMethod,
                    currency = payload.currency,
                ),
            )
        }
    }
}
