package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.logging.Logger
import hero.gcloud.FirestoreRef
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Notification
import hero.model.Post
import hero.model.StorageEntityType
import hero.repository.notification.NotificationRepository
import hero.repository.post.PostRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import org.jooq.DSLContext
import org.jooq.exception.IntegrityConstraintViolationException

@Suppress("Unused")
class NotificationPostgresExport(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    systemEnv: EnvironmentVariables = SystemEnv,
    private val firestore: FirestoreRef = firestore(systemEnv.cloudProject, systemEnv.isProduction),
    private val logger: Logger = log,
) : FirestoreEventSubcriber(environmentVariables = systemEnv, retryable = true) {
    private val notificationsCollection = firestore.typedCollectionOf(Notification)
    private val notificationRepository: NotificationRepository = NotificationRepository(lazyContext)
    private val postsCollection = firestore.typedCollectionOf(Post)
    private val postRepository: PostRepository = PostRepository(lazyContext)

    override fun consume(event: FirestoreEvent) {
        val notification = notificationsCollection[event.documentId].fetch()
        if (notification == null) {
            logger.debug("Marking notification with id ${event.documentId} as deleted")
            notificationRepository.delete(event.documentId)
            logger.debug("Done marking notification ${event.documentId} as deleted")
        } else {
            try {
                exportNotification(notification)
            } catch (e: IntegrityConstraintViolationException) {
                val postId = notification.objectId
                if (postId != null && notification.objectType == StorageEntityType.POST) {
                    val post = postsCollection[postId].get()
                    postRepository.save(post)
                    exportNotification(notification)
                } else {
                    throw e
                }
            }
        }
    }

    private fun exportNotification(notification: Notification) {
        logger.debug("Exporting notification ${notification.id} to Postgres")
        notificationRepository.save(notification)
        logger.debug("Done exporting notification ${notification.id} to Postgres")
    }
}
