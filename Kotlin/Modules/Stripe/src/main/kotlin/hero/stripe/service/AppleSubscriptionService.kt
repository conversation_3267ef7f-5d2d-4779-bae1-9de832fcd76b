package hero.stripe.service

import com.github.kittinunf.fuel.core.Response
import com.github.kittinunf.fuel.core.extensions.cUrlString
import com.github.kittinunf.fuel.httpPost
import hero.jackson.toJson
import hero.stripe.model.AppleRefundType
import hero.stripe.model.AppleRequestInfo
import hero.stripe.model.AppleSubscriptionCancelRequest
import hero.stripe.model.AppleSubscriptionRevokeRequest

class AppleSubscriptionService(
    private val signingService: AppleSigningService,
    private val isProduction: Boolean,
    private val storeKitPath: String = if (isProduction) "storekit" else "storekit-sandbox",
) {
    // TODO https://github.com/apple/app-store-server-library-java/issues/169

    // Immediate cancel and refund.
    // https://developer.apple.com/documentation/advancedcommerceapi/revoke-subscription
    fun cancel(
        appleTransactionId: String,
        appleReferenceId: String,
        refundReason: String,
        storefront: String,
    ): Response {
        val payload = AppleSubscriptionRevokeRequest(
            refundReason = refundReason,
            refundRiskingPreference = true,
            refundType = AppleRefundType.FULL,
            requestInfo = AppleRequestInfo(requestReferenceId = appleReferenceId),
            storefront = storefront,
        )
        val signature = signingService.signAppStoreConnect(payload)

        return "https://api.$storeKitPath.itunes.apple.com/advancedCommerce/v1/subscription/revoke/$appleTransactionId"
            .httpPost()
            .header("Authorization", "Bearer $signature")
            .header("Content-Type", "application/json")
            .body(payload.toJson())
            .also { println(it.cUrlString()) }
            .response()
            .second
    }

    // Cancel at a period end.
    // https://developer.apple.com/documentation/advancedcommerceapi/cancel-a-subscription
    fun cancelAtPeriodEnd(
        appleTransactionId: String,
        appleReferenceId: String,
        storefront: String,
    ): Response {
        val payload = AppleSubscriptionCancelRequest(
            requestInfo = AppleRequestInfo(requestReferenceId = appleReferenceId),
            storefront = storefront,
        )
        val signature = signingService.signAppStoreConnect(payload)

        // signing app store requests https://developer.apple.com/documentation/storekit/generating-jws-to-sign-app-store-requests
        return "https://api.$storeKitPath.itunes.apple.com/advancedCommerce/v1/subscription/cancel/$appleTransactionId"
            .httpPost()
            .header("Authorization", "Bearer $signature")
            .header("Content-Type", "application/json")
            .body(payload.toJson())
            .also { println(it.cUrlString()) }
            .response()
            .second
    }
}

fun main() {
    val service = AppleSubscriptionService(AppleSigningService(false), false)

    val respo1 = service.cancel("2000000943274226", "9df55a88-28be-4a3d-ac9d-272caad562ec", "please", "cze")
    println(respo1)

    val respo2 = service.cancelAtPeriodEnd("2000000943274226", "9df55a88-28be-4a3d-ac9d-272caad562ec", "cze")
    println(respo2)
}
