package hero.auth.controller

import hero.auth.constant.IMPERSONATION_TOKEN_DURATION
import hero.baseutils.log
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.http4k.auth.parseJwtUser
import hero.http4k.auth.withDeletedImpersonateTokenCookie
import hero.http4k.auth.withImpersonateTokenCookie
import hero.http4k.extensions.authorization
import hero.http4k.extensions.delete
import hero.http4k.extensions.example
import hero.http4k.extensions.post
import hero.jwt.JwtUser
import hero.jwt.TokenType.IMPERSONATE
import hero.jwt.toJwt
import hero.model.Role
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.string
import java.time.Instant

class ImpersonateController {
    @Suppress("unused")
    val routeImpersonate: ContractRoute =
        ("/v1/users" / Path.string().of("targetUserId") / "impersonate").post(
            // intentionally with no description
            summary = "",
            tag = "Authentication",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            receiving = null,
            handler = { request, _, targetUserId, _ ->
                val user = request.parseJwtUser()
                if (user?.roleIndex != Role.MODERATOR.ordinal) {
                    log.error(
                        "User ${user?.id} does not have right to impersonate $targetUserId.",
                        mapOf("userId" to user?.id),
                    )
                    throw ForbiddenException()
                }
                if (user.id == targetUserId) {
                    throw ConflictException("You cannot impersonate yourself.", labels = mapOf("userId" to user.id))
                }
                val jwt = JwtUser(
                    targetUserId,
                    Instant.now().plus(IMPERSONATION_TOKEN_DURATION).epochSecond,
                    0,
                ).toJwt(type = IMPERSONATE)
                log.info("User ${user.id} is impersonating as $targetUserId.", mapOf("userId" to user.id))
                Response(Status.NO_CONTENT)
                    .withImpersonateTokenCookie(jwt)
            },
        )

    @Suppress("unused")
    val routeStopImpersonate: ContractRoute =
        ("/v1/users/impersonate").delete(
            // intentionally with no description
            summary = "",
            tag = "Authentication",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            handler = { _, _ ->
                Response(Status.NO_CONTENT)
                    .withDeletedImpersonateTokenCookie()
            },
        )
}
