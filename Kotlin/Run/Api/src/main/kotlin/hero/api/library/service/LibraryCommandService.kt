package hero.api.library.service

import hero.api.library.service.dto.SavedPostWithData
import hero.api.post.service.fetchActiveSubscription
import hero.baseutils.log
import hero.core.logging.Logger
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.model.Post
import hero.model.SavedPost
import hero.model.Subscriber
import hero.model.topics.PostState
import java.time.Instant

class LibraryCommandService(
    private val savedPostsCollection: TypedCollectionReference<SavedPost>,
    private val postsCollection: TypedCollectionReference<Post>,
    private val subscribersCollection: TypedCollectionReference<Subscriber>,
    private val logger: Logger = log,
) {
    fun execute(command: AddPostToLibrary): SavedPostWithData {
        val post = postsCollection[command.postId].get()

        if (post.userId != command.userId) {
            val subscriber = subscribersCollection.fetchActiveSubscription(command.userId, post.userId)

            if (subscriber?.status?.isActive != true) {
                throw ForbiddenException("User ${command.userId} does not subscribe ${post.userId}")
            }

            if (post.state != PostState.PUBLISHED) {
                throw BadRequestException("Post ${post.id} is not published yet")
            }
        }

        val savedPost = SavedPost(
            postId = post.id,
            creatorId = post.userId,
            userId = command.userId,
            subscriptionActive = true,
            postPublishedAt = post.published,
            savedAt = Instant.now(),
        ).also {
            savedPostsCollection[it.id].set(it)
        }

        return SavedPostWithData(savedPost, post)
    }

    fun execute(command: RemovePostFromLibrary) {
        val savedPostRef = savedPostsCollection[SavedPost.id(userId = command.userId, postId = command.postId)]
        val savedPost = savedPostRef.get()

        if (savedPost.userId != command.userId) {
            logger.error(
                "User ${command.userId} attempted to modify ${savedPost.userId} library",
                mapOf("userId" to command.userId),
            )
            throw ForbiddenException()
        }

        savedPostRef.delete()
    }
}

data class AddPostToLibrary(val postId: String, val userId: String)

data class RemovePostFromLibrary(val postId: String, val userId: String)
