package hero.auth.controller

import hero.gcloud.PubSub
import hero.model.DiscordCredentials

class DiscordSubscriberOAuth<PERSON>ontroller(
    hostnameServices: String,
    hostname: String,
    servicePath: String,
    credentials: DiscordCredentials,
    pubSub: PubSub,
) : DiscordBaseOAuthController(
        hostnameServices,
        hostname,
        servicePath,
        credentials,
        "subscriber",
        listOf("email", "identify", "guilds.join"),
        pubSub,
    )
