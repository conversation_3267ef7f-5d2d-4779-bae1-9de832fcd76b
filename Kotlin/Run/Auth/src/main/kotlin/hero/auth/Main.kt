package hero.auth

import hero.auth.controller.DiscordCreatorOAuthController
import hero.auth.controller.DiscordSubscriberOAuthController
import hero.auth.controller.FacebookController
import hero.auth.controller.FirebaseAuthController
import hero.auth.controller.ImpersonateController
import hero.auth.controller.InternalFirebaseAuthController
import hero.auth.controller.LogoutController
import hero.auth.controller.RefreshController
import hero.auth.controller.SessionsController
import hero.auth.controller.SpotifyOAuthController
import hero.auth.service.FacebookService
import hero.auth.service.RefreshTokenCommandService
import hero.auth.service.SessionCommandService
import hero.auth.service.SessionQueryService
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.baseutils.systemEnv
import hero.gcloud.PubSub
import hero.gcloud.fetchSingle
import hero.gcloud.firestore
import hero.gcloud.get
import hero.gcloud.typedCollectionOf
import hero.http4k.http4kInJetty
import hero.model.DiscordCredentials
import hero.model.SpotifyCredentials
import hero.model.User
import hero.repository.session.SessionRepository
import hero.spotify.SpotifyOpenClient
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import org.jooq.DSLContext

fun main() {
    val environment = SystemEnv.environment
    log.info("Service is starting in $environment")
    val hostname = SystemEnv.hostname
    val hostnameServices = SystemEnv.hostnameServices
    val isLocalHost = systemEnv("LOG_APPENDER") != "ConsoleFluentD"
    val servicePath = "/${systemEnv("SERVICE_NAME")}"
    val projectId = SystemEnv.cloudProject
    val production = SystemEnv.isProduction
    val firestore = firestore(projectId, SystemEnv.isProduction)
    val pubSub = PubSub(environment, projectId)

    val lazyContext = lazyContext(environment)
    val sessionRepository = SessionRepository(lazyContext)
    val discordCredentials = firestore.firestore["constants"]["oauth-discord"].fetchSingle<DiscordCredentials>()!!
    val spotifyCredentials = firestore.firestore["constants"]["oauth-spotify"].fetchSingle<SpotifyCredentials>()!!
    val usersCollection = firestore.typedCollectionOf(User)
    val refreshTokenCommandService = RefreshTokenCommandService(sessionRepository, usersCollection, lazyContext)
    val facebookService = FacebookService(systemEnv("FACEBOOK_SECRET"))
    val spotifyOpenClient =
        SpotifyOpenClient(spotifyCredentials.appId, spotifyCredentials.appSecret, spotifyCredentials.partnerId)

    val typedUsersCollection = firestore.typedCollectionOf(User)
    val firebaseAuth = initFirebaseAuth(projectId, production.envPrefix)
    val sessionCommandService = SessionCommandService(sessionRepository, lazyContext)
    http4kInJetty(
        "Herohero Auth service",
        production,
        isLocalHost,
        listOf(
            DiscordCreatorOAuthController(
                hostnameServices = hostnameServices,
                hostname = hostname,
                servicePath = servicePath,
                credentials = discordCredentials,
                pubSub = pubSub,
            ),
            DiscordSubscriberOAuthController(
                hostnameServices = hostnameServices,
                hostname = hostname,
                servicePath = servicePath,
                credentials = discordCredentials,
                pubSub = pubSub,
            ),
            SpotifyOAuthController(
                hostnameServices = hostnameServices,
                hostname = hostname,
                servicePath = servicePath,
                credentials = spotifyCredentials,
                spotifyOpenClient = spotifyOpenClient,
                usersCollection = usersCollection,
            ),
            FacebookController(facebookService),
            FirebaseAuthController(
                refreshTokenCommandService = refreshTokenCommandService,
                sessionCommandService = sessionCommandService,
                firebaseAuth = firebaseAuth,
                usersCollection = typedUsersCollection,
                pubSub = pubSub,
                hostname = SystemEnv.hostname,
                production = SystemEnv.isProduction,
            ),
            ImpersonateController(),
            LogoutController(
                sessionCommandService = sessionCommandService,
            ),
            RefreshController(
                refreshTokenCommandService = refreshTokenCommandService,
            ),
            SessionsController(
                sessionsQueryService = SessionQueryService(sessionRepository, lazyContext),
                sessionCommandService = sessionCommandService,
            ),
            InternalFirebaseAuthController(
                firebaseAuth = firebaseAuth,
            ),
        ),
    )
}

private fun lazyContext(environment: String): Lazy<DSLContext> {
    // if environment is not local, we have to trigger the lazy loading, so we know if a configuration is wrong
    // on the start
    try {
        ConnectorConnectionPool.dataSource
    } catch (e: Exception) {
        if (environment != "local") {
            throw e
        } else {
            log.warn("Failed to load data source, ignoring as run in local env: ${e.message}")
        }
    }

    return lazy {
        JooqSQL.context(ConnectorConnectionPool.dataSource)
    }
}
