package hero.api.payment.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.randomString
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Currency
import hero.model.Tier
import hero.stripe.model.StripePrice
import hero.stripe.service.StripeService

fun main() {
    val isProduction = true
    val (_, _, _, _, clients) = initializeStripeScript(true)
    val stripeService = StripeService(clients, null)
    val firestore = firestore(SystemEnv.cloudProject, true)
    val stripePricesCollection = firestore.typedCollectionOf(StripePrice)
    val oldCouponId = "OCXOMKSNKS"
    val currency = Currency.EUR

    val oldCoupon = clients[currency].coupons().retrieve(oldCouponId) ?: error("Could not find coupon")
    val tierId = oldCoupon.metadata["tierId"] ?: error("Missing tierId")
    val creatorId = oldCoupon.metadata["creatorId"] ?: error("Missing creatorId")
    val purchaseByUserId = oldCoupon.metadata["purchasedByUserId"] ?: error("Missing purchaseByUserId")
    val months = oldCoupon.metadata["months"]?.toInt()?.minus(1)
    val days = oldCoupon.metadata["days"]?.toInt()

    fun generateCouponId(): String {
        while (true) {
            val couponId = randomString(10).uppercase()
            // check if coupon with given id exists and return its value if not
            stripeService.getCouponOrNull(couponId, currency)
                ?: return couponId
        }
    }

    val stripePriceId = stripePricesCollection["$creatorId|$tierId"].get().stripeId
    val price = clients[currency].prices().retrieve(stripePriceId)
    println(stripePriceId)

    val newCoupon = stripeService.createCoupon(
        purchasedByUserId = purchaseByUserId,
        couponId = generateCouponId(),
        creatorId = creatorId,
        tier = Tier.ofId(tierId),
        price = price,
        months = months,
        days = days,
        extraMetadata = mapOf("duplicateOf" to oldCouponId),
        percentOff = oldCoupon.percentOff?.toInt(),
        campaign = "Duplicate removal",
        currency = currency,
    )
    println(newCoupon)
}
