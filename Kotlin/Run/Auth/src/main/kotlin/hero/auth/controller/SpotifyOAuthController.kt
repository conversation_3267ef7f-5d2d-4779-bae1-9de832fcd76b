package hero.auth.controller

import com.github.kittinunf.fuel.httpGet
import dev.forkhandles.result4k.mapFailure
import dev.forkhandles.result4k.resultFrom
import hero.auth.model.AccessTokenDetails
import hero.baseutils.FuelException
import hero.baseutils.fetch
import hero.baseutils.log
import hero.baseutils.serviceCall
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.HttpStatusException
import hero.exceptions.http.UnauthorizedException
import hero.gcloud.TypedCollectionReference
import hero.http4k.auth.parseJwtUser
import hero.http4k.extensions.example
import hero.http4k.extensions.post
import hero.jackson.fromJson
import hero.jwt.ACCESS_TOKEN
import hero.jwt.authorization
import hero.model.ExtractedUser
import hero.model.OAuthProvider.SPOTIFY
import hero.model.SpotifyCredentials
import hero.model.SubscriptionsDtoResponse
import hero.model.User
import hero.spotify.SpotifyOpenClient
import org.http4k.contract.ContractRoute
import org.http4k.core.Credentials
import org.http4k.core.HttpHandler
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.core.Uri
import org.http4k.security.AccessTokenResponse
import org.http4k.security.OAuthCallbackError
import org.http4k.security.OAuthPersistence
import org.http4k.security.OAuthProvider
import org.http4k.security.OAuthProviderConfig
import org.http4k.security.openid.IdToken
import java.time.Instant
import org.http4k.security.AccessTokenDetails as AccessTokenDetailsHttp4k

class SpotifyOAuthController(
    hostnameServices: String,
    hostname: String,
    servicePath: String,
    credentials: SpotifyCredentials,
    private val spotifyOpenClient: SpotifyOpenClient,
    private val usersCollection: TypedCollectionReference<User>,
) : OAuthController(
        hostnameServices = hostnameServices,
        hostname = hostname,
        servicePath = servicePath,
        oAuthCredentials = credentials,
        service = "spotify",
        domainPattern = ".*\\.spotify.com",
    ) {
    override fun provide(
        client: HttpHandler,
        credentials: Credentials,
        tokenPersistence: OAuthPersistence,
        callbackUri: Uri,
    ): OAuthProvider =
        OAuthProvider(
            providerConfig = OAuthProviderConfig(
                authBase = Uri.of("https://accounts.spotify.com"),
                authPath = "/authorize",
                tokenPath = "/api/token",
                credentials = credentials,
                apiBase = Uri.of("https://accounts.spotify.com"),
            ),
            client = client,
            callbackUri = callbackUri,
            scopes = listOf("user-soa-link", "user-library-modify"),
            oAuthPersistence = tokenPersistence,
            accessTokenExtractor = { msg ->
                resultFrom {
                    val result = msg.bodyString().fromJson<AccessTokenResponse>()
                    val id = result.id_token?.let { IdToken(it) }
                    AccessTokenDetailsHttp4k(
                        result.toAccessToken(),
                        idToken = id,
                    )
                }.mapFailure {
                    OAuthCallbackError.CouldNotFetchAccessToken(msg.status, msg.bodyString())
                }
            },
        )

    override fun handleAccessToken(
        tokenDetails: AccessTokenDetails,
        tokenDetailsMap: Map<String, Any?>,
        request: Request,
    ): Pair<ExtractedUser, String?> {
        val oAuthUser = try {
            "https://api.spotify.com/v1/me".httpGet()
                .header("Authorization", "Bearer ${tokenDetails.accessToken.value}")
                .fetch<Map<String, Any?>>()
        } catch (e: FuelException) {
            when (e.status) {
                Status.UNAUTHORIZED.code -> throw UnauthorizedException(e.message)
                Status.FORBIDDEN.code -> throw ForbiddenException(e.message)
                else -> throw e
            }
        }

        val imageUrl = (oAuthUser["images"] as? List<Map<String, Any>>)
            ?.maxByOrNull { it["width"] as Int }
            ?.get("url") as String?

        val userId = tokenDetailsMap["userId"] as String

        val extractedUser = ExtractedUser(
            id = oAuthUser["id"] as String,
            secondaryId = null,
            userId = userId,
            provider = SPOTIFY,
            name = oAuthUser["display_name"] as String,
            email = null,
            imageUrl = imageUrl,
            accessToken = tokenDetails.accessToken.value,
            refreshToken = tokenDetails.accessToken.refreshToken?.value,
            tokenExpiresAt = tokenDetails.accessToken.expiresIn?.let { Instant.now().plusSeconds(it) },
        )

        try {
            // we try to remove previous links to the user, so that it can be correctly recreated
            spotifyOpenClient.unlinkUser(userId)
        } catch (e: HttpStatusException) {
            // we don't care at this point
        }
        // now we fetch current users subscriptions to be passed to spotify
        val creatorIds = serviceCall("api", "/v2/users/$userId/subscriptions?of=user")
            .httpGet()
            .header("Cookie", "$ACCESS_TOKEN=${userId.authorization()}")
            .fetch<SubscriptionsDtoResponse>()
            .subscriptions
            .map { it.relationships.creator.id }

        val registerResponse = spotifyOpenClient.registerUser(userId, tokenDetails.accessToken.value, creatorIds)

        // now user has to be redirected to the completionUrl
        return extractedUser to registerResponse.completionUrl
    }

    @Suppress("unused")
    val routeUnlink: ContractRoute
        get() = "/$service/v1/unlink".post(
            summary = "Unlinks user from Spotify.",
            tag = service.capitalize(),
            parameters = object {},
            responses = listOf(Status.NO_CONTENT example Unit),
            receiving = null,
            handler = { request, parameters ->
                val userId = request.parseJwtUser()?.id ?: throw UnauthorizedException()
                log.info("User $userId is being unlinked from Spotify.", properties = mapOf("userId" to userId))
                spotifyOpenClient.unlinkUser(userId)
                usersCollection[userId].field(User::spotify).update(null)
                Response(status = Status.NO_CONTENT)
            },
        )
}
