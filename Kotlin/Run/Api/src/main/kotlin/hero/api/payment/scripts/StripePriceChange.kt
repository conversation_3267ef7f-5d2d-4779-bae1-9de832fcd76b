package hero.api.payment.scripts

import com.stripe.param.SubscriptionItemUpdateParams
import com.stripe.param.SubscriptionSearchParams
import com.stripe.param.SubscriptionUpdateParams
import hero.baseutils.log
import hero.baseutils.zoneEuPrg
import hero.gcloud.typedCollectionOf
import hero.model.Currency
import hero.model.Tier
import hero.stripe.model.StripePrice
import java.time.Instant
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

fun main() {
    val creatorId = "luigibusinessofficialdmlrmcfc"
    // WARN you cannot migrate USD->EUR or back because of Stripe limitations
    //      – only same currency is allowed
    val oldTier = "EUR06"
    val newTier = "EUR05"
    val (firestore, _, _, _, clients) = initializeStripeScript(true)
    val prices = firestore.typedCollectionOf(StripePrice)
    val oldPrice = prices["$creatorId|$oldTier"].get().stripeId
    val newPrice = prices["$creatorId|$newTier"].get().stripeId

    val client = clients[Currency.EUR]
    val executor = Executors.newFixedThreadPool(20)

    client.subscriptions()
        .search(
            SubscriptionSearchParams.builder().setQuery(
                "metadata['creatorId']:'$creatorId' AND status:'active'",
            ).build(),
        )
        .autoPagingIterable()
        .forEach {
            if (it.items.data.size != 1) {
                error("Subscription ${it.id} has more payment items.")
            }
            val item = it.items.data.first()
            if (item.price.id != oldPrice) {
                return@forEach
            }
            if (it.status != "active") {
                println("${it.id} is not active: ${it.status}")
                return@forEach
            }

            val coupon = it.discount?.coupon
            if (coupon != null) {
                val ends = it.discount.end?.let { Instant.ofEpochSecond(it).atZone(zoneEuPrg).toLocalDate() }
                println(
                    "Subscription ${it.id} has a coupon ${coupon.percentOff}%/${coupon.amountOff} " +
                        "for ${coupon.durationInMonths}, ends $ends skipping for now.",
                )
                return@forEach
            }
            if (item.discounts.isNotEmpty()) {
                error("Subscription ${it.id} has a discount ${it.items.data}")
            }

            executor.submit {
                println(
                    "Handling sub ${it.id} $oldPrice -> $newPrice" +
                        if (it.cancelAtPeriodEnd) " (cancelsAtPeriodEnd)" else "",
                )
                try {
                    it.update(
                        SubscriptionUpdateParams.builder()
                            .putMetadata("tierId", newTier)
                            .putMetadata("priceCents", Tier.ofId(newTier).priceCents.toString())
                            .build(),
                    )
                    item.update(
                        SubscriptionItemUpdateParams.builder()
                            .setPrice(newPrice)
                            .setProrationBehavior(SubscriptionItemUpdateParams.ProrationBehavior.NONE)
                            .build(),
                    )
                } catch (e: Exception) {
                    log.error(e.message, cause = e)
                }
            }

            /*
            if (coupon != null) {
                val months = coupon.durationInMonths -
                    (Period.between(Instant.now().atZone(zoneEuPrg).toLocalDate(), Instant.ofEpochSecond(it.created).atZone(zoneEuPrg).toLocalDate()).months + 1)
                println("Coupon ${coupon.id} was created for ${coupon.durationInMonths}, recreating for ${months}")
                client.coupons().create(
                CouponCreateParams.builder()
                    .setPercentOff(coupon.percentOff)
                    .setAmountOff(coupon.amountOff)
                    .setDurationInMonths(months)
                    .build()
                )
            }
             */
        }
    executor.shutdown()
    executor.awaitTermination(Long.MAX_VALUE, TimeUnit.HOURS)
}
