package hero.functions

import com.google.cloud.functions.HttpFunction
import com.google.cloud.functions.HttpRequest
import com.google.cloud.functions.HttpResponse
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.jackson.jackson
import hero.jackson.map
import hero.jackson.to
import hero.jwt.parseJwt
import hero.model.NotificationDisableRequest
import hero.model.NotificationType
import hero.model.NotificationsEnabled
import hero.model.User
import java.net.HttpURLConnection.HTTP_BAD_REQUEST
import java.net.HttpURLConnection.HTTP_OK
import kotlin.reflect.KMutableProperty1

class NotificationDisabler(
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, SystemEnv.isProduction),
    private val hostname: String = SystemEnv.hostname,
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
) : HttpFunction {
    override fun service(
        request: HttpRequest,
        response: HttpResponse,
    ) {
        val writer = response.writer
        val disableRequest = request.queryParameters
            ?.get("request")
            ?.firstOrNull()
            ?.toString()
            ?.parseJwt()
            ?.to<NotificationDisableRequest>()

        val enabled = request.getFirstQueryParameter("enabled").get().toBoolean()

        if (disableRequest == null) {
            writer.write(response(null, HTTP_BAD_REQUEST, enabled, null))
            return
        }

        log.info("User is disabling notification of type ${disableRequest.notificationType}.", disableRequest.map())

        when (disableRequest.notificationType) {
            NotificationType.NEW_POST -> {
                disableNotificationForNewPost(disableRequest.userId, enabled, NotificationsEnabled::emailNewPost)
            }
            NotificationType.NEWSLETTER -> {
                disableNotificationForNewPost(disableRequest.userId, enabled, NotificationsEnabled::newsletter)
            }
            NotificationType.TERMS_CHANGED -> {
                disableNotificationForNewPost(disableRequest.userId, enabled, NotificationsEnabled::termsChanged)
            }
            else -> {
                log.debug("${disableRequest.notificationType} cannot yet be disabled.", disableRequest.map())
                return
            }
        }

        writer.write(
            response(
                disableRequest.userId,
                HTTP_OK,
                enabled,
                // we use jackson to conform to @JsonProperty naming for output
                jackson.convertValue(disableRequest.notificationType, String::class.java),
            ),
        )
    }

    private fun response(
        userId: String?,
        status: Int,
        enabled: Boolean,
        notificationType: String?,
    ) = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8"/>
          <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
          <meta name="referrer" content="no-referrer"/>
          <title>Herohero - Disabling notification…</title>
          <script>
              window.location.href = '$hostname/${userId ?: ""}?status=$status&${if (enabled) "subscribed" else "unsubscribed"}=${notificationType ?: ""}';
          </script>
        </head>
        <body>
          Notification disabled. Feel free to close this window.
        </body>
        </html>
        """.trimIndent()

    internal fun disableNotificationForNewPost(
        userId: String,
        enabled: Boolean,
        notificationTypePath: KMutableProperty1<NotificationsEnabled, Boolean>,
    ) {
        usersCollection[userId]
            .field(root(User::notificationsEnabled).path(notificationTypePath))
            .update(enabled)
    }
}
