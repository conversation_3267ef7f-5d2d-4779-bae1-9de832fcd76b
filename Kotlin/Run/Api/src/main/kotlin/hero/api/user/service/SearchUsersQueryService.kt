package hero.api.user.service

import hero.core.data.Page
import hero.core.data.Pageable
import hero.model.User
import hero.model.UserStatus
import hero.repository.subscription.JooqSubscriptionHelper
import hero.repository.user.JooqUserHelper
import hero.sql.jooq.Tables.NAME_HISTOGRAM
import hero.sql.jooq.Tables.NOTIFICATION_SETTINGS
import hero.sql.jooq.Tables.SUBSCRIPTION
import hero.sql.jooq.Tables.USER
import org.jooq.DSLContext
import org.jooq.impl.DSL

class SearchUsersQueryService(
    lazyContext: Lazy<DSLContext>,
) {
    private val context by lazyContext

    /**
     * Searches for users based on a fuzzy match of the query string against their names, prioritizing
     * subscribed creators and relevance. The search is optimized for both performance and result quality.
     *
     * ## Optimizations:
     *  **Split into two branches using `UNION ALL`**:
     *   - Keeps logic clean and avoids a deeply nested query.
     *   - Separates handling of subscribed and non-subscribed users,
     *   allowing each branch to be optimized independently.
     *   - attempting to sort subscribed creators as first and then order by subscriber count in a single query
     *   makes it very hard for PostgreSQL to use the `pg_trgm` index effectively, resulting in slow performance.
     *
     * **Conditional ordering strategy** for performance:
     *   - If the number of matches (`count`) is less than the page size, sort by `<->` distance for relevance,
     *   without the order by, it's very slow since
     *   - If not, skip the expensive distance sort and fall back to sorting by `subscribers_count` to reduce overhead.
     *   - This avoids unnecessary cost for common search terms that match many users.
     */
    fun execute(query: SearchUsers): Page<User> {
        if (query.query.isBlank()) {
            return Page.emptyPage()
        }

        val wordSimilarityCondition = DSL.condition(
            "immutable_unaccent({0}) <% immutable_unaccent({1})",
            query.query,
            DSL.field("name"),
        )
        val distanceOrder = DSL.field(
            "immutable_unaccent({0}) <-> immutable_unaccent({1})",
            Double::class.java,
            USER.NAME,
            query.query,
        )

        val count = context.select(DSL.sum(NAME_HISTOGRAM.COUNT))
            .from(NAME_HISTOGRAM)
            .where(wordSimilarityCondition)
            .fetchSingle()
            .value1()
            ?.toInt()
            ?: 0

        val subscribedCreatorsCte = DSL.name("subscribed_creators")
            .`as`(
                DSL.select(USER.ID)
                    .from(SUBSCRIPTION)
                    .join(USER)
                    .on(USER.ID.eq(SUBSCRIPTION.CREATOR_ID)).and(wordSimilarityCondition)
                    .where(SUBSCRIPTION.USER_ID.eq(query.userId).and(JooqSubscriptionHelper.activeSubscription))
                    .orderBy(USER.SUBSCRIBERS_COUNT.desc())
                    .limit(query.pageable.pageSize),
            )

        val subQuery = DSL
            .select(JooqUserHelper.userFields)
            .from(USER)
            .join(NOTIFICATION_SETTINGS)
            .on(NOTIFICATION_SETTINGS.USER_ID.eq(USER.ID))
            .where(USER.STATUS.eq(UserStatus.ACTIVE.name))
            .and(
                USER.ID.notIn(
                    DSL.select(subscribedCreatorsCte.field(USER.ID)).from(subscribedCreatorsCte),
                ),
            )
            .let {
                // this is an arbitrary number based on experiments
                if (count > 200) {
                    it
                } else {
                    it.orderBy(distanceOrder)
                }
            }

        val users = context
            .with(subscribedCreatorsCte)
            .select(JooqUserHelper.userFields)
            .from(subscribedCreatorsCte)
            .join(USER).on(USER.ID.eq(subscribedCreatorsCte.field(USER.ID)))
            .join(NOTIFICATION_SETTINGS)
            .on(NOTIFICATION_SETTINGS.USER_ID.eq(USER.ID))
            .unionAll(
                context
                    .select()
                    .from(subQuery)
                    .where(wordSimilarityCondition)
                    .orderBy(subQuery.field(USER.SUBSCRIBERS_COUNT)?.desc())
                    .limit(query.pageable.pageSize),
            )
            .limit(query.pageable.pageSize)
            .fetch()
            .map { JooqUserHelper.mapRecordToEntity(it) }

        return Page(users, query.pageable, false)
    }
}

data class SearchUsers(val query: String, val userId: String?, val pageable: Pageable)
