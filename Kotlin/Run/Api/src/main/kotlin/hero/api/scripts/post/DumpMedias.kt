package hero.api.scripts.post

import hero.baseutils.SystemEnv
import hero.baseutils.systemEnv
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.gjirafa.GjirafaUploadsService
import hero.model.Post
import hero.model.topics.PostState
import kotlin.concurrent.thread

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)
    val postsCollection = firestore.typedCollectionOf(Post)
    val gjirafaProject = if (production) systemEnv("GJIRAFA_PROJECT_PROD") else SystemEnv.gjirafaProject
    val gjirafaApiKey = if (production) systemEnv("GJIRAFA_API_KEY_PROD") else SystemEnv.gjirafaApiKey
    val gjirafaImageKey = SystemEnv.gjirafaImageKey
    val gjirafaService = GjirafaUploadsService(gjirafaProject, gjirafaApiKey, gjirafaImageKey)

    val creatorId = "creatorId"

    val posts = postsCollection
        .where(Post::userId).isEqualTo(creatorId)
        .fetchAll()
        .filter { it.state != PostState.REVISION }
        .filter { it.messageThreadId == null }
        .filter { it.assets.any { it.gjirafa != null } }
        .sortedBy { it.created }

    posts.forEach {
        println(it.created.toString() + "\t" + it.text.replace("\\s+".toRegex(), " ").trim())
        it.assets
            .filter { it.gjirafa != null }
            .forEach {
                thread {
                    try {
                        gjirafaService.setPermissions(2, it.gjirafa!!.id)
                    } catch (e: Exception) {
                        println("- ${it.gjirafa!!.id}: ${e.message}")
                    }
                }
                val asset = gjirafaService.getAsset(userId = null, assetId = it.gjirafa!!.id, withDebug = true)
                val uploadId = asset.debugDetail!!.originalFile!!.replace("[?].*".toRegex(), "")
                val uploadUrl = if (uploadId.startsWith("https://")) {
                    uploadId
                } else {
                    val upload = gjirafaService.getUpload(uploadId)
                    upload.result.filePath
                }
                println(" - ${it.gjirafa!!.id} $uploadUrl")
            }
        println()
    }
}
