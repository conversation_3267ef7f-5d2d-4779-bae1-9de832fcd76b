package hero.functions

import com.github.kittinunf.fuel.core.extensions.authentication
import com.github.kittinunf.fuel.httpGet
import com.github.kittinunf.fuel.httpPost
import hero.baseutils.FuelException
import hero.baseutils.SystemEnv
import hero.baseutils.fetch
import hero.baseutils.log
import hero.jackson.jackson
import hero.jackson.map
import hero.model.topics.EmailPublished

class MailgunPublisher : PubSubSubscriber<EmailPublished>() {
    private val environmentRaw = SystemEnv.environment

    // We have only prod/devel environment for emails. All non-prod envs should be send as devel.
    private val environment = if (environmentRaw == "prod") "prod" else "devel"
    private val domain = "mg.herohero.co"
    private val apiKey = "**************************************************"
    private val mailgunApi = "https://api.eu.mailgun.net/v3/$domain"

    override fun consume(payload: EmailPublished) {
        // We currently support only languages listed below, see templates here:
        // https://gitlab.com/heroheroco/mailgun-templates/-/tree/main/templates
        val language = when (payload.language?.lowercase()) {
            "cs" -> "cs"
            "de" -> "de"
            "es" -> "es"
            "sk" -> "sk"
            else -> "en"
        }
        val tag = "$environment-$language"
        val subject = payload.variables.toMap()["subject"]?.toString()
            ?: subject(payload.template, tag, payload.variables.toMap())
        val variablesWithUtm = mapUtmParams(payload.variables, payload.template)

        val response = try {
            "$mailgunApi/messages"
                .httpPost(
                    EmailPayload(
                        from = if (payload.from != null)
                            "\"${payload.from} (via Herohero)\" <info@$domain>"
                        else
                            "Herohero <info@$domain>",
                        to = payload.to,
                        template = payload.template,
                        tag = tag,
                        subject = subject,
                        variables = jackson.writeValueAsString(variablesWithUtm),
                    ).serialize(),
                )
                .authentication().basic("api", apiKey)
                .header()
                .fetch<EmailRequestResponse>()
        } catch (e: FuelException) {
            // email might be refused by mailgun despite the InternetAddress validation above
            EmailRequestResponse("", e.message)
        }

        log.info("Email to ${payload.to} (subject: $subject) was published: ${response.map()}")
    }

    private val subjectsCache: MutableMap<String, String> = mutableMapOf()
    internal val subjectMatcher = "<title>(.+?)</title>".toRegex()

    // we need to attach utm parameters as described in https://linear.app/herohero/issue/HH-1839/
    internal fun mapUtmParams(
        variables: List<Pair<String, Any?>>,
        template: String,
    ): Map<String, Any?> {
        val utm = "utm_source=mailgun&utm_medium=email&utm_campaign=$template"

        return variables
            .plus("utm" to utm)
            .associate { (key, value) ->
                if (value is String && value.matches("https://[a-z.]*herohero.co/.*".toRegex())) {
                    if (value.contains("?")) {
                        key to "$value&$utm"
                    } else {
                        key to "$value?$utm"
                    }
                } else {
                    key to value
                }
            }
    }

    internal fun subject(
        templateName: String,
        tag: String,
        variables: Map<String, Any?>,
    ): String {
        val subjectRaw = subjectsCache.getOrPut("$templateName/$tag") {
            val templateResponse = "$mailgunApi/templates/$templateName/versions/$tag"
                .httpGet()
                .authentication().basic("api", apiKey)
                .fetch<MailgunTemplateResponse>()
                .template

            if (templateResponse.version == null) {
                error("Template $templateName/$tag response was missing `version` field: $templateResponse")
            }

            val template = templateResponse.version.template

            subjectMatcher
                .find(template)
                ?.groups
                ?.get(1)
                ?.value
                ?.trim()
                ?: error("Template $template/$tag does not contain <title> block to generate subject.")
        }

        // replace values in localized subject, eg. {{creator-name}} -> Jane Austen
        // WARN this does not expand {{#if}}, {{else}}, {{/if}} blocks
        return subjectRaw.replace("\\{\\{([a-zA-Z0-9-]+)\\}\\}".toRegex()) {
            val variable = it.groups[1]!!.value
            variables[variable]?.toString() ?: variable
        }
    }
}

data class EmailRequestResponse(
    val id: String,
    val message: String,
)

data class EmailPayload(
    val from: String,
    val to: String,
    val template: String,
    val tag: String,
    val subject: String,
    val variables: String,
) {
    // duplication of the FROM header to the output JSON
    val sender: String
        get() = from

    // https://documentation.mailgun.com/en/latest/user_manual.html#templates
    fun serialize(): List<Pair<String, Any?>> =
        map().filter { it.key !in setOf("variables", "tag") }
            .map { it.key to it.value } +
            listOf(
                "h:X-Mailgun-Variables" to variables,
                "t:version" to tag,
            )
}

data class MailgunTemplateResponse(
    val template: MailgunTemplate,
)

data class MailgunTemplate(
    val createdAt: String,
    val description: String,
    val name: String,
    val version: MailgunTemplateVersion?,
)

data class MailgunTemplateVersion(
    val createdAt: String,
    val engine: String,
    val tag: String,
    val template: String,
    val comment: String,
)
