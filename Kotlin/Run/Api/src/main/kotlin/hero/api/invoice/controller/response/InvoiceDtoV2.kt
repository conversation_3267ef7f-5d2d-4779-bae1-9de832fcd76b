package hero.api.invoice.controller.response

import hero.model.InvoiceDto
import hero.model.UserDto

data class InvoicesDtoResponse(
    val meta: InvoicesDtoMeta? = null,
    val invoices: List<InvoiceDto>,
    val included: InvoicesDtoIncluded,
)

data class InvoicesDtoIncluded(
    val users: List<UserDto>,
)

data class InvoicesDtoMeta(
    val hasNext: <PERSON>olean,
    val scrollAfter: String?,
)
