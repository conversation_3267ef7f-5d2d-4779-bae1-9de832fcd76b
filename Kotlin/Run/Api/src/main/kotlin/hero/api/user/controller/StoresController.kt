package hero.api.user.controller

import hero.api.user.controller.dto.UpdateAssetUserTimestampRequest
import hero.api.user.controller.dto.exampleUpdateAssetUserTimestampRequest
import hero.api.user.service.UpdateAssetTimestamp
import hero.api.user.service.UserMediaCommandService
import hero.api.watch.service.RecordWatchActivity
import hero.api.watch.service.RemoveWatchActivity
import hero.api.watch.service.WatchActivityCommandService
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.UnauthorizedException
import hero.gcloud.TypedCollectionReference
import hero.http4k.auth.getJwtUser
import hero.http4k.auth.parseJwtUser
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.put
import hero.model.UserStore
import hero.model.UserStoreAttributes
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.regex
import java.time.Instant

class StoresController(
    private val collection: TypedCollectionReference<UserStore>,
    private val userMediaCommandService: UserMediaCommandService,
    private val watchActivityCommandService: WatchActivityCommandService,
) {
    @Suppress("unused")
    val routeUserStore: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "stores" / Path.regex("([a-z0-9-]+)").of("storeId")).get(
            summary = "Get user store by given id.",
            tag = "Store",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(
                Status.OK example UserStore(
                    id = "search",
                    attributes = UserStoreAttributes(mapOf("foo" to "bar"), Instant.now(), "v20210908"),
                    relationships = mapOf("many" to "any"),
                ),
            ),
            handler = { request, _, userId, _, storeId ->
                val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
                if (jwtUser.id != userId) {
                    throw ForbiddenException()
                }
                val store = collection["$userId-$storeId"].fetch()
                    ?: UserStore(storeId, UserStoreAttributes(emptyMap(), Instant.now(), null))
                Response(Status.OK).body(store)
            },
        )

    @Suppress("unused")
    val routePutUserStore: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "stores" / Path.regex("([a-z0-9-]+)").of("storeId")).put(
            summary = "Put to user store by given id.",
            tag = "Store",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(
                Status.OK example UserStore(
                    id = "search",
                    attributes = UserStoreAttributes(mapOf("foo" to "bar"), Instant.now(), "v20210908"),
                    relationships = mapOf("many" to "any"),
                ),
            ),
            receiving = UserStore(
                id = "search",
                attributes = UserStoreAttributes(mapOf("foo" to "bar"), Instant.now(), "v20210908"),
                relationships = mapOf("many" to "any"),
            ),
            handler = { request, _, userId, _, storeId ->
                val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
                if (jwtUser.id != userId) {
                    throw ForbiddenException()
                }

                val body = lens<UserStore>(request)
                    .let { it.copy(attributes = it.attributes.copy(updatedAt = Instant.now())) }

                if (body.attributes.content.keys.any { it.isBlank() }) {
                    throw BadRequestException("Content key cannot be blank or empty")
                }

                if (body.id != storeId) {
                    throw BadRequestException("Entity id ${body.id} does not correspond to id $storeId in URL.")
                }
                collection["$userId-$storeId"].set(body)
                Response(Status.OK).body(body)
            },
        )

    @Suppress("unused")
    val routePutAssetTimestamp =
        ("/v1/stores/media").put(
            summary = "Update authenticated user's media store",
            tag = "Store",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.OK to Unit),
            receiving = exampleUpdateAssetUserTimestampRequest,
            handler = { request, _ ->
                val jwtUser = request.getJwtUser(allowImpersonation = false)
                val body = lens<UpdateAssetUserTimestampRequest>(request)

                if (body.timestamp == null) {
                    watchActivityCommandService.execute(
                        RemoveWatchActivity(
                            userId = jwtUser.id,
                            assetId = body.assetId,
                            postId = body.postId!!,
                        ),
                    )
                    userMediaCommandService.execute(
                        UpdateAssetTimestamp(
                            userId = jwtUser.id,
                            assetId = body.assetId,
                            timestamp = 0.0,
                            postId = body.postId,
                        ),
                    )
                } else {
                    if (body.postId != null) {
                        userMediaCommandService.execute(
                            UpdateAssetTimestamp(
                                userId = jwtUser.id,
                                assetId = body.assetId,
                                timestamp = body.timestamp,
                                postId = body.postId,
                            ),
                        )
                    }

                    watchActivityCommandService.execute(
                        RecordWatchActivity(
                            userId = jwtUser.id,
                            // TODO remove when session is no longer nullable
                            sessionId = jwtUser.sessionId!!,
                            assetId = body.assetId,
                            timestamp = body.timestamp,
                            postId = body.postId,
                        ),
                    )
                }

                Response(Status.NO_CONTENT)
            },
        )
}
